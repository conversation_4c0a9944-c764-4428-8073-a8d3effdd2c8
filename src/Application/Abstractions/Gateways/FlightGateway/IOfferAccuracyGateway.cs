using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.Abstractions.Gateways.FlightGateway;

public interface IOfferAccuracyGateway
{
    public Task<HashSet<string>> GetUnavailableFlightOffers(string[] flightIds, Occupancy occupancy,
        CancellationToken cancellationToken = default);

    public Task<IEnumerable<string>> GetUnavailableHotelRoomOffers(int provider, int hotelMetaCode,
        DateOnly checkinDate, DateOnly checkoutDate, CancellationToken cancellationToken = default);
}