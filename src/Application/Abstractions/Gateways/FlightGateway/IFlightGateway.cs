using Esky.Packages.Application.Dtos.FlightOffers;

namespace Esky.Packages.Application.Abstractions.Gateways.FlightGateway;

public interface IFlightGateway
{
    Task<IEnumerable<FlightOfferDto>> GetFlightOffers(
        FlightSearchCriteria criteria,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<FlightOfferDto>> GetAlternativeFlightOffers(
        AlternativeFlightOffersCriteria criteria,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<FlightOfferDto>> GetFlightsByOfferKeyAsync(
        FlightByOfferKeySearchCriteria criteria,
        CancellationToken cancellationToken);
}