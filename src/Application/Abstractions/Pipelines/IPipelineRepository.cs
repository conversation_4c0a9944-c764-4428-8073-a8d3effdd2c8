namespace Esky.Packages.Application.Abstractions.Pipelines;

public interface IPipelineRepository
{
    Task UpsertWithoutConcurrency(List<Pipeline> pipelines, CancellationToken cancellationToken = default);
    Task<Pipeline?> GetById(string id, CancellationToken cancellationToken = default);
    Task MarkForRetry(string id, string startedByUserId, CancellationToken cancellationToken = default);
    Task MarkAsRunning(string id, CancellationToken cancellationToken = default);
    Task MarkAsCompleted(string id, CancellationToken cancellationToken = default);
    Task MarkAsFailed(string id, string errorMessage, CancellationToken cancellationToken = default);
    Task RemoveOutdatedPipelines(string definitionId, CancellationToken cancellationToken = default);
    Task<List<GenerationAggregatedStatistics>> GenerationsAggregatedStatistics(CancellationToken cancellationToken = default);
    Task<GenerationDetailedStatistics?> GenerationDetailedStatistics(string definitionId, CancellationToken cancellationToken = default);
    Task<List<DefinitionFailedPipelines>> GetAllFailedPipelinesByDefinitionId(CancellationToken cancellationToken = default);
}