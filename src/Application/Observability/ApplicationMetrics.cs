using System.Collections.Concurrent;

namespace Esky.Packages.Application.Observability;

public static class ApplicationMetrics
{
    public static long GeneratedPackageFlights => _generatedPackageFlights;
    public static long UpsertedPackageFlights => _upsertedPackageFlights;
    public static long GeneratedPackageHotelOffers => _generatedPackageHotelOffers;
    public static long UpsertedPackageHotelOffers => _upsertedPackageHotelOffers;
    public static IDictionary<string, long> GenerationStepDuration => _generationStepDuration;
    public static IDictionary<string, long> GenerationStepErrors => _generationStepErrors;
    public static long PackageVariantSearchOffers => _packageVariantSearchOffers;
    public static long PackageFactorsSearchOffers => _packageFactorsSearchOffers;
    public static long PackageDynamicSearchOffers => _packageDynamicSearchOffers;

    private static long _generatedPackageFlights;
    private static long _upsertedPackageFlights;
    private static long _generatedPackageHotelOffers;
    private static long _upsertedPackageHotelOffers;
    private static readonly ConcurrentDictionary<string, long> _generationStepDuration = new();
    private static readonly ConcurrentDictionary<string, long> _generationStepErrors = new();
    private static long _packageVariantSearchOffers;
    private static long _packageFactorsSearchOffers;
    private static long _packageDynamicSearchOffers;
    
    
    public static void RegisterGeneratedPackageFlights(long generatedPackageFlights, long upsertedPackageFlights)
    {
        Interlocked.Add(ref _generatedPackageFlights, generatedPackageFlights);
        Interlocked.Add(ref _upsertedPackageFlights, upsertedPackageFlights);
    }
    
    public static void RegisterGeneratedPackageHotelOffers(long generatedPackageHotelOffers, long upsertedPackageHotelOffers)
    {
        Interlocked.Add(ref _generatedPackageHotelOffers, generatedPackageHotelOffers);
        Interlocked.Add(ref _upsertedPackageHotelOffers, upsertedPackageHotelOffers);
    }
    
    public static void RegisterGenerationStepDuration(string stepName, long duration)
    {
        _generationStepDuration.AddOrUpdate(stepName, duration, (_, oldValue) => oldValue + duration);
    }
    
    public static void RegisterGenerationStepError(string stepName, int errorCount = 1)
    {
        _generationStepErrors.AddOrUpdate(stepName, errorCount, (_, oldValue) => oldValue + errorCount);
    }
    
    public static void RegisterPackageVariantSearchOffers(long offers)
    {
        Interlocked.Add(ref _packageVariantSearchOffers, offers);
    }
    
    public static void RegisterPackageFactorsSearchOffers(long offers)
    {
        Interlocked.Add(ref _packageFactorsSearchOffers, offers);
    }
    
    public static void RegisterPackageDynamicSearchOffers(long offers)
    {
        Interlocked.Add(ref _packageDynamicSearchOffers, offers);
    }
}