using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.Dtos.HotelOffers;

public static class HotelOfferDtoExtension
{
    public static bool HasSupportedOccupancy(this HotelOfferDto dto)
    {
        return HasSupportedOccupancy(dto.Occupancy);
    }
    
    public static bool HasSupportedOccupancy(this HotelOfferIdDto dto)
    {
        return HasSupportedOccupancy(dto.Occupancy);
    }

    private static bool HasSupportedOccupancy(HotelOfferOccupancy hotelOfferOccupancy)
    {
        // TODO: Refactor this - move this logic somewhere else
        var occupancy = new Occupancy(hotelOfferOccupancy.Adults, hotelOfferOccupancy.Children);

        return PackageOccupancy.IsSupportedInPrecomputedPackage(occupancy);
    }
    
    public static HotelOfferQuote ToDomainQuote(this HotelOfferDto dto)
    {
        return new HotelOfferQuote
        {
            MetaCode = dto.MetaCode,
            CheckIn = dto.CheckIn,
            StayLength = dto.StayLength,
            ProviderConfigurationId = dto.ProviderConfigurationId,
            Occupancy = ToDomain(dto.Occupancy),
            Prices = ToDomain(dto.Prices),
            UpdatedAt = DateTime.UtcNow
        };
    }
    
    private static PackageOccupancy ToDomain(HotelOfferOccupancy dto)
    {
        // TODO: Refactor this - move this logic somewhere else
        var occupancy = new Occupancy(dto.Adults, dto.Children);

        return (PackageOccupancy)PackageOccupancy.MapExact(occupancy, true)!;
    }

    private static MealPlan ToDomain(HotelOfferDto.HotelOfferMealPlanDto dto)
    {
        return dto switch
        {
            HotelOfferDto.HotelOfferMealPlanDto.None => MealPlan.None,
            HotelOfferDto.HotelOfferMealPlanDto.Breakfast => MealPlan.Breakfast,
            HotelOfferDto.HotelOfferMealPlanDto.HalfBoard => MealPlan.HalfBoard,
            HotelOfferDto.HotelOfferMealPlanDto.FullBoard => MealPlan.FullBoard,
            HotelOfferDto.HotelOfferMealPlanDto.AllInclusive => MealPlan.AllInclusive,
            _ => throw new ArgumentOutOfRangeException(nameof(dto), dto, message: null)
        };
    }

    private static Refundability ToDomain(HotelOfferDto.HotelOfferRefundabilityDto dto)
    {
        return dto switch
        {
            HotelOfferDto.HotelOfferRefundabilityDto.Refundable => Refundability.Refundable,
            HotelOfferDto.HotelOfferRefundabilityDto.NonRefundable => Refundability.NonRefundable,
            _ => throw new ArgumentOutOfRangeException(nameof(dto), dto, message: null)
        };
    }
    
    private static Money ToDomain(HotelOfferDto.HotelOfferMoneyDto dto)
    {
        return new Money(dto.Value, dto.Currency);
    }
    

    private static Dictionary<MealPlan, Dictionary<Refundability, Money>> ToDomain(
        Dictionary<HotelOfferDto.HotelOfferMealPlanDto, Dictionary<HotelOfferDto.HotelOfferRefundabilityDto, HotelOfferDto.HotelOfferMoneyDto>> prices)
    {
        return prices.SelectMany(mealPlanEntry => mealPlanEntry.Value.Select(refundabilityEntry => (
                MealPlan: ToDomain(mealPlanEntry.Key), 
                Refundability: ToDomain(refundabilityEntry.Key),
                Money: ToDomain(refundabilityEntry.Value))))
            .GroupBy(x => x.MealPlan)
            .ToDictionary(g => g.Key, g => g
                .GroupBy(x => x.Refundability)
                .ToDictionary(g2 => g2.Key, g2 => g2.First().Money));
    }
}