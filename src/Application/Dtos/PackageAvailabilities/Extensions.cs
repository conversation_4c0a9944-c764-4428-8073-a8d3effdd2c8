using Esky.Packages.Application.Dtos.Common;
using Esky.Packages.Contract.PackageAvailabilities;
using Esky.Packages.Domain.Model.PackageAvailabilities;

namespace Esky.Packages.Application.Dtos.PackageAvailabilities;

public static class Extensions
{
    public static PackageAvailabilityDto ToDto(this PackageAvailability packageAvailability)
    {
        return new PackageAvailabilityDto(
            MetaCode: packageAvailability.MetaCode,
            Occupancies: packageAvailability.Occupancies.Select(x => x.ToOccupancy().ToDto()).ToArray(),
            MealPlans: packageAvailability.MealPlans.Select(x => x.ToDto()).ToArray(),
            DepartureAirports: packageAvailability.DepartureAirports.Select(x => x.ToString()).ToArray(),
            StayLengths: packageAvailability.StayLengths,
            LowestTotalPrice: packageAvailability.LowestTotalPrice,
            LowestPricesPerStayLength: packageAvailability.LowestPricesPerStayLength,
            MinCheckIn: packageAvailability.MinCheckIn,
            MaxCheckIn: packageAvailability.MaxCheckIn
        );
    }
}
