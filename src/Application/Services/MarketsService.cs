using Esky.Packages.Application.Exceptions;
using Esky.Packages.Contract.Markets;
using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.Services;

public interface IMarketsService
{
    Task<MarketDto> Create(MarketDto marketDto, CancellationToken cancellationToken = default);
    Task<MarketDto> Upsert(string marketId, MarketDto marketDto, CancellationToken cancellationToken = default);
}

internal class MarketsService(IMarketRepository marketRepository) : IMarketsService
{
    public async Task<MarketDto> Create(MarketDto marketDto, CancellationToken cancellationToken = default)
    {
        var market = Market.Create(
            marketDto.Id,
            new Currency(marketDto.Currency),
            marketDto.PartnerCode,
            marketDto.DepartureAirports.Select(a => new Airport(a)).ToArray()
        );

        await marketRepository.Add(market, cancellationToken);
        
        return new MarketDto(
            market.Id,
            market.Currency.ToString(),
            market.PartnerCode,
            market.DepartureAirports.Select(a => a.ToString()).ToList()
        );
    }

    public async Task<MarketDto> Upsert(string marketId, MarketDto marketDto, CancellationToken cancellationToken = default)
    {
        if (marketId != marketDto.Id)
        {
            throw new InvalidOperationException("Market id cannot be changed.");
        }

        var newMarket = Market.Create(
            marketDto.Id,
            new Currency(marketDto.Currency),
            marketDto.PartnerCode,
            marketDto.DepartureAirports.Select(a => new Airport(a)).ToArray()
        );
        
        var market = await marketRepository.GetById(marketId, cancellationToken);
        if (market is null)
        {
            market = newMarket;
        }
        else
        {
            market.Update(newMarket);
        }

        await marketRepository.Upsert(market, cancellationToken);
        
        return new MarketDto(
            market.Id,
            market.Currency.ToString(),
            market.PartnerCode,
            market.DepartureAirports.Select(a => a.ToString()).ToList()
        );
    }
}