using Esky.Packages.Application.Abstractions.Gateways.FlightGateway;
using Esky.Packages.Application.Abstractions.Gateways.FlightLiveGateway;
using Esky.Packages.Application.Abstractions.Gateways.HotelTransactionGateway;
using Esky.Packages.Application.Dtos.Common;
using Esky.Packages.Application.Dtos.FlightOffers;
using Esky.Packages.Application.Dtos.HotelOfferVariants;
using Esky.Packages.Application.Exceptions;
using Esky.Packages.Contract.Common;
using Esky.Packages.Contract.PackageVariants;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.LiveVariants;
using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Model.PackageDefinitions;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.Services;

public record PackageLiveVariantsParameters(
    string PackageId,
    Occupancy[] Occupancies,
    string[]? DepartureAirports,
    string? FlightOptionId,
    string? PreferredDepartureAirport,
    TimeOfDay[]? InboundDepartures,
    TimeOfDay[]? OutboundDepartures,
    bool PreferSelectedFlight);

public interface IPackageLiveVariantsService
{
    Task<PackageLiveVariantsDto> GetPackageLiveVariants(
        PackageLiveVariantsParameters parameters,
        CancellationToken cancellationToken);
}

internal class PackageLiveVariantsService(
    IPackageHotelOfferRepository packageHotelOfferRepository,
    IPackageFlightRepository packageFlightRepository,
    IPackageDefinitionRepository packageDefinitionRepository,
    IPackageHotelAirportsRepository packageHotelAirportsRepository,
    IMarketRepository marketRepository,
    IFlightGateway flightGateway,
    IFlightLiveGateway flightLiveGateway,
    IFlightVariantFactory flightVariantFactory,
    IHotelTransactionGateway hotelTransactionGateway,
    IHotelOfferVariantFactory hotelOfferVariantFactory,
    IOfferAccuracyGateway offerAccuracyGateway,
    ILogger<PackageLiveVariantsService> logger)
    : IPackageLiveVariantsService
{
    public async Task<PackageLiveVariantsDto> GetPackageLiveVariants(
        PackageLiveVariantsParameters parameters,
        CancellationToken cancellationToken)
    {
        var packageId = PackageId.IsOldPackageId(parameters.PackageId) 
            ? PackageId.ParseFromOldPackageId(parameters.PackageId) 
            : new PackageId(parameters.PackageId);
        
        var packageHotelOffer = await GetPackageHotelOffer(packageId, cancellationToken);
        if (packageHotelOffer != null)
        {
            var packageDefinitionTask = packageDefinitionRepository.GetById(packageHotelOffer.DefinitionId, cancellationToken);
            var packageFlightsTask = GetPackageFlights(packageHotelOffer, cancellationToken);
            
            await Task.WhenAll(packageFlightsTask, packageDefinitionTask);

            var packageFlights = packageFlightsTask.Result;
            var packageDefinition = packageDefinitionTask.Result ?? throw new PackageDefinitionNotFoundException(packageHotelOffer.DefinitionId);

            var flightOffers = packageFlights
                .Where(x => parameters.PreferredDepartureAirport is null ||
                            x.Id.DepartureAirport == parameters.PreferredDepartureAirport)
                .Where(x => parameters.DepartureAirports is null ||
                            parameters.DepartureAirports.Contains(x.Id.DepartureAirport.ToString()))
                .SelectMany(x => x.FlightOffers
                    .Where(o => parameters.InboundDepartures is null || parameters.InboundDepartures.Length == 0 ||
                                parameters.InboundDepartures.Contains(o.InboundDeparture))
                    .Where(o => parameters.OutboundDepartures is null || parameters.OutboundDepartures.Length == 0 ||
                                parameters.OutboundDepartures.Contains(o.OutboundDeparture)));
            
            // todo mb filter precomputed flights - not here
            {
                
            }
            var package = Package.Create(
                id: packageId,
                packageHotelOffer: packageHotelOffer,
                packageFlights: packageFlights);

            return await GetPackageLiveVariants(
                package,
                packageHotelOffer,
                packageDefinition,
                parameters,
                flightOffers,
                cancellationToken);
        }
        else
        {
            var market = await marketRepository.GetById(packageId.MarketId, cancellationToken)
                         ?? throw new MarketNotFoundException(packageId.MarketId);

            return await GetPackageLiveVariantsWithoutCompensation(
                packageId,
                market,
                parameters,
                cancellationToken);
        }
    }
    
    private async Task<List<PackageHotelOfferDto>> GetLiveHotelOfferDtos(
        Package package,
        PackageHotelOffer packageHotelOffer,
        PackageDefinition packageDefinition,
        Occupancy[] occupancies,
        CancellationToken cancellationToken)
    {
        var hotelOfferVariants = await GetHotelOfferVariants(
            package,
            packageHotelOffer,
            packageDefinition,
            occupancies,
            cancellationToken);

        var primaryOccupancy = occupancies[0];
        var packageOccupancy = PackageOccupancy.Map(primaryOccupancy);

        var cheapestPackageHotelOffersPerMealPlan = package
            .GetLowestPricesPerOccupancyAndMealPlan()
            .Where(x => x.Occupancy == packageOccupancy)
            .ToDictionary(x => x.MealPlan, x => x.Price);

        return GetLiveHotelOfferDtosFromVariants(hotelOfferVariants, cheapestPackageHotelOffersPerMealPlan);
    }
    
    private List<PackageHotelOfferDto> GetLiveHotelOfferDtosFromVariants(
        HotelOfferLiveVariants hotelOfferVariants,
        Dictionary<MealPlan, decimal>? cheapestPackageHotelOffersPerMealPlan)
    {
        var liveHotelOffers = new List<PackageHotelOfferDto>();
        
        foreach (var (mealPlan, hotelVariantsForMealPlan) in hotelOfferVariants.GetHotelOffersByMealPlan())
        {
            var cheapestPackageHotelOfferForMealPlan = cheapestPackageHotelOffersPerMealPlan?.TryGetValue(mealPlan, out var price) == true
                ? (decimal?)price
                : null;

            liveHotelOffers.AddRange(CreateMealPlanHotelOfferDtos(hotelVariantsForMealPlan, cheapestPackageHotelOfferForMealPlan));
        }

        return liveHotelOffers;
    }
    
    private async Task<IEnumerable<PackageFlightOfferDto>> GetLiveFlightOffersDtos(
        Package package,
        PackageDefinition packageDefinition,
        Currency currency,
        Occupancy[] occupancies,
        PackageLiveVariantsParameters parameters,
        IEnumerable<FlightOffer> flightOffers,
        CancellationToken cancellationToken)
    {
        var flightVariants = await GetFlightVariants(
            package,
            packageDefinition,
            currency,
            occupancies,
            parameters,
            flightOffers,
            cancellationToken);
        
        var packageOccupancy = PackageOccupancy.Map(occupancies[0]);
        
        var cheapestPackageFlightsForAirports = package
            .GetLowestPricesPerDepartureAirportAndOccupancy() //todo here we use the lowest prices not filtered by inbound/outbound TimeOfDay
            .Where(x => x.Occupancy == packageOccupancy)
            .ToDictionary(x => x.DepartureAirport.ToString(), x => x.PriceEntry.Price);

        return GetLiveFlightOffersDtosFromVariants(flightVariants, cheapestPackageFlightsForAirports);
    }
    
    private static IEnumerable<PackageFlightOfferDto> GetLiveFlightOffersDtosFromVariants(
        FlightLiveVariants flightVariants,
        Dictionary<string, decimal>? cheapestPackageFlightsForAirports)
    {
        var liveFlightOffersDtos = new List<PackageFlightOfferDto>();
        
        foreach (var (departureAirport, alternativeFlightsForAirport) in flightVariants.GetFlightsByDepartureAirport())
        {
            var cheapestPackageFlightForAirport = cheapestPackageFlightsForAirports?.TryGetValue(departureAirport, out var price) == true
                ? (decimal?)price
                : null;

            liveFlightOffersDtos.AddRange(CreateAirportFlightOfferDtos(alternativeFlightsForAirport, cheapestPackageFlightForAirport));
        }

        return liveFlightOffersDtos;
    }

    private async Task<FlightLiveVariants> GetFlightVariants(
        Package package,
        PackageDefinition packageDefinition,
        Currency currency,
        Occupancy[] occupancies,
        PackageLiveVariantsParameters parameters,
        IEnumerable<FlightOffer> flightOffers,
        CancellationToken cancellationToken)
    {
        var combinedOccupancy = CombineOccupancies(occupancies);

        //todo here we use the lowest prices not filtered by inbound/outbound TimeOfDay
        var departureAirports = package.FlightPricesByOccupancyByDepartureByArrival
            .SelectMany(arrival => arrival.Value.Select(departure => (Airport)departure.Key.ToString()))
            .Distinct()
            .ToArray();
        var arrivalAirports = package.FlightPricesByOccupancyByDepartureByArrival
            .Select(arrival => (Airport)arrival.Key.ToString())
            .ToArray();

        return await GetFlightLiveVariants(package.Id,
            parameters: parameters,
            combinedOccupancy: combinedOccupancy,
            partnerCode: packageDefinition.Parameters.PartnerCode,
            currency: currency,
            departureAirports: departureAirports,
            arrivalAirports: arrivalAirports,
            maxStops: packageDefinition.Parameters.FlightCriteria.MaxStop,
            maxLegDuration: packageDefinition.Parameters.FlightCriteria.MaxLegDuration,
            flightIds: flightOffers.Select(x => x.Id).ToHashSet(),
            cancellationToken: cancellationToken);
    }

    private async Task<FlightLiveVariant?> GetSelectedFlightLiveVariant(
        string partnerCode,
        Currency currency,
        Occupancy combinedOccupancy,
        string flightOptionId,
        CancellationToken cancellationToken)
    {
        var flightOffer = (await flightGateway.GetFlightsByOfferKeyAsync(new FlightByOfferKeySearchCriteria
                (
                    FlightOfferKeys: [flightOptionId],
                    PartnerCode: partnerCode,
                    Occupancy: combinedOccupancy,
                    IncludeFlightDetails: true
                ), cancellationToken)
            ).FirstOrDefault();

        if (flightOffer is null) return null;

        var selectedFlightVariant = flightVariantFactory.Create(
            key: flightOffer.Key,
            departureAirport: flightOffer.DepartureAirport,
            arrivalAirport: flightOffer.ArrivalAirport,
            departureDate: flightOffer.DepartureDate,
            arrivalDate: flightOffer.ArrivalDate,
            returnDepartureDate: flightOffer.ReturnDepartureDate,
            returnArrivalDate: flightOffer.ReturnArrivalDate,
            providerCode: flightOffer.ProviderCode,
            stops: flightOffer.Stops,
            airlineCodes: flightOffer.AirlineCodes,
            flightIds: flightOffer.FlightIds,
            legLocators: flightOffer.LegLocators ?? [],
            currency: currency,
            prices: flightOffer.Prices.Select(p =>
                new Money(p.Value.PaxConfigurations.First().Value.TotalPrice, p.Value.Currency)).ToArray(),
            registeredBaggageIncluded: flightOffer.BaggageIncluded);

        var liveCheck = await flightLiveGateway.LiveCheck(
            new LiveCheckCriteria(
                FlightOfferKey: selectedFlightVariant.Key,
                PartnerCode: partnerCode,
                CurrencyCode: currency,
                Occupancy: combinedOccupancy),
            cancellationToken);

        if (liveCheck is null || liveCheck.Status is not FlightLiveCheckStatusDto.Available and not FlightLiveCheckStatusDto.Timeout) return null;

        selectedFlightVariant.MarkAsSelected();

        if (liveCheck.Price is not null)
        {
            selectedFlightVariant.UpdateLivePrice(
                price: new Money(liveCheck.Price.Value, liveCheck.Price.Currency),
                legLocators: liveCheck.LegLocators);
        }

        return selectedFlightVariant;
    }

    private async Task LiveCheckFlightVariant(
        string partnerCode,
        Currency currency,
        PackageLiveVariantsParameters parameters,
        Occupancy combinedOccupancy,
        FlightLiveVariants flightLiveVariants,
        CancellationToken cancellationToken)
    {
        var selectedFlightVariant = flightLiveVariants.SelectFlight(
            selectedDepartureAirports: parameters.DepartureAirports,
            selectedFlightOptionId: parameters.FlightOptionId,
            preferredDepartureAirport: parameters.PreferredDepartureAirport);

        if (selectedFlightVariant is not null)
        {
            selectedFlightVariant.MarkAsSelected();

            var liveCheck = await flightLiveGateway.LiveCheck(
                new LiveCheckCriteria(
                    FlightOfferKey: selectedFlightVariant.Key,
                    PartnerCode: partnerCode,
                    CurrencyCode: currency,
                    Occupancy: combinedOccupancy),
                cancellationToken);

            if (liveCheck is not null)
            {
                if (liveCheck.Status is FlightLiveCheckStatusDto.NotAvailable)
                {
                    flightLiveVariants.RemoveSoldOutFlight(selectedFlightVariant);
                }
                else if (liveCheck.Status is FlightLiveCheckStatusDto.Available && liveCheck.Price is not null)
                {
                    selectedFlightVariant.UpdateLivePrice(
                        price: new Money(liveCheck.Price.Value, liveCheck.Price.Currency),
                        legLocators: liveCheck.LegLocators);
                }
                else
                {
                    logger.LogWarning("Live check failed for flight {flightOfferKey} with status {status}",
                        selectedFlightVariant.Key,
                        liveCheck.Status);
                }
            }
        }
    }
    
    private async Task<(Airport[] DepartureAirports, Airport[] ArrivalAirports)> GetDepartureAndArrivalAirports(
        PackageId packageId,
        Market market,
        CancellationToken cancellationToken)
    {
        var packageHotelAirports = await packageHotelAirportsRepository.ListByMarketIdAndMetaCodes(
            packageId.MarketId,
            [packageId.MetaCode],
            cancellationToken);

        var arrivalAirportsList = packageHotelAirports
                .SelectMany(m => m.Airports)
                .Distinct()
                .ToArray();
        
        return (market.DepartureAirports.ToArray(), arrivalAirportsList);
    }

    private async Task<PackageHotelOffer?> GetPackageHotelOffer(PackageId packageId, CancellationToken cancellationToken)
    {
        var packageHotelOfferId = new PackageHotelOfferId(
            checkIn: packageId.CheckIn,
            stayLength: packageId.StayLength,
            marketId: packageId.MarketId,
            metaCode: packageId.MetaCode);

        return await packageHotelOfferRepository.GetById(packageHotelOfferId, cancellationToken);
    }

    private async Task<List<PackageFlight>> GetPackageFlights(
        PackageHotelOffer packageHotelOffer,
        CancellationToken cancellationToken)
    {
        var packageFlightOfferIds = packageHotelOffer
            .GetPackageFlightIds()
            .ToList();
        
        var packageFlights = await packageFlightRepository.ListByIds(packageFlightOfferIds, cancellationToken);
        if (packageFlights.Count == 0)
        {
            throw new PackageFlightNotFoundException(packageFlightOfferIds);
        }

        return packageFlights;
    }

    private async Task<PackageLiveVariantsDto> GetPackageLiveVariants(
        Package package,
        PackageHotelOffer packageHotelOffer,
        PackageDefinition packageDefinition,
        PackageLiveVariantsParameters parameters,
        IEnumerable<FlightOffer> flightOffers,
        CancellationToken cancellationToken)
    {
        var currency = packageHotelOffer.Currency;

        var liveFlightsDtosTask = GetLiveFlightOffersDtos(
            package,
            packageDefinition,
            currency,
            parameters.Occupancies,
            parameters,
            flightOffers,
            cancellationToken);

        var liveHotelOffersDtosTask = GetLiveHotelOfferDtos(
            package,
            packageHotelOffer,
            packageDefinition,
            parameters.Occupancies,
            cancellationToken);

        await Task.WhenAll(liveFlightsDtosTask, liveHotelOffersDtosTask);

        return new PackageLiveVariantsDto(
            Id: package.Id.ToString(),
            Occupancy: parameters.Occupancies.First().ToDto(),
            Occupancies: parameters.Occupancies.Select(o => o.ToDto()).ToArray(),
            CheckIn: package.Id.CheckIn,
            StayLength: package.Id.StayLength,
            MetaCode: package.Id.MetaCode,
            DefinitionId: packageDefinition.Id,
            MarketId: package.Id.MarketId,
            PartnerCode: packageDefinition.Parameters.PartnerCode,
            Currency: currency,
            FlightOffers: liveFlightsDtosTask.Result.ToArray(),
            HotelOffers: liveHotelOffersDtosTask.Result.ToArray());
    }

    private static IEnumerable<PackageFlightOfferDto> CreateAirportFlightOfferDtos(
        List<FlightLiveVariant> alternativeFlightsForAirport,
        decimal? cheapestPackagePriceForAirport)
    {
        for (var i = 0; i < alternativeFlightsForAirport.Count; i++)
        {
            var alternativeFlight = alternativeFlightsForAirport[i];
            var priceCompensated = (cheapestPackagePriceForAirport, i) switch
            {
                (null or 0m, _) => alternativeFlight.Compensate(alternativeFlight.Price),
                (_, 0) => alternativeFlight.Compensate(cheapestPackagePriceForAirport),
                _ => alternativeFlight.Compensate(cheapestPackagePriceForAirport, onlyUp: true)
            };

            yield return new PackageFlightOfferDto(
                Id: alternativeFlight.Key,
                Price: alternativeFlight.Price,
                PriceCompensated: priceCompensated,
                DepartureAirport: alternativeFlight.DepartureAirport,
                ArrivalAirport: alternativeFlight.ArrivalAirport,
                DepartureDate: alternativeFlight.DepartureDate.ToDateOnly(),
                ArrivalDate: alternativeFlight.ArrivalDate.ToDateOnly(),
                ReturnDepartureDate: alternativeFlight.ReturnDepartureDate.ToDateOnly(),
                ReturnArrivalDate: alternativeFlight.ReturnArrivalDate.ToDateOnly(),
                Stops: alternativeFlight.Stops,
                FlightIds: alternativeFlight.FlightIds,
                LegLocators: alternativeFlight.LegLocators,
                RegisteredBaggageIncluded: alternativeFlight.RegisteredBaggageIncluded,
                IsSelected: alternativeFlight.IsSelected);
        }
    }

    private async Task<HotelOfferLiveVariants> GetHotelOfferVariants(
        Package package,
        PackageHotelOffer packageHotelOffer,
        PackageDefinition packageDefinition,
        Occupancy[] occupancies,
        CancellationToken cancellationToken)
    {
        var variantsGroupedByRoomsDtos = await hotelTransactionGateway.GetVariantsGroupedByRooms(
            new HotelOfferVariantsCritieria(
                CheckIn: package.Id.CheckIn,
                CheckOut: package.Id.CheckIn.AddDays(package.Id.StayLength),
                PartnerCode: packageDefinition.Parameters.PartnerCode,
                MetaCode: package.Id.MetaCode,
                Occupancies: occupancies
            ),
            cancellationToken);

        var hotelOfferVariantsGroupedByRooms = variantsGroupedByRoomsDtos
            .Select(x => x
                .ToDictionary(
                    p => p.Key,
                    p => p.Value
                        .Select(o => hotelOfferVariantFactory
                            .Create(
                                offerId: o.OfferId,
                                mealPlan: o.MealPlan.ToDomain(),
                                refundability: o.Refundability.ToDomain(),
                                freeRefundUntil: o.FreeRefundUntil,
                                currency: packageHotelOffer.Currency,
                                price: new Money(o.Price.Value, o.Price.Currency),
                                priceAtHotel: new Money(o.PriceAtHotel.Value, o.PriceAtHotel.Currency),
                                availability: o.Availability))
                        .ToList()))
            .ToList();

        return HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms: hotelOfferVariantsGroupedByRooms,
            onlyRefundable: packageHotelOffer.OnlyRefundable);
    }

    private static IEnumerable<PackageHotelOfferDto> CreateMealPlanHotelOfferDtos(
        List<HotelOfferLiveVariant> hotelOfferVariantsForMealPlan,
        decimal? cheapestPackageHotelOfferForMealPlan)
    {
        for (var i = 0; i < hotelOfferVariantsForMealPlan.Count; i++)
        {
            var hotelOfferVariant = hotelOfferVariantsForMealPlan[i];
            var priceCompensated = (cheapestPackageHotelOfferForMealPlan, i) switch
            {
                (null or 0m, _) => hotelOfferVariant.Compensate(hotelOfferVariant.Price),
                (_, 0) => hotelOfferVariant.Compensate(cheapestPackageHotelOfferForMealPlan),
                _ => hotelOfferVariant.Compensate(cheapestPackageHotelOfferForMealPlan, onlyUp: true)
            };

            yield return new PackageHotelOfferDto(
                OfferId: hotelOfferVariant.OfferId,
                MealPlan: hotelOfferVariant.MealPlan.ToDto(),
                Availability: hotelOfferVariant.Availability,
                Price: hotelOfferVariant.Price,
                PriceCompensated: priceCompensated,
                PriceAtHotel: new MoneyDto(
                    Value: hotelOfferVariant.PriceAtHotel.Value,
                    Currency: hotelOfferVariant.PriceAtHotel.Currency),
                FreeRefund: hotelOfferVariant.Refundability.IsRefundable,
                FreeRefundUntil: hotelOfferVariant.FreeRefundUntil
            );
        }
    }
    
    private async Task<PackageLiveVariantsDto> GetPackageLiveVariantsWithoutCompensation(
        PackageId packageId,
        Market market,
        PackageLiveVariantsParameters parameters,
        CancellationToken cancellationToken)
    {
        var currency = market.Currency;
        var partnerCode = market.PartnerCode;
        
        var liveFlightsDtosTask = GetLiveFlightOffersDtosWithoutCompensation(
            packageId,
            market,
            currency,
            parameters.Occupancies,
            parameters,
            partnerCode,
            cancellationToken);
        
        var liveHotelOffersDtosTask = GetLiveHotelOffersDtosWithoutCompensation(
            packageId,
            currency,
            partnerCode,
            parameters.Occupancies,
            cancellationToken);
        
        await Task.WhenAll(liveFlightsDtosTask, liveHotelOffersDtosTask);
        
        return new PackageLiveVariantsDto(
            Id: packageId.ToString(),
            Occupancy: parameters.Occupancies.First().ToDto(), // TODO: remove after multiroom is implemented in all clients
            Occupancies: parameters.Occupancies.Select(o => o.ToDto()).ToArray(),
            CheckIn: packageId.CheckIn,
            StayLength: packageId.StayLength,
            MetaCode: packageId.MetaCode,
            DefinitionId: null,
            MarketId: packageId.MarketId,
            PartnerCode: partnerCode,
            Currency: currency,
            FlightOffers: liveFlightsDtosTask.Result.ToArray(),
            HotelOffers: liveHotelOffersDtosTask.Result.ToArray());
    }
    
    private async Task<IEnumerable<PackageFlightOfferDto>> GetLiveFlightOffersDtosWithoutCompensation(
        PackageId packageId,
        Market market,
        Currency currency,
        Occupancy[] occupancies,
        PackageLiveVariantsParameters parameters,
        string partnerCode,
        CancellationToken cancellationToken)
    {
        var flightVariants = await GetFlightVariantsWithoutCompensation(
            packageId,
            market,
            currency,
            occupancies,
            parameters,
            partnerCode,
            cancellationToken);

        return GetLiveFlightOffersDtosFromVariants(flightVariants, null);
    }

    private async Task<FlightLiveVariants> GetFlightVariantsWithoutCompensation(
        PackageId packageId,
        Market market,
        Currency currency,
        Occupancy[] occupancies,
        PackageLiveVariantsParameters parameters,
        string partnerCode,
        CancellationToken cancellationToken)
    {
        // TODO: Rewrite it - use flights the same way as in dynamic search
        var (departureAirports, arrivalAirports) = await GetDepartureAndArrivalAirports(
            packageId,
            market,
            cancellationToken);

        var combinedOccupancy = CombineOccupancies(occupancies);

        return await GetFlightLiveVariants(packageId,
            parameters: parameters,
            combinedOccupancy: combinedOccupancy,
            partnerCode: partnerCode,
            currency: currency,
            departureAirports: departureAirports,
            arrivalAirports: arrivalAirports,
            maxStops: 1,
            maxLegDuration: null,
            flightIds: [],
            cancellationToken: cancellationToken);
    }

    private async Task<FlightLiveVariants> GetFlightLiveVariants(
        PackageId packageId,
        PackageLiveVariantsParameters parameters,
        Occupancy combinedOccupancy,
        string partnerCode,
        string currency,
        Airport[] departureAirports,
        Airport[] arrivalAirports,
        int maxStops,
        TimeSpan? maxLegDuration,
        HashSet<string> flightIds,
        CancellationToken cancellationToken)
    {
        if (parameters is { PreferSelectedFlight: true, FlightOptionId: not null })
        {
            var selectedFlightVariantTask = GetSelectedFlightLiveVariant(
                partnerCode,
                currency,
                combinedOccupancy,
                parameters.FlightOptionId,
                cancellationToken);
            var offerAvailabilityTask = offerAccuracyGateway.GetUnavailableFlightOffers([parameters.FlightOptionId], combinedOccupancy, cancellationToken);
            await Task.WhenAll(selectedFlightVariantTask, offerAvailabilityTask);

            if (selectedFlightVariantTask.Result is not null && 
                (offerAvailabilityTask.Result.Count == 0 || offerAvailabilityTask.Result.Contains(parameters.FlightOptionId)))
                return FlightLiveVariants.Create([selectedFlightVariantTask.Result]);
        }

        FlightOfferDto[] flightDtos = [];

        if (parameters.PreferSelectedFlight //todo what difference made here parameters.PreferSelectedFlight ?
            && flightIds.Count > 0)
        {
            var flightOffersTask = flightGateway.GetFlightsByOfferKeyAsync(new FlightByOfferKeySearchCriteria
            (
                FlightOfferKeys: flightIds,
                PartnerCode: partnerCode,
                Occupancy: combinedOccupancy,
                IncludeFlightDetails: true
            ), cancellationToken);
            
            var offerAvailabilityTask = offerAccuracyGateway.GetUnavailableFlightOffers(flightIds.ToArray(), combinedOccupancy, cancellationToken);
            await Task.WhenAll(flightOffersTask, offerAvailabilityTask);

            flightDtos = flightOffersTask.Result
                .Where(x => !offerAvailabilityTask.Result.Contains(x.Key))
                .ToArray();
        }

        if (flightDtos.Length == 0)
        {
            var alternativeFlightOffersTask = GetAlternativeFlightOffers();
            var offerAvailabilityTask = offerAccuracyGateway.GetUnavailableFlightOffers(alternativeFlightOffersTask.Result.Select(x => x.Key).ToArray(), combinedOccupancy, cancellationToken);
            await Task.WhenAll(alternativeFlightOffersTask, offerAvailabilityTask);

            flightDtos = alternativeFlightOffersTask.Result
                .Where(x => !offerAvailabilityTask.Result.Contains(x.Key))
                .ToArray();
        }

        var flightVariants = FlightLiveVariants.Create(flightDtos.Select(MapToFlightLiveVariantFromOfferDto));

        await LiveCheckFlightVariant(
            partnerCode,
            currency,
            parameters,
            combinedOccupancy,
            flightVariants,
            cancellationToken);

        return flightVariants;

        async Task<List<FlightOfferDto>> GetAlternativeFlightOffers()
        {
            return (await flightGateway.GetAlternativeFlightOffers(
                new AlternativeFlightOffersCriteria(
                    PartnerCode: partnerCode,
                    CheckIn: packageId.CheckIn,
                    StayLength: packageId.StayLength,
                    DepartureAirports: departureAirports.Select(a => a.ToString()).ToArray(),
                    ArrivalAirports: arrivalAirports.Select(a => a.ToString()).ToArray(),
                    Occupancy: combinedOccupancy,
                    MaxStops: maxStops,
                    MaxLegDuration: maxLegDuration,
                    Limit: null),
                cancellationToken)).ToList();
        }

        FlightLiveVariant MapToFlightLiveVariantFromOfferDto(FlightOfferDto x)
        {
            return flightVariantFactory.Create(
                key: x.Key,
                departureAirport: x.DepartureAirport,
                arrivalAirport: x.ArrivalAirport,
                departureDate: x.DepartureDate,
                arrivalDate: x.ArrivalDate,
                returnDepartureDate: x.ReturnDepartureDate,
                returnArrivalDate: x.ReturnArrivalDate,
                providerCode: x.ProviderCode,
                stops: x.Stops,
                airlineCodes: x.AirlineCodes,
                flightIds: x.FlightIds,
                legLocators: x.LegLocators,
                currency: currency,
                prices: x.Prices.Select(p =>
                    new Money(p.Value.PaxConfigurations.First().Value.TotalPrice, p.Value.Currency)).ToArray(),
                registeredBaggageIncluded: x.BaggageIncluded);
        }
    }

    private async Task<List<PackageHotelOfferDto>> GetLiveHotelOffersDtosWithoutCompensation(
        PackageId packageId,
        Currency currency,
        string partnerCode,
        Occupancy[] occupancies,
        CancellationToken cancellationToken)
    {
        var hotelOfferVariants = await GetHotelOfferVariantsWithoutCompensation(
            packageId,
            currency,
            false,
            partnerCode,
            occupancies,
            cancellationToken);

        return GetLiveHotelOfferDtosFromVariants(hotelOfferVariants, null);
    }

    private async Task<HotelOfferLiveVariants> GetHotelOfferVariantsWithoutCompensation(
        PackageId packageId,
        Currency currency,
        bool onlyRefundable,
        string partnerCode,
        Occupancy[] occupancies,
        CancellationToken cancellationToken)
    {
        var variantsGroupedByRoomsDtos = await hotelTransactionGateway.GetVariantsGroupedByRooms(
            new HotelOfferVariantsCritieria(
                CheckIn: packageId.CheckIn,
                CheckOut: packageId.CheckIn.AddDays(packageId.StayLength),
                PartnerCode: partnerCode,
                MetaCode: packageId.MetaCode,
                Occupancies: occupancies
            ),
            cancellationToken);

        var hotelOfferVariantsGroupedByRooms = variantsGroupedByRoomsDtos
            .Select(x => x
                .ToDictionary(
                    p => p.Key,
                    p => p.Value
                        .Select(o => hotelOfferVariantFactory
                            .Create(
                                offerId: o.OfferId,
                                mealPlan: o.MealPlan.ToDomain(),
                                refundability: o.Refundability.ToDomain(),
                                freeRefundUntil: o.FreeRefundUntil,
                                currency: currency,
                                price: new Money(o.Price.Value, o.Price.Currency),
                                priceAtHotel: new Money(o.PriceAtHotel.Value, o.PriceAtHotel.Currency),
                                availability: o.Availability))
                        .ToList()))
            .ToList();

        return HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms: hotelOfferVariantsGroupedByRooms,
            onlyRefundable: onlyRefundable);
    }

    private static Occupancy CombineOccupancies(Occupancy[] occupancies)
    {
        return new Occupancy(occupancies.Sum(o => o.Adults), occupancies.SelectMany(o => o.ChildrenAges).ToArray());
    }
}