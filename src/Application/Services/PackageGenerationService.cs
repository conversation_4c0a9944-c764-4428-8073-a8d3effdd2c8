using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.Abstractions.Tasks;
using Esky.Packages.Application.Exceptions;
using Esky.Packages.Application.PackagesGeneration;
using Esky.Packages.Application.PackagesGeneration.Factories;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Application.Tasks;
using Esky.Packages.Domain.Model.PackageDefinitions;
using Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias.DurationStrategies;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.Services;

public interface IPackageGenerationService
{
    Task EnqueueGenerations(EnqueueGenerationsTask task, CancellationToken cancellationToken = default);

    Task GeneratePackages(string definitionId, PackagePipelineOptions options,
        TransformedPackageDefinitionParameters parameters, CancellationToken cancellationToken = default);

    Task RetryPipelines(RetryPipelinesTask task, CancellationToken cancellationToken = default);
    Task RetryAllFailedPipelines(RetryAllFailedPipelinesTask task, CancellationToken cancellationToken = default);
}

internal class PackageGenerationService(
    IPackageDefinitionRepository packageDefinitionRepository,
    IPackagePipelineFactory packagePipelineFactory,
    IPackageDefinitionParametersTransformerFactory packageDefinitionParametersTransformerFactory,
    IPipelineIdGenerator pipelineIdGenerator,
    ITaskScheduler taskScheduler,
    IPipelineRepository pipelineRepository,
    ILogger<PackageGenerationService> logger)
    : IPackageGenerationService
{
    public async Task EnqueueGenerations(
        EnqueueGenerationsTask task,
        CancellationToken cancellationToken)
    {
        var definition = await packageDefinitionRepository.GetById(task.DefinitionId, cancellationToken);
        if (definition is null)
        {
            throw new PackageDefinitionNotFoundException(task.DefinitionId);
        }

        await EnqueueGenerationJobs(definition, task, cancellationToken);
    }

    private async Task EnqueueGenerationJobs(
        PackageDefinition definition,
        EnqueueGenerationsTask task,
        CancellationToken cancellationToken)
    {
        // TODO: Refactor transformers! Create something like packagePipeline with context and state
        var transformedParameters = await packageDefinitionParametersTransformerFactory
            .Create()
            .Transform(
                definition.Parameters, 
                TransformedPackageDefinitionParameters.FromDefinition(definition), 
                cancellationToken);

        var totalPartitions = transformedParameters
            .TimeCriteria
            .DurationStrategy
            .AsDerivedOrThrow<CheckInDurationStrategy>()
            .GetTotalPartitionsByDate();

        var partitionsToGenerate = task.Partitions is { Length: > 0 }
            ? task.Partitions
            : Enumerable.Range(0, totalPartitions).ToArray();

        var pipelineGroupId = pipelineIdGenerator.Generate();
        var pipelinesToAdd = new List<Pipeline>();
        var tasks = new List<GeneratePackagesTask>();

        foreach (var partition in partitionsToGenerate)
        {
            if (partition >= totalPartitions)
            {
                logger.LogWarning("Partition {partition} is out of range for definition {definitionId}", partition,
                    definition.Id);

                continue;
            }

            var parameters = await packageDefinitionParametersTransformerFactory
                .CreateForPartition(partition)
                .Transform(definition.Parameters, transformedParameters, cancellationToken);

            var options = CreateOptions(
                pipelineId: pipelineIdGenerator.Generate(),
                startedByUserId: task.StartedByUserId,
                pipelineGroupId: pipelineGroupId,
                totalPartitions: totalPartitions,
                partition: partition,
                generationMode: task.GenerationMode);

            pipelinesToAdd.Add(new Pipeline
            {
                Id = options.PipelineId,
                GroupId = options.PipelineGroupId,
                DefinitionId = definition.Id,
                StartedByUserId = options.StartedByUserId,
                CreatedAt = DateTime.UtcNow,
                Status = PipelineStatus.Waiting,
                Partition = options.Partition,
                TotalPartitions = options.TotalPartitions
            });

            tasks.Add(new GeneratePackagesTask
            {
                DefinitionId = definition.Id,
                Options = options,
                Parameters = parameters
            });
        }

        await pipelineRepository.UpsertWithoutConcurrency(pipelinesToAdd, cancellationToken);
        foreach (var taskToPublish in tasks) 
        {
            await taskScheduler.Publish(taskToPublish, cancellationToken);
        }
        await pipelineRepository.RemoveOutdatedPipelines(definition.Id, cancellationToken);
    }

    public async Task GeneratePackages(
        string definitionId,
        PackagePipelineOptions options,
        TransformedPackageDefinitionParameters parameters,
        CancellationToken cancellationToken)
    {
        var pipeline = packagePipelineFactory.Create(definitionId, parameters, options);

        await pipeline.Run(cancellationToken);
    }

    public async Task RetryAllFailedPipelines(RetryAllFailedPipelinesTask task, CancellationToken cancellationToken)
    {
        var failedPipelinesGroups = await pipelineRepository.GetAllFailedPipelinesByDefinitionId(cancellationToken);

        foreach (var failedPipelinesGroup in failedPipelinesGroups)
        {
            await RetryPipelines(
                new RetryPipelinesTask
                {
                    DefinitionId = failedPipelinesGroup.DefinitionId,
                    PipelineIds = failedPipelinesGroup.FailedPipelineIds,
                    StartedByUserId = task.StartedByUserId,
                    GenerationMode = task.GenerationMode
                }, cancellationToken);
        }
    }

    public async Task RetryPipelines(RetryPipelinesTask task, CancellationToken cancellationToken)
    {
        var definition = await packageDefinitionRepository.GetById(task.DefinitionId, cancellationToken);
        if (definition is null)
        {
            throw new PackageDefinitionNotFoundException(task.DefinitionId);
        }

        var transformedParameters = await packageDefinitionParametersTransformerFactory
            .Create()
            .Transform(definition.Parameters, TransformedPackageDefinitionParameters.FromDefinition(definition), cancellationToken);

        foreach (var pipelineId in task.PipelineIds)
        {
            var pipeline = await pipelineRepository.GetById(pipelineId, cancellationToken);
            if (pipeline is null)
            {
                logger.LogWarning("Pipeline {pipelineId} does not exist", pipelineId);

                continue;
            }

            var parameters = await packageDefinitionParametersTransformerFactory
                .CreateForPartition(pipeline.Partition)
                .Transform(definition.Parameters, transformedParameters, cancellationToken);

            var options = CreateOptions(
                pipelineId: pipelineId,
                startedByUserId: task.StartedByUserId,
                pipelineGroupId: pipeline.GroupId,
                totalPartitions: pipeline.TotalPartitions,
                partition: pipeline.Partition,
                generationMode: task.GenerationMode);

            await pipelineRepository.MarkForRetry(
                id: pipelineId,
                startedByUserId: task.StartedByUserId,
                cancellationToken: cancellationToken);

            await taskScheduler.Publish(
                new GeneratePackagesTask
                {
                    DefinitionId = definition.Id,
                    Options = options,
                    Parameters = parameters
                }, cancellationToken);
        }
    }

    private static PackagePipelineOptions CreateOptions(
        string pipelineId,
        string startedByUserId,
        string pipelineGroupId,
        int totalPartitions,
        int partition,
        GenerationMode generationMode)
    {
        return new PackagePipelineOptions
        {
            PipelineId = pipelineId,
            StartedByUserId = startedByUserId,
            PipelineGroupId = pipelineGroupId,
            TotalPartitions = totalPartitions,
            Partition = partition,
            GenerationMode = generationMode
        };
    }
}