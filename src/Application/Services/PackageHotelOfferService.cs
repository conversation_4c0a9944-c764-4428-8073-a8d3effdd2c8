using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.Services;

public interface IPackageHotelOfferService
{
    Task<(int TruePositives, int FalsePositives, int Updated)> ApplyQuotes(List<HotelOfferQuote> quotes, 
        CancellationToken cancellationToken = default);
}

internal class PackageHotelOfferService(IPackageHotelOfferRepository repository, ILogger<PackageHotelOfferService> logger) 
    : IPackageHotelOfferService
{
    public async Task<(int TruePositives, int FalsePositives, int Updated)> ApplyQuotes(List<HotelOfferQuote> quotes, 
        CancellationToken cancellationToken)
    {
        var foundStayKeys = new HashSet<PackageHotelOfferStayKey>();
        
        var quotesByStayKey = quotes
            .GroupBy(q => new PackageHotelOfferStayKey(q.<PERSON>a<PERSON><PERSON>, q.CheckIn, q.<PERSON>Length))
            .ToDictionary(g => g.Key, g => g.ToList());
        
        var stayKeys = quotesByStayKey.Keys.ToList();
        var packageHotelOffers = await repository.ListByStayKeys(stayKeys, cancellationToken);
        
        var updatedPackageHotelOffers = new List<PackageHotelOffer>();
        
        foreach (var packageHotelOffer in packageHotelOffers)
        {
            var updated = packageHotelOffer.ApplyQuotes(quotesByStayKey);
            if (updated)
            {
                updatedPackageHotelOffers.Add(packageHotelOffer);
            }
            
            foundStayKeys.Add(packageHotelOffer.GetStayKey());
        }
        
        if (updatedPackageHotelOffers.Count > 0)
        {
            await repository.Update(updatedPackageHotelOffers, cancellationToken);
            logger.LogDebug("Updated {count} package hotel offers", updatedPackageHotelOffers.Count);
        }
        
        var falsePositives = stayKeys.Except(foundStayKeys).Count();
        var truePositives = stayKeys.Count - falsePositives;
        
        logger.LogDebug("Applied {count} quotes, true positives: {truePositives}, false positives: {falsePositives}", 
            quotes.Count, truePositives, falsePositives);
        
        return (truePositives, falsePositives, updatedPackageHotelOffers.Count);
    }
}