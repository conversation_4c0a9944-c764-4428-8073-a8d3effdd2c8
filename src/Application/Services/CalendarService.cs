using Esky.Packages.Application.Dtos.Common;
using Esky.Packages.Contract.Calendars;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Model.PackageHotelAirports;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.Services;

public record CalendarsParameters(
    string MarketId,
    int MetaCode,
    List<Airport> DepartureAirports,
    List<MealPlan> MealPlans,
    List<TimeOfDay> InboundDepartures,
    List<TimeOfDay> OutboundDepartures,
    Occupancy Occupancy);

public record CalendarAggregationParameters(
    string MarketId,
    int MetaCode,
    Occupancy Occupancy);

public interface ICalendarService
{
    Task<CalendarsDto> GetCalendars(CalendarsParameters parameters, CancellationToken cancellationToken = default);
    Task<CalendarAggregationDto> GetCalendarAggregation(CalendarAggregationParameters parameters,
        CancellationToken cancellationToken = default);
}

internal class CalendarService(
    IPackageVariantRepository packageVariantRepository,
    IPackageFlightVariantRepository packageFlightVariantRepository,
    IPackageHotelOfferRepository packageHotelOfferRepository,
    IPackageHotelAirportsRepository packageHotelAirportsRepository)
    : ICalendarService
{
    public async Task<CalendarsDto> GetCalendars(CalendarsParameters parameters,
        CancellationToken cancellationToken = default)
    {
        var packageOccupancy = (PackageOccupancy)PackageOccupancy.Map(parameters.Occupancy, shouldThrow: true)!;
        
        var calendar = await GetPackageFactorsCalendar(
            metaCode: parameters.MetaCode, 
            occupancy: packageOccupancy,
            marketId: parameters.MarketId, 
            mealPlans: parameters.MealPlans, 
            departureAirports: parameters.DepartureAirports, 
            inboundDepartures: parameters.InboundDepartures,
            outboundDepartures: parameters.OutboundDepartures,
            cancellationToken: cancellationToken);
        
        var calendars = calendar
            .GroupBy(c => (c.DepartureDate.Year, c.DepartureDate.Month))
            .OrderBy(c => (c.Key.Year, c.Key.Month))
            .Select(g => new CalendarDto
            {
                Year = g.Key.Year,
                Month = g.Key.Month,
                Days = g
                    .GroupBy(c => c.DepartureDate.Day)
                    .OrderBy(c => c.Key)
                    .ToDictionary(g2 => g2.Key, g2 => g2
                        .GroupBy(c => c.Id.StayLength)
                        .OrderBy(c => c.Key)
                        .ToDictionary(g3 => g3.Key, g3 => g3
                            .Select(c => new CalendarEntryDto
                                {
                                    PackageId = new PackageId(c.Id.CheckIn, c.Id.StayLength, c.Id.MarketId,
                                        c.Id.MetaCode),
                                    DepartureAirport = c.Id.DepartureAirport,
                                    CheckIn = c.Id.CheckIn,
                                    ReturnArriveDate = c.ReturnArrivalDate,
                                    MealPlan = c.Id.MealPlan.ToDto(),
                                    Price = c.Price,
                                }
                            )
                            .OrderBy(c => c.Price)
                            .First()))
            }).ToList();

        return new CalendarsDto(calendars);
    }

    public async Task<CalendarAggregationDto> GetCalendarAggregation(CalendarAggregationParameters parameters,
        CancellationToken cancellationToken = default)
    {
        var packageOccupancy = (PackageOccupancy)PackageOccupancy.Map(parameters.Occupancy, shouldThrow: true)!;
        
        var aggregation = await GetPackageFactorsAggregation(parameters.MetaCode, parameters.MarketId, packageOccupancy,
            cancellationToken);
           
        return new CalendarAggregationDto(
            aggregation.DepartureAirports.Select(a => a.ToString()).ToList(),
            aggregation.MealPlans.Select(m => m.ToDto()).ToList(),
            aggregation.Occupancies.Select(o => o.ToOccupancy().ToDto()).ToList());
    }

    private async Task<List<PackageVariant>> GetPackageVariantCalendar(int metaCode, PackageOccupancy occupancy, 
        string marketId, List<MealPlan> mealPlans, List<Airport> departureAirports, CancellationToken cancellationToken)
    {
        return await packageVariantRepository.GetCheapestsPerDepartureDate(
            metaCode: metaCode, 
            occupancy: occupancy, 
            marketId: marketId, 
            mealPlans: mealPlans,
            departureAirports: departureAirports, 
            cancellationToken);
    }

    private async Task<List<PackageVariant>> GetPackageFactorsCalendar(int metaCode, PackageOccupancy occupancy, 
        string marketId, List<MealPlan> mealPlans, List<Airport> departureAirports, List<TimeOfDay> inboundDepartures, 
        List<TimeOfDay> outboundDepartures, CancellationToken cancellationToken)
    {
        var packageHotelAirports = await packageHotelAirportsRepository.ListByMarketIdAndMetaCodes(marketId, 
            [metaCode], cancellationToken);
        var arrivalAirports = packageHotelAirports.SelectMany(m => m.Airports).Distinct().ToList();

        if (arrivalAirports.Count == 0)
        {
            return [];
        }

        var packageFlightVariants = await packageFlightVariantRepository.GetCheapestsPerDates(marketId,
            departureAirports, arrivalAirports, occupancy, inboundDepartures, outboundDepartures, cancellationToken);

        var packageHotelOfferIds = GetHotelOfferIds(packageFlightVariants, packageHotelAirports);
        if (packageHotelOfferIds.Count == 0)
        {
            return [];
        }

        var packageHotelOffers = await packageHotelOfferRepository.ListByIds(packageHotelOfferIds, cancellationToken);
        if (packageHotelOffers.Count == 0)
        {
            return [];
        }

        var packageHotelOfferVariants = packageHotelOffers
            .SelectMany(h => h.GetVariants([occupancy], mealPlans))
            .ToList();

        return PackageVariant
            .CreateMany(packageFlightVariants, packageHotelOfferVariants, packageHotelAirports);
    }
    
    private async Task<PackageVariantAggregation> GetPackageVariantAggregation(int metaCode, string marketId, 
        CancellationToken cancellationToken)
    {
        return await packageVariantRepository.GetPackageVariantAggregation(metaCode, marketId, cancellationToken);
    }

    private async Task<PackageVariantAggregation> GetPackageFactorsAggregation(int metaCode, string marketId, 
        PackageOccupancy occupancy, CancellationToken cancellationToken)
    {
        var packageHotelAirports = await packageHotelAirportsRepository.ListByMarketIdAndMetaCodes(marketId, 
            [metaCode], cancellationToken);
        var arrivalAirports = packageHotelAirports.SelectMany(m => m.Airports).Distinct().ToList();

        if (arrivalAirports.Count == 0)
        {
            return PackageVariantAggregation.Empty;
        }

        var departureAirports = await packageFlightVariantRepository.GetDepartureAirports(marketId,
            arrivalAirports, occupancy, cancellationToken);

        var packageHotelOffers = await packageHotelOfferRepository.ListByMarketAndMetaCode(marketId,
            metaCode, cancellationToken);

        var mealPlans = packageHotelOffers.SelectMany(o => o.GetVariants([occupancy], []))
            .Select(v => v.Id.MealPlan)
            .Distinct()
            .ToList();
        
        var occupancies = packageHotelOffers.SelectMany(o => o.GetVariants([], []))
            .Select(v => v.Id.Occupancy)
            .Distinct()
            .ToList();

        return new PackageVariantAggregation(mealPlans, departureAirports, occupancies);
    }


// TODO: Move to domain
    private List<PackageHotelOfferId> GetHotelOfferIds(
        List<PackageFlightVariant> packageFlightVariant,
        List<PackageHotelAirports> airportMappings)
    {
        var airportsToMetaCodes = airportMappings
            .SelectMany(m => m.Airports.Select(a => (ArrivalAirport: a, m.Id.MetaCode)))
            .GroupBy(g => g.ArrivalAirport)
            .ToDictionary(g => g.Key, g => g.Select(x => x.MetaCode).Distinct().ToList());

        return packageFlightVariant
            .SelectMany(f => airportsToMetaCodes.GetValueOrDefault(f.Id.ArrivalAirport, []).Select(metaCode =>
                new PackageHotelOfferId(
                    checkIn: f.Id.CheckIn,
                    stayLength: f.Id.StayLength,
                    marketId: f.Id.MarketId,
                    metaCode: metaCode)))
            .ToList();
    }
}