using Esky.Packages.Application.PackagesGeneration.Factories;
using Esky.Packages.Application.PackagesGeneration.Stages;
using Esky.Packages.Application.PackagesGeneration.Transformers.Steps;
using Esky.Packages.Application.Services;
using Esky.Packages.Application.Workers;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.Packages.Application.DependencyInjections;

public interface IApplicationOptions
{
    void AddCurrencyConverter();
    void AddGenerationPipeline();
}

internal class ApplicationOptions : IApplicationOptions
{
    private readonly IServiceCollection _services;

    public ApplicationOptions(IServiceCollection services)
    {
        _services = services;
    }

    public void AddCurrencyConverter()
    {
        _services.AddHostedService<CurrencyConverterRefresher>();
    }

    public void AddGenerationPipeline()
    {
        _services
            .AddScoped<IPackageDefinitionParametersTransformerFactory, PackageDefinitionParametersTransformerFactory>()
            .AddScoped<AddDefaultOccupanciesTransformer>()
            .AddScoped<EvaluateRollingDurationStrategiesTransformer>()
            .AddScoped<EvaluateHotelMetaCodeSelectorTransformer>()
            .AddScoped<MapAirportsTransformer>()
            .AddScoped<IPackagePipelineFactory, PackagePipelineFactory>()
            .AddScoped<SelectFlightDestinationsTimeRangesStage>()
            .AddScoped<FindFlightsStage>()
            .AddScoped<CreatePackageFlightsStage>()
            .AddScoped<FilterChangedPackageFlightsToUpsertStage>()
            .AddScoped<SavePackageFlightsStage>()
            .AddScoped<CreatePackageFlightsHotelDetailsStage>()
            .AddScoped<SelectHotelStayTimeRangesStage>()
            .AddScoped<RemoveOutdatedPackageFlightsStage>()
            .AddScoped<RemoveOutdatedPackageFlightsTimeRangesStage>()
            .AddScoped<RemoveOutdatedPackageHotelOffersStage>()
            .AddScoped<RemoveOutdatedPackageHotelOffersTimeRangesStage>()
            .AddScoped<FindHotelOffersStage>()
            .AddScoped<GetMarketStage>()
            .AddScoped<SavePackageHotelAirportsStage>()
            .AddScoped<GetProviderConfigurationIdsStage>()
            .AddScoped<PublishBloomFilterFlightNotificationsStage>()
            .AddScoped<PublishBloomFilterHotelOfferNotificationsStage>()
            .AddScoped<CreatePackageHotelOffersStage>()
            .AddScoped<FilterChangedPackageHotelOffersToUpsertStage>()
            .AddScoped<SavePackageHotelOffersStage>()
            .AddScoped<CreatePackageHotelRouteOffersStage>()
            .AddScoped<CreatePackageAvailabilitiesStage>()
            .AddScoped<LogPackagesStage>()
            .AddScoped<SavePackageAvailabilitiesStage>()
            .AddScoped<RemoveOutdatedPackageAvailabilitiesStage>()
            .AddScoped<SelectFlightOccupanciesStage>()
            .AddScoped<IPackageGenerationService, PackageGenerationService>();
    }
}