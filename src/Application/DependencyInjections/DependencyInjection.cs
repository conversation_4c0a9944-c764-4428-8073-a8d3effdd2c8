using Esky.Packages.Application.Services;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.Packages.Application.DependencyInjections;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services, 
        Action<IApplicationOptions>? builder = null)
    {
        var options = new ApplicationOptions(services);
        builder?.Invoke(options);

        return services
            .AddSingleton<ICurrencyConverterService, CurrencyConverterService>()
            .AddSingleton<IPackageFlightService, PackageFlightService>()
            .AddSingleton<IPackageFlightVariantService, PackageFlightVariantService>()
            .AddSingleton<IPackageHotelOfferService, PackageHotelOfferService>()
            .AddSingleton<IPackageService, PackageService>()
            .AddSingleton<IPackageLiveVariantsService, PackageLiveVariantsService>()
            .AddSingleton<ISearchService, SearchService>()
            .AddSingleton<ICalendarService, CalendarService>()
            .AddSingleton<IMarketsService, MarketsService>()
            .AddSingleton<IPackageDefinitionService, PackageDefinitionService>()
            .AddSingleton<IPackageAvailabilityService, PackageAvailabilityService>()
            .AddSingleton<IPackageGenerationStatisticsService, PackageGenerationStatisticsService>();
    }
}