using Esky.Packages.Application.PackagesGeneration.State.Components;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Model.PackageAvailabilities;

namespace Esky.Packages.Application.PackagesGeneration.State;

public class PackagePipelineState
{
    public required string TargetDefinitionId { get; init; }
    public required DateTime StartedAt { get; init; }

    public Market Market { get; set; } = null!;

    // GetProviderConfigurationIdsStage
    public List<string> HotelOfferProviderConfigurationIds { get; set; } = [];
    
    // SelectFlightDestinationsTimeRangesStage
    public List<FlightDestinationsTimeRange> FlightDestinationsTimeRanges { get; set; } = [];

    // ChunkFlightDestinationsTimeRangesStage
    public ChunkFlightDestinationsTimeRangeState ChunkFlightDestinationsTimeRangeState { get; set; } = null!;

    // CreatePackageFlightHotelDetailsStage
    public List<PackageFlightHotelDetails> PackageFlightsHotelDetails { get; set; } = [];

    // SelectHotelStayTimeRangesStage
    public List<HotelStayTimeRange> HotelStayTimeRanges { get; set; } = [];

    // ChunkHotelStayTimeRangesStage
    public ChunkHotelStayTimeRangeState ChunkHotelStayTimeRangeState { get; set; } = ChunkHotelStayTimeRangeState.Empty;

    // CreatePackageAvailabilitiesStage
    public Dictionary<int, PackageAvailability> PackageAvailabilities { get; } = [];
    public PackageOccupancy[] FlightOccupancies { get; set; }
}