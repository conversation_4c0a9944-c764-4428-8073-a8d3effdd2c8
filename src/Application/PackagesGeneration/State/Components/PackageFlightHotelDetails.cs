using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;

namespace Esky.Packages.Application.PackagesGeneration.State.Components;

public record struct PackageFlightHotelDetails(
    PackageFlightId Id,
    DateOnly CheckIn,
    int StayLength,
    string DepartureAirport,
    string ArrivalAirport,
    int[] MetaCodes,
    Dictionary<PackageOccupancy, FlightPriceEntry> Prices);
