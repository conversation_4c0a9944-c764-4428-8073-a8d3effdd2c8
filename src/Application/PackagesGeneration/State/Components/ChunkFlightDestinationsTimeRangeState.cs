using Esky.Packages.Domain.Model.PackageFlights;
namespace Esky.Packages.Application.PackagesGeneration.State.Components;

public class ChunkFlightDestinationsTimeRangeState(FlightDestinationsTimeRange currentChunk)
{
    public FlightDestinationsTimeRange CurrentChunk { get; } = currentChunk;

    // FindFlightsStage
    public List<Dtos.FlightOffers.FlightOfferDto> Flights { get; set; } = [];

    // CreatePackageFlightsStage
    public List<PackageFlight> PackageFlights { get; set; } = [];

    // FilterChangedPackageFlightsToUpsertStage
    public List<PackageFlight> PackageFlightsToUpsert { get; set; } = [];
}
