using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Application.PackagesGeneration.State.Components;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Calculate hotel stay time ranges based on package flights, so we won't ask for hotel availability that doesn't have flights
/// </summary>
public class SelectHotelStayTimeRangesStage : PipelineStageBase<PackagePipelineContext>
{
    protected override Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        context.State.HotelStayTimeRanges = context
            .State
            .PackageFlightsHotelDetails
            .GroupBy(x => new HotelStayTimeRangeGroupingKey(
                CheckIn: x.CheckIn,
                StayLength: x.StayLength))
            .Select(x => new HotelStayTimeRange(
                CheckIn: x.Key.CheckIn,
                CheckOut: x.Key.CheckIn.AddDays(x.Key.StayLength),
                StayLength: x.Key.StayLength,
                MetaCodes: x.SelectMany(y => y.MetaCodes).Distinct().ToArray()))
            .ToList();

        context.Logger.LogInformation("Hotel stay time ranges count: {total}", context.State.HotelStayTimeRanges.Count);

        return Task.CompletedTask;
    }

    private record HotelStayTimeRangeGroupingKey(
        DateOnly CheckIn, 
        int StayLength);
}