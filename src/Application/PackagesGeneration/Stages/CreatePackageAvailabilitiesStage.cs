using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Application.PackagesGeneration.State.Components;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageAvailabilities;
using Esky.Packages.Domain.Types;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Create package availabilities so hotel index can have data which can base initial sorting on.
/// </summary>
public class CreatePackageAvailabilitiesStage : PipelineStageBase<PackagePipelineContext>
{
    protected override Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        if (!context.DefinitionParameters.Occupancies.Contains(PackageOccupancy.Occupancy2A))
        {
            context.Logger.LogInformation("Skipping step - occupancy {occupancy} not present in a definition.", PackageOccupancy.Occupancy2A);
            return Task.CompletedTask;
        }

        var availabilities = context.State.ChunkHotelStayTimeRangeState.PackageHotelRouteOffers
            .GroupBy(
                p => p.MetaCode,
                (i, packages) => (
                    MetaCode: i,
                    Availiability: GetAvailability(context, packages.ToList())))
            .ToDictionary(
                p => p.MetaCode,
                p => p.Availiability);

        foreach (var availability in availabilities)
        {
            if (context.State.PackageAvailabilities.TryGetValue(availability.Key, out var packageAvailability))
            {
                packageAvailability.Merge(availability.Value);
            }
            else
            {
                context.State.PackageAvailabilities.Add(availability.Key, availability.Value);
            }
        }

        return Task.CompletedTask;
    }

    private static PackageAvailability GetAvailability(
        PackagePipelineContext context, 
        List<PackageHotelRouteOffers> packages)
    {
        var hotelOfferOccupancies = new HashSet<PackageOccupancy>();
        var flightOccupancies = new HashSet<PackageOccupancy>();
        var mealPlans = new HashSet<MealPlan>();
        var stayLengths = new HashSet<int>();
        var departureAirports = new HashSet<string>();
        var lowestPricesPerStayLength = new Dictionary<int, decimal>();
        var minCheckIn = DateOnly.MaxValue;
        var maxCheckIn = DateOnly.MinValue;
        decimal? lowestTotalPrice = null;

        foreach (var package in packages)
        {
            var hotelPrices = package.HotelOffers.SelectMany(x => x.GetLowestPrices());
            var flightPrices = package.PackageFlightHotelDetails.SelectMany(x => x.Prices);

            hotelOfferOccupancies.UnionWith(hotelPrices.Select(occupancyPrice => occupancyPrice.Key));
            flightOccupancies.UnionWith(flightPrices.Select(occupancyPrice => occupancyPrice.Key));
            departureAirports.UnionWith(package.PackageFlightHotelDetails.Select(flightOffer => flightOffer.DepartureAirport));
            stayLengths.Add(package.StayLength);
            mealPlans
                .UnionWith(hotelPrices
                    .SelectMany(occupancyPrice => occupancyPrice.Value
                        .Select(mealPlanPrice => mealPlanPrice.Key)));

            var packagePrice = GetLowestTotalPrice(package);
            if (packagePrice.HasValue)
            {
                lowestTotalPrice = lowestTotalPrice.HasValue ? Math.Min(lowestTotalPrice.Value, packagePrice.Value) : packagePrice.Value;

                if (lowestPricesPerStayLength.TryGetValue(package.StayLength, out var currentPrice))
                {
                    lowestPricesPerStayLength[package.StayLength] = Math.Min(currentPrice, packagePrice.Value);
                }
                else
                {
                    lowestPricesPerStayLength.Add(package.StayLength, packagePrice.Value);
                }
            }

            if (minCheckIn > package.CheckIn)
            {
                minCheckIn = package.CheckIn;
            }

            if (maxCheckIn < package.CheckIn)
            {
                maxCheckIn = package.CheckIn;
            }
        }

        return new PackageAvailability
        {
            MetaCode = packages.First().MetaCode,
            Occupancies = hotelOfferOccupancies.Intersect(flightOccupancies).ToArray(),
            MealPlans = mealPlans.ToArray(),
            StayLengths = stayLengths.ToArray(),
            DepartureAirports = departureAirports.Select(x => new Airport(x)).ToArray(),
            LowestTotalPrice = lowestTotalPrice ?? decimal.MaxValue,
            LowestPricesPerStayLength = lowestPricesPerStayLength,
            MinCheckIn = minCheckIn,
            MaxCheckIn = maxCheckIn
        };
    }

    private static decimal? GetLowestTotalPrice(PackageHotelRouteOffers package)
    {
        var flightPrice = package.PackageFlightHotelDetails.Min(f =>
        {
            f.Prices.TryGetValue(PackageOccupancy.Occupancy2A, out var price);

            return price;
        });

        var hotelPrice = package.HotelOffers.Min(h =>
        {
            h.GetLowestPrices().TryGetValue(PackageOccupancy.Occupancy2A, out var prices);

            return prices?.Min(x => x.Value);
        });

        if (flightPrice is null || hotelPrice is null)
        {
            return null;
        }

        return flightPrice.Price + hotelPrice;
    }
}