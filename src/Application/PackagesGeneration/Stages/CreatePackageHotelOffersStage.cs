using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.Dtos.HotelOffers;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Types;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Group hotel offers by check in, stay length and meta code.
/// 2. For each group.
///     - Calculate airports based on previous stages for each meta code.
///     - Create hotel quotes (prices) per providerConfigurationId and occupancy
/// </summary>
public class CreatePackageHotelOffersStage(IPackageHotelOfferFactory packageHotelOfferFactory)
    : PipelineStageBase<PackagePipelineContext>
{
    protected override Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var packageHotelOffers = context
            .State
            .ChunkHotelStayTimeRangeState
            .HotelOffers
            .GroupBy(x => new PackageHotelOfferGroupingKey(x.CheckIn, x.Stay<PERSON>th, x.MetaCode))
            .OrderBy(x => x.Key.CheckIn)
            .ThenBy(x => x.Key.StayLength)
            .Select(x => CreatePackageHotelOffers(x, context))
            .ToList();

        context.Logger.LogInformation("Created package hotel offers: {count}", packageHotelOffers.Count);
        context.State.ChunkHotelStayTimeRangeState.PackageHotelOffers = packageHotelOffers;

        CleanUpPreviousStepState(context);

        return Task.CompletedTask;
    }

    private static void CleanUpPreviousStepState(PackagePipelineContext context)
    {
        context.State.ChunkHotelStayTimeRangeState.HotelOffers.Clear();
    }

    private PackageHotelOffer CreatePackageHotelOffers(IGrouping<PackageHotelOfferGroupingKey, HotelOfferDto> grouping,
        PackagePipelineContext context)
    {
        var airports = CreateAirportsForMetaCode(grouping.Key.MetaCode, context);
        var hotelOfferQuotes = grouping.Select(dto => dto.ToDomainQuote()).ToArray();

        var packageHotelOfferId = new PackageHotelOfferId(
            checkIn: grouping.Key.CheckIn,
            stayLength: grouping.Key.StayLength,
            marketId: context.State.Market.Id,
            metaCode: grouping.Key.MetaCode);
        
        var mergeIntoPackage = context.DefinitionParameters.MergeIntoPackageMetaCodes.Contains(grouping.Key.MetaCode);
        
        return packageHotelOfferFactory.Create(
            id: packageHotelOfferId,
            definitionId: context.State.TargetDefinitionId,
            currency: context.State.Market.Currency,
            occupancies: context.DefinitionParameters.Occupancies,
            providerConfigurationIds: context.State.HotelOfferProviderConfigurationIds.ToArray(),
            hotelOfferQuotes: hotelOfferQuotes,
            airports: airports,
            mergeIntoPackage: mergeIntoPackage);
    }

    private static Dictionary<Airport, Airport[]> CreateAirportsForMetaCode(int metaCode, 
        PackagePipelineContext context)
    {
        return context.State.PackageFlightsHotelDetails
            .Where(x => x.MetaCodes.Contains(metaCode))
            .Select(x => new { x.ArrivalAirport, x.DepartureAirport })
            .GroupBy(x => x.ArrivalAirport)
            .ToDictionary(x => new Airport(x.Key),
                x => x.Select(y => new Airport(y.DepartureAirport)).Distinct().ToArray());
    }

    private record PackageHotelOfferGroupingKey(DateOnly CheckIn, int StayLength, int MetaCode);
}