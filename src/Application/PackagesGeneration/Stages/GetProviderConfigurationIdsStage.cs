using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

public class GetProviderConfigurationIdsStage : PipelineStageBase<PackagePipelineContext>
{
    protected override Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        if (context.DefinitionParameters.PartnerCode == "THOMASCOOKUKPACKAGES")
        {
            context.State.HotelOfferProviderConfigurationIds = ["12|tcpaeac", "36|tcp", "63|lte", "122|def", "123|packages", "124|packages", "126|TPA"];
        }
        else if (context.DefinitionParameters.PartnerCode == "ESKYPLPACKAGES")
        {
            context.State.HotelOfferProviderConfigurationIds = ["12|epaeac", "36|epa", "119|plp", "119|hup", "119|skp", "122|epa", "123|epa", "124|esky-package"];
        }
        else if (context.DefinitionParameters.PartnerCode == "ESKYHUPACKAGES")
        {
            context.State.HotelOfferProviderConfigurationIds = ["12|epaeac", "36|epa", "119|plp", "119|hup", "119|skp", "122|epa", "124|esky-package"];
        }
        else if (context.DefinitionParameters.PartnerCode == "ESKYSKPACKAGES")
        {
            context.State.HotelOfferProviderConfigurationIds = ["12|epaeac", "36|epa", "119|plp", "119|hup", "119|skp", "122|epa", "124|esky-package"];
        }
        else if (context.DefinitionParameters.PartnerCode == "ESKYROPACKAGES")
        {
            context.State.HotelOfferProviderConfigurationIds = ["12|epaeac", "36|epa", "119|plp", "119|hup", "119|skp", "122|epa", "124|esky-package"];
        }
        else if (context.DefinitionParameters.PartnerCode == "ESKYBGPACKAGES")
        {
            context.State.HotelOfferProviderConfigurationIds = ["12|epaeac", "36|epa", "119|plp", "119|hup", "119|skp", "122|epa", "124|esky-package"];
        }
        else if (context.DefinitionParameters.PartnerCode == "ESKYHRPACKAGES")
        {
            context.State.HotelOfferProviderConfigurationIds = ["12|epaeac", "36|epa", "119|plp", "119|hup", "119|skp", "122|epa", "124|esky-package"];
        }
        
        return Task.CompletedTask;
    }
}