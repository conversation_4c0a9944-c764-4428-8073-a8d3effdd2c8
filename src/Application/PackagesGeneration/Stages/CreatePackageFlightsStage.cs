using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.Dtos.FlightOffers;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias.DurationStrategies;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Types.Extensions;
using FlightOffer = Esky.Packages.Domain.Model.PackageFlights.FlightOffer;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Calculate check in/check out for each flight offer.
/// 2. Filter out flights that do not match the definition stay length.
/// 3. Group flights by check in, stay length, departure and arrival airports.
/// 4. Create package flights from the grouped flights.
/// 5. Filter out prices that don't match the definition occupancies.
/// </summary>
public class CreatePackageFlightsStage(IPackageFlightFactory packageFlightFactory) : PipelineStageBase<PackagePipelineContext>
{
    protected override Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var checkInDurationStrategy = context.DefinitionParameters
            .TimeCriteria
            .DurationStrategy
            .AsDerivedOrThrow<CheckInDurationStrategy>();

        var flightsWithCheckInCheckOut = context
            .State
            .ChunkFlightDestinationsTimeRangeState
            .Flights
            .Select(CreateFlightCheckInCheckOutOffer)
            .Where(flight => flight.IsMatchingDefinitionStayLength(checkInDurationStrategy));

        context.State.ChunkFlightDestinationsTimeRangeState.PackageFlights = flightsWithCheckInCheckOut
            .GroupBy(x => new PackageFlightGroupingKey(
                CheckIn: x.CheckInCheckOut.CheckIn,
                StayLength: x.CheckInCheckOut.StayLength,
                DepartureAirport: x.FlightOffer.DepartureAirport,
                ArrivalAirport: x.FlightOffer.ArrivalAirport))
            .OrderBy(x => x.Key.CheckIn)
            .ThenBy(x => x.Key.StayLength)
            .Select(x => CreatePackageFlight(x, context))
            .ToList();

        CleanUpPreviousStepState(context);

        return Task.CompletedTask;
    }

    private static void CleanUpPreviousStepState(PackagePipelineContext context)
    {
        context.State.ChunkFlightDestinationsTimeRangeState.Flights.Clear();
    }

    private static FlightCheckInCheckOutOffer CreateFlightCheckInCheckOutOffer(FlightOfferDto flightOffer)
    {
        var flightCheckInCheckOut = new FlightCheckInCheckOut(
            arrivalDate: flightOffer.ArrivalDate, 
            returnDepartureDate: flightOffer.ReturnDepartureDate);

        return new FlightCheckInCheckOutOffer(flightOffer, flightCheckInCheckOut);
    }

    private PackageFlight CreatePackageFlight(
        IGrouping<PackageFlightGroupingKey, FlightCheckInCheckOutOffer> grouping,
        PackagePipelineContext context)
    {
        var key = grouping.Key;
        var packageFlightId = new PackageFlightId(
            checkIn: key.CheckIn, 
            stayLength: key.StayLength, 
            arrivalAirport: key.ArrivalAirport, 
            departureAirport: key.DepartureAirport, 
            marketId: context.State.Market.Id);

        var metaCodes = context.DefinitionParameters.ArrivalAirportCodeToMetaCodesMap
            .TryGetValue(key.ArrivalAirport, out var result) ? result : [];

        metaCodes = metaCodes.Intersect(context.DefinitionParameters.MergeIntoPackageMetaCodes).ToArray();

        return packageFlightFactory.Create(
            id: packageFlightId,
            definitionId: context.State.TargetDefinitionId,
            partnerCode: context.DefinitionParameters.PartnerCode,
            currency: context.State.Market.Currency,
            occupancies: context.State.FlightOccupancies,
            flightOffers: CreateFlightOffers(grouping), // TODO: Move this to factory or domain model
            flightQuotes: CreateFlightQuotes(grouping, context), // TODO: Move this to factory or domain model
            metaCodes: metaCodes);
    }

    private static FlightOffer[] CreateFlightOffers(
        IEnumerable<FlightCheckInCheckOutOffer> flightsWithCheckInCheckOut)
    {
        return flightsWithCheckInCheckOut
            .Select(x => new FlightOffer
            {
                Id = x.FlightOffer.Key,
                DepartureDate = x.FlightOffer.DepartureDate,
                ReturnDepartureDate = x.FlightOffer.ReturnDepartureDate,
                ReturnArrivalDate = x.FlightOffer.ReturnArrivalDate,
                Flights = x
                    .FlightOffer
                    .Prices
                    .Select(p => new Flight
                    {
                        Id = p.Key,
                        Prices = []
                    })
                    .ToArray()
            })
            .ToArray();
    }

    private static FlightQuote[] CreateFlightQuotes(
        IEnumerable<FlightCheckInCheckOutOffer> flightsWithCheckInCheckOut, 
        PackagePipelineContext context)
    {
        return flightsWithCheckInCheckOut
            .SelectMany(x => x.FlightOffer.Prices)
            .Select(p => new FlightQuote
            {
                FlightId = p.Key,
                Prices = CreatePrices(p.Value, context),
                Currency = p.Value.Currency,
                UpdateTime = p.Value.RefreshDate
            })
            .ToArray();
    }

    private static Dictionary<PackageOccupancy, decimal> CreatePrices(
        FlightPriceDto flightPrice, 
        PackagePipelineContext context)
    {
        var occupancies = context.State.FlightOccupancies;

        return flightPrice.PaxConfigurations
            .Select(
                price => new
                {
                    Occupancy = new PackageOccupancy(
                        adults: price.Key.Adults,
                        youths: price.Key.Youths,
                        children: price.Key.Children,
                        infants: price.Key.Infants),
                    TotalPrice = price.Value.TotalPrice
                })
            .Where(x => occupancies.Contains(x.Occupancy))
            .ToDictionary(x => x.Occupancy, x => x.TotalPrice);
    }

    private record PackageFlightGroupingKey(
        DateOnly CheckIn,
        int StayLength,
        string DepartureAirport,
        string ArrivalAirport);

    private record FlightCheckInCheckOutOffer(FlightOfferDto FlightOffer, FlightCheckInCheckOut CheckInCheckOut)
    {
        public bool IsMatchingDefinitionStayLength(CheckInDurationStrategy checkInDurationStrategy)
        {
            return checkInDurationStrategy.StayLengths.Contains(CheckInCheckOut.StayLength);
        }
    }
}