using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

public class SelectFlightOccupanciesStage : PipelineStageBase<PackagePipelineContext>
{
    protected override Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var flightOccupancies = new HashSet<PackageOccupancy>();

        foreach (var occupancy in context.DefinitionParameters.Occupancies)
        {
            flightOccupancies.Add(occupancy);
        }

        foreach (var seats in Enumerable.Range(1, PackageFlight.MaxSupportedSeats))
        {
            flightOccupancies.Add(new PackageOccupancy(seats, 0, 0, 0));
        }
        
        context.State.FlightOccupancies = flightOccupancies.ToArray();
        
        context.Logger.LogInformation("Selected flight occupancies: {count}", context.State.FlightOccupancies.Length);
        
        return Task.CompletedTask;
    }
}