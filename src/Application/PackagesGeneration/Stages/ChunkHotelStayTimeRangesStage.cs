using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Application.PackagesGeneration.State.Components;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Process hotel stay time ranges in chunks.
/// </summary>
public class ChunkHotelStayTimeRangesStage(PipelineStageBase<PackagePipelineContext> stage) : PipelineStageBase<PackagePipelineContext>
{
    public int ChunkSize = 2;

    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var chunks = context.State.HotelStayTimeRanges.Chunk(ChunkSize).ToArray();
        context.Logger.LogInformation("Hotel stay time range chunks: {total}", chunks.Length);

        var chunk = 1;
        foreach (var timeRanges in chunks)
        {
            context.State.ChunkHotelStayTimeRangeState = new ChunkHotelStayTimeRangeState(timeRanges.ToList());

            context.Logger.LogInformation("Processing hotel stay time range chunk: {chunk}", chunk++);
            await stage.Run(context, cancellationToken);
        }
    }
}