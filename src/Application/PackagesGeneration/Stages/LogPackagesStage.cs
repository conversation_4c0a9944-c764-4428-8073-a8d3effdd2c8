using Esky.Packages.Application.Abstractions.Gateways.DataAnalyticsGateway;
using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.Packages;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

public class LogPackagesStage(IDataAnalyticsGateway dataAnalyticsGateway)
    : PipelineStageBase<PackagePipelineContext>
{
    private static readonly PackageOccupancy[] _allowedAnalyticsOccupancies = 
    [
        PackageOccupancy.Occupancy1A,
        PackageOccupancy.Occupancy2A,
        PackageOccupancy.Occupancy2AC1
    ];

    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        if (!context.DefinitionParameters.Occupancies.Contains(PackageOccupancy.Occupancy2A))
        {
            context.Logger.LogInformation("Skipping step - occupancy {occupancy} not present in a definition.", PackageOccupancy.Occupancy2A);
            return;
        }

        try
        {
            var packages = context.State.ChunkHotelStayTimeRangeState.PackageHotelRouteOffers
                .GroupBy(x => (x.CheckIn, x.StayLength, x.MetaCode))
                .Select(g => new GeneratedPackage
                {
                    Id = new PackageId(
                        checkIn: g.Key.CheckIn,
                        stayLength: g.Key.StayLength,
                        marketId: context.State.Market.Id,
                        metaCode: g.Key.MetaCode).ToString(),
                    Timestamp = DateTime.UtcNow,
                    DefinitionId = context.State.TargetDefinitionId,
                    MetaCode = g.Key.MetaCode,
                    CheckIn = g.Key.CheckIn,
                    StayLength = g.Key.StayLength,
                    Currency = context.State.Market.Currency,
                    HotelOffers = g.SelectMany(x => x.HotelOffers)
                        .DistinctBy(x => x.Id)
                        .SelectMany(s => s.GetLowestPrices()
                            .Where(o => _allowedAnalyticsOccupancies.Contains(o.Key)))
                        .SelectMany(m => m.Value.Select(p => new GeneratedPackage.HotelOffer
                        {
                            Occupancy = m.Key.ToOccupancy().ToString(),
                            MealPlan = p.Key.ToString(),
                            Price = p.Value
                        })).ToList(),
                    FlightOffers = g.SelectMany(f => f.PackageFlightHotelDetails
                        .SelectMany(p => p.Prices
                            .Where(x => _allowedAnalyticsOccupancies.Contains(x.Key))
                            .Select(fo => new GeneratedPackage.FlightOffer
                            {
                                Ids = fo.Value.FlightIds.ToList(),
                                DepartureAirport = p.DepartureAirport,
                                ArrivalAirport = p.ArrivalAirport,
                                DepartureDate = fo.Value.DepartureDate,
                                ReturnArrivalDate = fo.Value.ReturnArrivalDate,
                                Occupancy = fo.Key.ToOccupancy().ToString(),
                                Price = fo.Value.Price
                            }))).ToList()
                })
                .Where(x => x.HotelOffers.Count > 0 && x.FlightOffers.Count > 0);

            await dataAnalyticsGateway.LogGeneratedPackages(packages, cancellationToken);
        }
        catch (Exception ex)
        {
            context.Logger.LogError(ex, "Failed to log generated packages");
        }

        CleanUpPreviousStepState(context);
    }

    private void CleanUpPreviousStepState(PackagePipelineContext context)
    {
        context.State.ChunkHotelStayTimeRangeState.PackageHotelRouteOffers.Clear();
    }
}