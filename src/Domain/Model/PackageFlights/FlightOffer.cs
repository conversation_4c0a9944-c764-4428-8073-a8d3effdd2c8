using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageFlights;

public class FlightOffer
{
    public string Id { get; set; } = "";
    public DateTime DepartureDate { get; set; }
    public DateTime ReturnDepartureDate { get; set; }
    public DateTime ReturnArrivalDate { get; set; }
    public Flight[] Flights { get; set; } = default!;
    
    public TimeOfDay InboundDeparture => TimeOfDay.FromDateTime(ReturnDepartureDate);
    public TimeOfDay OutboundDeparture => TimeOfDay.FromDateTime(DepartureDate);
}