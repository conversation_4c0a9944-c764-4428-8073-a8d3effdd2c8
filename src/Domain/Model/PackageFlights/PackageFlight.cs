using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Policies;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Domain.Model.PackageFlights;

public class PackageFlight
{
    public const int MaxSupportedSeats = 9; // Maximum number of seats supported by the package flight
    
    private const int MaxFlightOffersPerOccupancy = 3;

    public PackageFlightId Id { get; private init; }
    public string DefinitionId { get; private init; } = null!;
    public PackageFlightPartitionKey PartitionKey { get; private init; }
    public string PartnerCode { get; private init; } = null!;
    public Currency Currency { get; private init; }
    public PackageOccupancy[] Occupancies { get; private set; } = null!;
    public FlightOffer[] FlightOffers { get; private set; } = null!;
    public int[] MetaCodes { get; private init; } = null!;
    public DateTime GeneratedAt { get; private init; }
    public DateTime UpdatedAt { get; private set; }

    private ICurrencyConversionPolicy _currencyConversionPolicy = null!;

    public static PackageFlight Create(PackageFlightId id, string definitionId, string partnerCode, Currency currency,
        PackageOccupancy[] occupancies, FlightOffer[] flights, FlightQuote[] quotes, int[] metaCodes,
        Action<PackageFlight>? configure = null)
    {
        if (flights.Length == 0 || quotes.Length == 0)
        {
            throw new ArgumentException("Flights and quotes must not be empty.");
        }

        var partitionKey = new PackageFlightPartitionKey(id.ArrivalAirport, id.DepartureAirport);

        var packageFlight = new PackageFlight
        {
            Id = id,
            DefinitionId = definitionId,
            PartitionKey = partitionKey,
            PartnerCode = partnerCode,
            Currency = currency,
            Occupancies = occupancies,
            FlightOffers = flights,
            MetaCodes = metaCodes,
            GeneratedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };

        configure?.Invoke(packageFlight);
        packageFlight.RequirePolicies();

        packageFlight.ApplyQuotes(quotes);
        packageFlight.LimitFlightOffers();

        return packageFlight;
    }

    public void ApplyPolicies(ICurrencyConversionPolicy currencyConversionPolicy)
    {
        _currencyConversionPolicy = currencyConversionPolicy;
    }

    // TODO: Unit test
    public bool Compare(PackageFlight packageFlight)
    {
        return Id == packageFlight.Id &&
               DefinitionId == packageFlight.DefinitionId &&
               PartnerCode == packageFlight.PartnerCode &&
               Occupancies.SequenceEqual(packageFlight.Occupancies) &&
               MetaCodes.SequenceEqual(packageFlight.MetaCodes) &&
               Currency == packageFlight.Currency &&
               CompareFlightOffers(packageFlight.FlightOffers);
    }

    public Dictionary<PackageOccupancy, FlightPriceEntry> GetLowestPricesByOccupancy()
    {
        var prices = GetLowestPricesByOccupancyInboundOutbound();

        return prices
            .GroupBy(p => p.Key.Occupancy)
            .ToDictionary(x => x.Key, x => x.MinBy(f => f.Value.Price).Value);
    }

    private Dictionary<(PackageOccupancy Occupancy, TimeOfDay InboundDeparture, TimeOfDay OutboundDeparture),
        FlightPriceEntry> GetLowestPricesByOccupancyInboundOutbound()
    {
        var prices = new Dictionary<(PackageOccupancy, TimeOfDay, TimeOfDay), FlightPriceEntry>();

        foreach (var occupancy in Occupancies)
        {
            foreach (var flightOffer in FlightOffers)
            {
                var inboundDeparture = flightOffer.InboundDeparture;
                var outboundDeparture = flightOffer.OutboundDeparture;

                var flightPrices = flightOffer.Flights
                    .SelectMany(f => f.Prices)
                    .Where(p => p.Key == occupancy)
                    .Select(p => p.Value)
                    .ToList();

                if (flightPrices.Count == flightOffer.Flights.Length)
                {
                    var price = flightPrices.Sum();
                    if (!prices.TryGetValue((occupancy, inboundDeparture, outboundDeparture), out var currentPrice) ||
                        currentPrice.Price > price)
                    {
                        prices[(occupancy, inboundDeparture, outboundDeparture)] = new FlightPriceEntry
                        {
                            Id = flightOffer.Id,
                            FlightIds = flightOffer.Flights.Select(f => f.Id).ToArray(),
                            Price = price,
                            DepartureDate = flightOffer.DepartureDate.ToDateOnly(),
                            ReturnDepartureDate = flightOffer.ReturnDepartureDate.ToDateOnly(),
                            ReturnArrivalDate = flightOffer.ReturnArrivalDate.ToDateOnly()
                        };
                    }
                }
            }
        }

        return prices;
    }

    public List<PackageFlightVariant> GetVariants()
    {
        var prices = GetLowestPricesByOccupancyInboundOutbound();

        return prices
            .Select(kvp => PackageFlightVariant.Create(
                checkIn: Id.CheckIn,
                stayLength: Id.StayLength,
                marketId: Id.MarketId,
                arrivalAirport: Id.ArrivalAirport,
                departureAirport: Id.DepartureAirport,
                packageOccupancy: kvp.Key.Occupancy,
                inboundDeparture: kvp.Key.InboundDeparture,
                outboundDeparture: kvp.Key.OutboundDeparture,
                departureDate: kvp.Value.DepartureDate,
                returnArrivalDate: kvp.Value.ReturnArrivalDate,
                price: (int)kvp.Value.Price))
            .ToList();
    }

    public List<PackageFlightVariant> GetVariants(PackageOccupancy occupancy)
    {
        return GetVariants().Where(v => v.Id.Occupancy.Equals(occupancy)).ToList();
    }

    public IEnumerable<string> GetFlightIds()
    {
        return FlightOffers
            .SelectMany(x => x.Flights)
            .Select(x => x.Id)
            .Distinct();
    }
    
    public IEnumerable<string> GetFlightOfferIds()
    {
        return FlightOffers
            .Select(x => x.Id)
            .Distinct();
    }

    public bool ApplyQuotes(Dictionary<string, FlightQuote> quotesByFlightId)
    {
        var updated = false;
        var flightsToUpdate = new List<Flight>();

        foreach (var flightOffer in FlightOffers)
        {
            foreach (var flight in flightOffer.Flights)
            {
                if (quotesByFlightId.ContainsKey(flight.Id))
                {
                    flightsToUpdate.Add(flight);
                }
            }
        }

        foreach (var flight in flightsToUpdate)
        {
            updated |= ApplyFlightQuote(flight, quotesByFlightId[flight.Id]);
        }

        if (updated)
        {
            UpdatedAt = DateTime.UtcNow;
        }

        return updated;
    }

    private void ApplyQuotes(FlightQuote[] quotes)
    {
        var quotesByFlightId = quotes
            .DistinctBy(x => x.FlightId)
            .ToDictionary(x => x.FlightId);

        ApplyQuotes(quotesByFlightId);
    }

    private bool ApplyFlightQuote(Flight flight, FlightQuote quote)
    {
        var updated = false;

        foreach (var occupancy in Occupancies)
        {
            var currentPrice = flight.Prices.TryGetValue(occupancy, out var cp) ? cp : (decimal?)null;
            var quotePrice = quote.Prices.TryGetValue(occupancy, out var qp) ? qp : (decimal?)null;
            var newPrice = quotePrice != null
                ? _currencyConversionPolicy.Convert((decimal)quotePrice, quote.Currency).RoundAwayFromZero()
                : (decimal?)null;

            if (!IsPriceEqual(currentPrice, newPrice) &&
                (flight.UpdatedAt == null || flight.UpdatedAt <= quote.UpdateTime))
            {
                if (newPrice != null)
                {
                    flight.Prices[occupancy] = (decimal)newPrice;
                }
                else
                {
                    flight.Prices.Remove(occupancy);
                }

                updated = true;
            }
        }

        if (updated)
        {
            flight.UpdatedAt = quote.UpdateTime;
        }

        return updated;
    }

    private void RequirePolicies()
    {
        if (_currencyConversionPolicy == null)
        {
            throw new InvalidOperationException("Currency conversion policy is required.");
        }
    }

    private void LimitFlightOffers()
    {
        if (FlightOffers.Length <= MaxFlightOffersPerOccupancy)
        {
            return;
        }

        var selectedFlightOfferIds = new HashSet<string>();
        var selectedFlightOffers = new List<FlightOffer>();

        foreach (var occupancy in Occupancies)
        {
            var offersWithPrices = FlightOffers
                .Select(offer => (
                    FlightOffer: offer,
                    Price: offer.Flights
                        .Where(f => f.Prices.ContainsKey(occupancy))
                        .Sum(f => f.Prices[occupancy])
                ))
                .Where(x => x.Price > 0)
                .OrderBy(x => x.Price)
                .Take(MaxFlightOffersPerOccupancy)
                .ToList();

            foreach (var (flightOffer, _) in offersWithPrices)
            {
                if (selectedFlightOfferIds.Add(flightOffer.Id))
                {
                    selectedFlightOffers.Add(flightOffer);
                }
            }
        }

        FlightOffers = selectedFlightOffers.ToArray();
    }

    private static bool IsPriceEqual(decimal? currentPrice, decimal? newPrice)
    {
        return currentPrice == newPrice;
    }

    private bool CompareFlightOffers(FlightOffer[] flightOffers)
    {
        return FlightOffers.Length == flightOffers.Length &&
               flightOffers.All(flightOffer => FlightOffers.Any(x => CompareFlightOffer(x, flightOffer)));

        static bool CompareFlightOffer(FlightOffer flightOfferA, FlightOffer flightOfferB) =>
            flightOfferA.Id == flightOfferB.Id &&
            flightOfferA.DepartureDate == flightOfferB.DepartureDate &&
            flightOfferA.ReturnArrivalDate == flightOfferB.ReturnArrivalDate &&
            flightOfferA.ReturnDepartureDate == flightOfferB.ReturnDepartureDate &&
            flightOfferA.Flights.Length == flightOfferB.Flights.Length &&
            CompareFlights(flightOfferA.Flights, flightOfferB.Flights);

        static bool CompareFlights(Flight[] flightsA, Flight[] flightsB) =>
            flightsB.All(flightB => flightsA.Any(flightA => CompareFlight(flightA, flightB)));

        static bool CompareFlight(Flight flightA, Flight flightB) =>
            flightA.Id == flightB.Id &&
            flightA.Prices.DeepEquals(flightB.Prices, (priceA, priceB) => IsPriceEqual(priceA, priceB));
    }

    public void AddOccupancy(PackageOccupancy packageOccupancy)
    {
        var occupancies = new HashSet<PackageOccupancy>(Occupancies);
        occupancies.Add(packageOccupancy);
        
        Occupancies = occupancies.ToArray();
    }
}