namespace Esky.Packages.Domain.Model.PackageFlights;

public record FlightPriceEntry
{
    public required string Id { get; init; }
    public required string[] FlightIds { get; init; }
    public required decimal Price { get; init; }
    public required DateOnly DepartureDate { get; init; }
    public required DateOnly ReturnDepartureDate { get; init; }
    public required DateOnly ReturnArrivalDate { get; init; }
}