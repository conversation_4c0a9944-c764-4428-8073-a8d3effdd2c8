using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageVariants;

public readonly struct PackageVariantId : IParsable<PackageVariantId>, IEquatable<PackageVariantId>
{
    private const char KeySeparator = ':';

    private readonly string _id;

    public DateOnly CheckIn { get; }
    public int StayLength { get; }
    public string MarketId { get; }
    public int MetaCode { get; }
    public PackageOccupancy Occupancy { get; }
    public Airport DepartureAirport { get; }
    public MealPlan MealPlan { get; }

    public PackageVariantId(string id)
    {
        _id = id;

        (CheckIn, StayLength, MarketId, MetaCode, Occupancy, DepartureAirport, MealPlan) =
            Parse(id, shouldThrow: true) ?? throw new FormatException("Invalid package variant id");
    }

    public PackageVariantId(DateOnly checkIn, int stayLength, string marketId, int metaCode, PackageOccupancy occupancy,
        Airport departureAirport, MealPlan mealPlan)
    {
        CheckIn = checkIn;
        StayLength = stayLength;
        MarketId = marketId;
        MetaCode = metaCode;
        Occupancy = occupancy;
        DepartureAirport = departureAirport;
        MealPlan = mealPlan;

        _id = string.Format(
            "{0}{1}{2}{3}{4}{5}{6}{7}{8}{9}{10}{11}{12}",
            checkIn.ToString("yyMMdd"), KeySeparator,
            stayLength, KeySeparator,
            marketId, KeySeparator,
            metaCode, KeySeparator,
            occupancy, KeySeparator,
            departureAirport, KeySeparator,
            mealPlan.ToShortString());
    }

    private static (DateOnly CheckIn, int StayLength, string MarketId, int MetaCode, PackageOccupancy Occupancy, 
        Airport DepartureAirport, MealPlan MealPlan)? Parse(string id, bool shouldThrow)
    {
        var t = id.AsSpan();

        var separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("CheckIn/StayLength date separator not found");
            return null;
        }

        if (!DateOnly.TryParseExact(t.Slice(0, separatorPosition), "yyMMdd", out var checkIn))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(checkIn)} date");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("StayLength/MarketId separator not found");
            return null;
        }

        if (!int.TryParse(t.Slice(0, separatorPosition), out var stayLength))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(stayLength)}");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("MarketId/MetaCode separator not found");
            return null;
        }

        var marketId = t.Slice(0, separatorPosition).ToString();

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("MetaCode/Occupancy separator not found");
            return null;
        }

        if (!int.TryParse(t.Slice(0, separatorPosition), out var metaCode))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(metaCode)}");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("Occupancy/DepartureAirport separator not found");
            return null;
        }

        if (!PackageOccupancy.TryParse(t.Slice(0, separatorPosition).ToString(), null, out var occupancy))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(occupancy)}");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("DepartureAirport/MealPlan separator not found");
            return null;
        }

        if (!Airport.TryParse(t.Slice(0, separatorPosition).ToString(), null, out var departureAirport))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(departureAirport)}");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        var mealPlan = MealPlan.FromShortString(t.ToString());

        return (checkIn, stayLength, marketId, metaCode, occupancy, departureAirport, mealPlan);
    }

    public static implicit operator PackageVariantId(string text)
        => new(text);

    public static implicit operator string(PackageVariantId packageVariantId)
        => packageVariantId._id;

    public static bool operator ==(PackageVariantId packageVariantId, PackageVariantId packageVariantId2)
        => packageVariantId._id == packageVariantId2._id;

    public static bool operator !=(PackageVariantId packageVariantId, PackageVariantId packageVariantId2)
        => !(packageVariantId == packageVariantId2);

    public override bool Equals(object? obj)
        => obj is PackageVariantId other && Equals(other);

    public override int GetHashCode()
        => _id.GetHashCode();

    public override string ToString()
        => _id;

    public static PackageVariantId Parse(string s, IFormatProvider? provider)
        => (PackageVariantId)s;

    public static bool TryParse(string? s, IFormatProvider? provider, out PackageVariantId result)
    {
        if (s != null && Parse(s, false) != null)
        {
            result = new PackageVariantId(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(PackageVariantId other)
    {
        return _id == other._id &&
               CheckIn == other.CheckIn &&
               StayLength == other.StayLength &&
               MarketId == other.MarketId &&
               MetaCode == other.MetaCode &&
               Occupancy == other.Occupancy &&
               DepartureAirport == other.DepartureAirport &&
               MealPlan == other.MealPlan;
    }
}