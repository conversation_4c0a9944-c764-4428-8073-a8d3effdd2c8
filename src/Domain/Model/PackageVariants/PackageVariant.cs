using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Model.PackageHotelOfferVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageVariants;

public record PackageVariant
{
    public PackageVariantId Id { get; private init; }
    public DateOnly DepartureDate { get; private init; }
    public DateOnly ReturnArrivalDate { get; private init; }
    public int Price { get; private init; }

    public static PackageVariant Create(DateOnly checkIn, int stayLength, string marketId, int metaCode,
        Airport departureAirport, PackageOccupancy occupancy, MealPlan mealPlan, DateOnly departureDate,
        DateOnly returnArrivalDate, decimal price)
    {
        var id = new PackageVariantId(checkIn, stayLength, marketId, metaCode, occupancy, departureAirport, mealPlan);
        var roundedPrice = Math.Round(price, MidpointRounding.ToEven);

        return new PackageVariant
        {
            Id = id,
            DepartureDate = departureDate,
            ReturnArrivalDate = returnArrivalDate,
            Price = roundedPrice > int.MaxValue ? int.MaxValue : (int)roundedPrice
        };
    }

    public static List<PackageVariant> CreateMany(List<PackageFlightVariant> packageFlightVariants,
        List<PackageHotelOfferVariant> packageHotelOfferVariants, 
        List<PackageHotelAirports.PackageHotelAirports> packageHotelAirports)
    {
        var packageVariants = new List<PackageVariant>();
        
        var airportsPerMetaCode = packageHotelOfferVariants
            .Join(packageHotelAirports, h => h.Id.MetaCode, a => a.Id.MetaCode, (h, a) => new { H = h, A = a })
            .GroupBy(x => x.H.Id.MetaCode)
            .ToDictionary(x => x.Key, x => x.SelectMany(g => g.A.Airports).Distinct().ToList());

        var groupedPackageHotelOfferVariants = packageHotelOfferVariants
            .GroupBy(h => h.Id.MetaCode)
            .ToDictionary(g => g.Key, g => g.ToList());
        
        var groupedPackageFlightVariants = packageFlightVariants
            .GroupBy(f => f.Id.ArrivalAirport)
            .ToDictionary(g => g.Key, g => g.ToList());

        foreach (var (metaCode, arrivalAirports) in airportsPerMetaCode)
        {
            var hotelOfferVariants = groupedPackageHotelOfferVariants
                .GetValueOrDefault(metaCode, new List<PackageHotelOfferVariant>());

            var flightVariants = arrivalAirports
                .SelectMany(a => groupedPackageFlightVariants.GetValueOrDefault(a, new List<PackageFlightVariant>()))
                .ToList();

            var variants = hotelOfferVariants.Join(flightVariants,
                h => new { h.Id.CheckIn, h.Id.StayLength, h.Id.Occupancy, h.Id.MarketId },
                f => new { f.Id.CheckIn, f.Id.StayLength, f.Id.Occupancy, f.Id.MarketId },
                (h, f) => Create(
                    f.Id.CheckIn,
                    f.Id.StayLength,
                    f.Id.MarketId,
                    metaCode,
                    f.Id.DepartureAirport,
                    f.Id.Occupancy,
                    h.Id.MealPlan,
                    f.DepartureDate,
                    f.ReturnArrivalDate,
                    h.Price + f.Price));

            packageVariants.AddRange(variants);
        }
        
        return packageVariants;
    }

    public static List<PackageVariant> CreateManyPerMetaCode(List<PackageFlightVariant> packageFlightVariants,
        List<PackageHotelOfferVariant> packageHotelOfferVariants, 
        List<PackageHotelAirports.PackageHotelAirports> packageHotelAirports)
    {
        var packageVariants = CreateMany(packageFlightVariants, packageHotelOfferVariants, packageHotelAirports);
        
        return packageVariants
            .GroupBy(v => v.Id.MetaCode)
            .Select(g => g.OrderBy(v => v.Price).First())
            .ToList();
    }
}