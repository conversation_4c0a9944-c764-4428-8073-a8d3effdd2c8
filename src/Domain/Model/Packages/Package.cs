using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Model.PackageVariants;
using Airport = Esky.Packages.Domain.Types.Airport;

namespace Esky.Packages.Domain.Model.Packages;

public class Package
{
    public required PackageId Id { get; set; }
    public required Dictionary<PackageOccupancy, Dictionary<Types.MealPlan, decimal>> HotelOfferPrices { get; set; } = new();
    public required Dictionary<Airport, Dictionary<Airport, Dictionary<PackageOccupancy, FlightPriceEntry>>> FlightPricesByOccupancyByDepartureByArrival { get; set; } = new();
    public DateTime? RemoveAt { get; set; }

    public static Package Create(
        PackageId id,
        PackageHotelOffer packageHotelOffer,
        IReadOnlyCollection<PackageFlight> packageFlights)
    {
        var flightPrices = new Dictionary<Airport, Dictionary<Airport, Dictionary<PackageOccupancy, FlightPriceEntry>>>();
        var hotelOfferPrices = packageHotelOffer.GetLowestPrices();

        foreach (var packageFlight in packageFlights)
        {
            var arrivalAirport = packageFlight.Id.ArrivalAirport;
            var departureAirport = packageFlight.Id.DepartureAirport;

            if (!flightPrices.TryGetValue(arrivalAirport, out var departureAirports))
            {
                departureAirports = new Dictionary<Airport, Dictionary<PackageOccupancy, FlightPriceEntry>>();
                flightPrices[arrivalAirport] = departureAirports;
            }

            flightPrices[arrivalAirport][departureAirport] = MapPrices(packageFlight.GetLowestPricesByOccupancy());
        }

        return new Package
        {
            Id = id,
            HotelOfferPrices = hotelOfferPrices,
            FlightPricesByOccupancyByDepartureByArrival = flightPrices
        };
    }
    
    public IEnumerable<(Airport DepartureAirport, PackageOccupancy Occupancy, FlightPriceEntry PriceEntry)> GetLowestPricesPerDepartureAirportAndOccupancy()
    {
        return FlightPricesByOccupancyByDepartureByArrival
            .SelectMany(arrivalAirportPair => arrivalAirportPair.Value
                .SelectMany(departureAirportPair => departureAirportPair.Value
                    .Select(occupancyPair => (DepartureAirport: departureAirportPair.Key, Occupancy: occupancyPair.Key,
                        PriceEntry: occupancyPair.Value))))
            .GroupBy(f => (f.DepartureAirport, f.Occupancy))
            .ToDictionary(f => f.Key, f => f.MinBy(p => p.PriceEntry.Price).PriceEntry)
            .Select(departureAirportOccupancyPair => (
                DepartureAirport: departureAirportOccupancyPair.Key.DepartureAirport,
                Occupancy: departureAirportOccupancyPair.Key.Occupancy,
                PriceEntry: departureAirportOccupancyPair.Value));
    }

    public IEnumerable<(PackageOccupancy Occupancy, Types.MealPlan MealPlan, decimal Price)> GetLowestPricesPerOccupancyAndMealPlan()
    {
        return HotelOfferPrices
            .SelectMany(occupancyPair => occupancyPair.Value.Select(mealPlanPair =>
                (Occupancy: occupancyPair.Key, MealPlan: mealPlanPair.Key, Price: mealPlanPair.Value)));
    }

    public List<PackageVariant> GetVariants()
    {
        var hotelOffers = GetLowestPricesPerOccupancyAndMealPlan();
        var flightOffers = GetLowestPricesPerDepartureAirportAndOccupancy();

        return hotelOffers.Join(flightOffers, h => h.Occupancy, f => f.Occupancy,
            (h, f) => PackageVariant.Create(
                checkIn: Id.CheckIn,
                stayLength: Id.StayLength,
                marketId: Id.MarketId,
                metaCode: Id.MetaCode,
                departureAirport: f.DepartureAirport,
                occupancy: h.Occupancy,
                mealPlan: h.MealPlan,
                departureDate: f.PriceEntry.DepartureDate,
                returnArrivalDate: f.PriceEntry.ReturnArrivalDate,
                price: h.Price + f.PriceEntry.Price)).ToList();
    }

    public static Dictionary<PackageOccupancy, FlightPriceEntry> MapPrices(Dictionary<PackageOccupancy, PackageFlights.FlightPriceEntry> prices)
    {
        return prices.ToDictionary(
            kvp => kvp.Key,
            kvp => new FlightPriceEntry
            {
                Price = kvp.Value.Price,
                DepartureDate = kvp.Value.DepartureDate,
                ReturnArrivalDate = kvp.Value.ReturnArrivalDate
            });
    }
}