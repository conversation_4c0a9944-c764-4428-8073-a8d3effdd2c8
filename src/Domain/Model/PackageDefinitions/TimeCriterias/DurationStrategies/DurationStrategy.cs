using System.Text.Json.Serialization;

namespace Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias.DurationStrategies;

[JsonDerivedType(typeof(CheckInDurationStrategy), "checkIn")]
[JsonDerivedType(typeof(RollingCheckInDurationStrategy), "rollingCheckIn")]
[JsonPolymorphic(TypeDiscriminatorPropertyName = "_t")]
public abstract class DurationStrategy
{
    public T AsDerivedOrThrow<T>() where T : DurationStrategy
    {
        if (this is not T casted)
        {
            throw new InvalidCastException($"Cannot cast {GetType().Name} to {typeof(T).Name}");
        }

        return casted;
    }
}