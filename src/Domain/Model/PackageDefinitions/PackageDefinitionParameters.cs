using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageDefinitions.FlightCriterias;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors.Combine;
using Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias;

namespace Esky.Packages.Domain.Model.PackageDefinitions;

public record PackageDefinitionParameters
{
    public string MarketId { get; init; } = "pl";
    public string PartnerCode { get; init; } = "ESKYPLPACKAGES";

    public HotelSelector HotelSelector { get; init; } = HotelNullSelector.Instance;
    public HotelSelector MergeIntoPackageHotelSelector { get; init; } = new HotelNotSelector(HotelNullSelector.Instance);
    public TimeCriteria TimeCriteria { get; init; } = new();
    public FlightCriteria FlightCriteria { get; init; } = new();
    public PackageOccupancy[] Occupancies { get; init; } = [];
}