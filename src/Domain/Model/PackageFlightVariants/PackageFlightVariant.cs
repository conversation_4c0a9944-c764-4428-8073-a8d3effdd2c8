using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageFlightVariants;

public class PackageFlightVariant
{
    public PackageFlightVariantId Id { get; private init; }
    public DateOnly DepartureDate { get; private init; }
    public DateOnly ReturnArrivalDate { get; private init; }
    public int Price { get; private init; }
    
    public static PackageFlightVariant Create(DateOnly checkIn, int stayLength, string marketId, Airport arrivalAirport,
        Airport departureAirport, PackageOccupancy packageOccupancy, TimeOfDay inboundDeparture, 
        TimeOfDay outboundDeparture, DateOnly departureDate, DateOnly returnArrivalDate, int price)
    {
        return new PackageFlightVariant
        {
            Id = new PackageFlightVariantId(checkIn, stayLength, marketId, arrivalAirport, departureAirport, 
                packageOccupancy, inboundDeparture, outboundDeparture),
            DepartureDate = departureDate,
            ReturnArrivalDate = returnArrivalDate,
            Price = price
        };
    }
    
    public PackageFlightId GetPackageFlightId()
    {
        return new PackageFlightId(Id.CheckIn, Id.StayLength, Id.MarketId, Id.ArrivalAirport, Id.DepartureAirport);
    }
}