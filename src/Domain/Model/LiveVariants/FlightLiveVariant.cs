using Esky.Packages.Domain.Policies;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Domain.Model.LiveVariants;

public class FlightLiveVariant
{
    private const decimal CompensationPercentage = 0.1M;

    public string Key { get; private set; } = null!;
    public string DepartureAirport { get; private set; } = null!;
    public string ArrivalAirport { get; private set; } = null!;
    public DateTime DepartureDate { get; private set; }
    public DateTime ArrivalDate { get; private set; }
    public DateTime ReturnDepartureDate { get; private set; }
    public DateTime ReturnArrivalDate { get; private set; }
    public int Stops { get; private set; }
    public int ProviderCode { get; private set; }
    public string[] AirlineCodes { get; private set; } = [];
    public string[] FlightIds { get; private set; } = [];
    public string[] LegLocators { get; set; } = [];
    public Currency Currency { get; private set; }
    public decimal Price { get; private set; }
    public bool RegisteredBaggageIncluded { get; private set; }
    public bool IsSelected { get; private set; } = false;

    private ICurrencyConversionPolicy _currencyConversionPolicy = null!;

    private FlightLiveVariant()
    {
    }

    public static FlightLiveVariant Create(
        string key,
        string departureAirport,
        string arrivalAirport,
        DateTime departureDate,
        DateTime arrivalDate,
        DateTime returnDepartureDate,
        DateTime returnArrivalDate,
        int stops,
        int providerCode,
        string[] airlineCodes,
        string[] flightIds,
        string[] legLocators,
        Currency currency,
        Money[] prices,
        bool registeredBaggageIncluded,
        Action<FlightLiveVariant>? configure = null)
    {
        var alternativeFlight = new FlightLiveVariant
        {
            Key = key,
            DepartureAirport = departureAirport,
            ArrivalAirport = arrivalAirport,
            DepartureDate = departureDate,
            ArrivalDate = arrivalDate,
            ReturnDepartureDate = returnDepartureDate,
            ReturnArrivalDate = returnArrivalDate,
            Stops = stops,
            ProviderCode = providerCode,
            AirlineCodes = airlineCodes,
            FlightIds = flightIds,
            LegLocators = legLocators,
            Currency = currency,
            RegisteredBaggageIncluded = registeredBaggageIncluded,
            IsSelected = false
        };

        configure?.Invoke(alternativeFlight);
        alternativeFlight.RequirePolicies();

        alternativeFlight.ApplyPrices(prices);

        return alternativeFlight;
    }

    private void RequirePolicies()
    {
        if (_currencyConversionPolicy == null)
        {
            throw new InvalidOperationException("Currency conversion policy is required.");
        }
    }

    private void ApplyPrices(Money[] prices)
    {
        Price = prices.Sum(p => _currencyConversionPolicy.Convert(p.Value, p.Currency).RoundAwayFromZero());
    }

    public void ApplyPolicies(ICurrencyConversionPolicy currencyConversionPolicy)
    {
        _currencyConversionPolicy = currencyConversionPolicy;
    }

    public void MarkAsSelected()
    {
        IsSelected = true;
    }

    public void UpdateLivePrice(Money price, string[] legLocators)
    {
        Price = _currencyConversionPolicy.Convert(price.Value, price.Currency).RoundAwayFromZero();
        LegLocators = legLocators;
    }

    public decimal Compensate(
        decimal? lastCompensatedPrice,
        bool onlyUp = false)
    {
        if (lastCompensatedPrice is null)
        {
            return Price;
        }

        if (Price <= lastCompensatedPrice)
        {
            return lastCompensatedPrice.Value;
        }

        var lowerPercent = onlyUp ? 1m : 1m - CompensationPercentage;
        var upperPercent = 1m + CompensationPercentage;

        return Compensate(lastCompensatedPrice.Value, lowerPercent, upperPercent);
    }

    private decimal Compensate(
        decimal lastCompensatedPrice,
        decimal lowerPercent,
        decimal upperPercent)
    {
        if (!IsInRange(
                target: lastCompensatedPrice,
                lower: Price * lowerPercent,
                upper: Price * upperPercent))
        {
            return Price;
        }

        return lastCompensatedPrice;
    }

    private static bool IsInRange(decimal target, decimal lower, decimal upper)
    {
        if (target < lower)
        {
            return false;
        }

        if (target > upper)
        {
            return false;
        }

        return true;
    }
}