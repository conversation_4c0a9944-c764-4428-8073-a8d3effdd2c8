namespace Esky.Packages.Domain.Model.LiveVariants;

public class FlightLiveVariants
{
    public List<FlightLiveVariant> Flights { get; private set; } = [];

    private FlightLiveVariants()
    {
    }

    public static FlightLiveVariants Create(IEnumerable<FlightLiveVariant> flights)
    {
        return new FlightLiveVariants
        {
            Flights = flights
                .DistinctBy(x => x.Key)
                .ToList()
        };
    }

    public FlightLiveVariant? SelectFlight(
        string[]? selectedDepartureAirports,
        string? selectedFlightOptionId,
        string? preferredDepartureAirport)
    {
        FlightLiveVariant? flightVariant;

        if (selectedFlightOptionId != null)
        {
            flightVariant = Flights.FirstOrDefault(f => f.Key == selectedFlightOptionId);
            if (flightVariant != null)
            {
                return flightVariant;
            }
        }

        if (preferredDepartureAirport != null)
        {
            flightVariant = Flights
                .Where(f => f.DepartureAirport == preferredDepartureAirport)
                .OrderBy(f => f.Price)
                .FirstOrDefault();

            if (flightVariant != null)
            {
                return flightVariant;
            }
        }

        if (selectedDepartureAirports != null)
        {
            flightVariant = Flights
                .Where(f => selectedDepartureAirports.Contains(f.DepartureAirport))
                .OrderBy(f => f.Price)
                .FirstOrDefault();

            if (flightVariant != null)
            {
                return flightVariant;
            }
        }

        return Flights.OrderBy(f => f.Price).FirstOrDefault();
    }

    public void RemoveSoldOutFlight(FlightLiveVariant flightLiveVariant)
    {
        if (!Flights.Contains(flightLiveVariant))
        {
            throw new InvalidOperationException($"Alternative flight with key {flightLiveVariant.Key} is not availabile in flight list.");
        }

        Flights.Remove(flightLiveVariant);
    }

    public Dictionary<string, List<FlightLiveVariant>> GetFlightsByDepartureAirport()
    {
        return Flights
            .GroupBy(f => f.DepartureAirport)
            .ToDictionary(
                g => g.Key,
                g => g.OrderBy(v => v.Price).ToList());
    }
}
