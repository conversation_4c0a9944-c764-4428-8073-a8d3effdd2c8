using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.LiveVariants;

public class HotelOfferLiveVariants
{
    public List<HotelOfferLiveVariant> HotelOffers { get; private set; } = [];
    public bool OnlyRefundable { get; private set; }

    private HotelOfferLiveVariants()
    {
    }

    public static HotelOfferLiveVariants CreateCheapestHotelOfferVariantsByRooms(
        List<Dictionary<string, List<HotelOfferLiveVariant>>> hotelOfferVariantsGroupedByRooms,
        bool onlyRefundable)
    {
        var hotelOfferVariants = new List<HotelOfferLiveVariant>();

        foreach (var hotelOfferVariantsByRoom in hotelOfferVariantsGroupedByRooms)
        {
            var cheapestRoom = hotelOfferVariantsByRoom
                .SelectMany(g => g.Value
                    .Where(v => !onlyRefundable || v.Refundability.IsRefundable)
                    .Select(v => (Room: g.Key, Variant: v)))
                .OrderBy(t => t.Variant.Price)
                .FirstOrDefault();

            if (cheapestRoom != default)
            {
                hotelOfferVariants.AddRange(hotelOfferVariantsByRoom[cheapestRoom.Room].Where(v => !onlyRefundable || v.Refundability.IsRefundable));
            }
        }

        return new HotelOfferLiveVariants
        {
            HotelOffers = hotelOfferVariants,
            OnlyRefundable = onlyRefundable
        };
    }

    public Dictionary<MealPlan, List<HotelOfferLiveVariant>> GetHotelOffersByMealPlan()
    {
        return HotelOffers
            .GroupBy(f => f.MealPlan)
            .ToDictionary(
                g => g.Key,
                g => g.OrderBy(v => v.Price).ToList());
    }
}