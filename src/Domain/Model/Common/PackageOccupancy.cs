using System.Text;
using System.Text.RegularExpressions;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.Common;

public readonly struct PackageOccupancy : IParsable<PackageOccupancy>, IEquatable<PackageOccupancy>
{
    private static readonly Regex _occupancyRegex = new(@"^A(?<adults>\d+)(Y(?<youth>\d+))?(C(?<children>\d+))?(I(?<infants>\d+))?$", RegexOptions.Compiled);

    public readonly int Adults;
    public readonly int Youths;
    public readonly int Children;
    public readonly int Infants;
    
    public const int InfantAge = 1;
    public const int ChildAge = 11;
    public const int YouthAge = 17;

    public static readonly PackageOccupancy Occupancy1A = new(adults: 1, youths: 0, children: 0, infants: 0);
    public static readonly PackageOccupancy Occupancy2A = new(adults: 2, youths: 0, children: 0, infants: 0);
    public static readonly PackageOccupancy Occupancy2AC1 = new(adults: 2, youths: 0, children: 1, infants: 0);

    public PackageOccupancy(int adults, int youths, int children, int infants)
    {
        if (adults < 1)
        {
            throw new ArgumentOutOfRangeException(nameof(adults), "At least one adult is required");
        }

        if (youths < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(youths), "Youths count cannot be negative");
        }

        if (children < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(children), "Children count cannot be negative");
        }

        if (infants < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(infants), "Infants count cannot be negative");
        }

        if (youths + children + infants > 5)
        {
            throw new ArgumentException("Total of youths, children and infants cannot exceed 5.");
        }
        
        if (adults + youths + children + infants > 9) 
        {
            throw new ArgumentException("Total of adults, youths, children and infants cannot exceed 9.");
        }

        Adults = adults;
        Youths = youths;
        Children = children;
        Infants = infants;
    }

    public static PackageOccupancy? Map(Occupancy occupancy, bool shouldThrow = false)
    {
        var adults = occupancy.Adults;
        var youths = occupancy.ChildrenAges.Count(c => c is > ChildAge and <= YouthAge);
        var children = occupancy.ChildrenAges.Count(c => c is > InfantAge and <= ChildAge);
        var infants = occupancy.ChildrenAges.Count(c => c <= InfantAge);
        
        switch (adults, youths, children, infants)
        {
            case (1, 0, 0, 0): // A1
                return new PackageOccupancy(adults: 1, youths: 0, children: 0, infants: 0); 
            case (1, 1, 0, 0): // A1Y1
                return new PackageOccupancy(adults: 1, youths: 1, children: 0, infants: 0);
            case (1, 0, 1, 0): // A1C1
            case (1, 0, 0, 1):
                return new PackageOccupancy(adults: 1, youths: 0, children: 1, infants: 0);
            case (2, 0, 0, 0): // A2
                return new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0);
            case (2, 1, 0, 0): // A2Y1
                return new PackageOccupancy(adults: 2, youths: 1, children: 0, infants: 0);
            case (2, 0, 1, 0): // A2C1
                return new PackageOccupancy(adults: 2, youths: 0, children: 1, infants: 0);
            case (2, 0, 0, 1): // A2I1
                return new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 1);
            case (2, 2, 0, 0): // A2Y2
                return new PackageOccupancy(adults: 2, youths: 2, children: 0, infants: 0);
            case (2, 1, 1, 0): // A2Y1C1
            case (2, 1, 0, 1):
                return new PackageOccupancy(adults: 2, youths: 1, children: 1, infants: 0);
            case (2, 0, 2, 0): // A2C2
            case (2, 0, 1, 1):
            case (2, 0, 0, 2):
                return new PackageOccupancy(adults: 2, youths: 0, children: 2, infants: 0);
            case (3, 0, 0, 0): // A3
                return new PackageOccupancy(adults: 3, youths: 0, children: 0, infants: 0);
            case (4, 0, 0, 0): // A4
                return new PackageOccupancy(adults: 4, youths: 0, children: 0, infants: 0);
            default:
                if (shouldThrow) 
                {
                    throw new ArgumentException("Invalid occupancy configuration.");
                }

                return null;
        }
    }
    
    public static PackageOccupancy? MapExact(Occupancy occupancy, bool shouldThrow = false)
    {
        var adults = occupancy.Adults;
        var youths = occupancy.ChildrenAges.Count(c => c is > ChildAge and <= YouthAge);
        var children = occupancy.ChildrenAges.Count(c => c is > InfantAge and <= ChildAge);
        var infants = occupancy.ChildrenAges.Count(c => c <= InfantAge);
        
        try
        {
            return new PackageOccupancy(adults: adults, youths: youths, children: children, infants: infants);
        }
        catch (Exception e)
        {
            if (shouldThrow)
            {
                throw new ArgumentException("Invalid occupancy configuration.", e);
            }
            
            return null;
        }
    }

    public Occupancy ToOccupancy()
    {
        var childrenAges = new List<int>();
        
        var adults = Adults;

        if (Youths > 0)
        {
            childrenAges.AddRange(Enumerable.Repeat(YouthAge, Youths));
        }
        if (Children > 0)
        {
            childrenAges.AddRange(Enumerable.Repeat(ChildAge, Children));
        }
        if (Infants > 0)
        {
            childrenAges.AddRange(Enumerable.Repeat(InfantAge, Infants));
        }

        return new Occupancy(adults, childrenAges.OrderDescending().ToArray());
    }
    
    public override string ToString()
    {
        var sb = new StringBuilder();
        
        sb.Append("A");
        sb.Append(Adults);
        
        if (Youths > 0)
        {
            sb.Append("Y");
            sb.Append(Youths);
        }
        
        if (Children > 0)
        {
            sb.Append("C");
            sb.Append(Children);
        }
        
        if (Infants > 0)
        {
            sb.Append("I");
            sb.Append(Infants);
        }
        
        return sb.ToString();
    }

    public static implicit operator PackageOccupancy(string text)
        => Parse(text, provider: null);

    public static implicit operator string(PackageOccupancy packageOccupancy)
        => packageOccupancy.ToString();

    public static bool operator ==(PackageOccupancy packageOccupancy, PackageOccupancy occupancy2)
        => packageOccupancy.Equals(occupancy2);

    public static bool operator !=(PackageOccupancy packageOccupancy, PackageOccupancy occupancy2)
        => !packageOccupancy.Equals(occupancy2);

    public override bool Equals(object? obj)
        => obj is PackageOccupancy other && Equals(other);

    public override int GetHashCode()
        => HashCode.Combine(Adults, Youths, Children, Infants);

    public static PackageOccupancy Parse(string s, IFormatProvider? provider)
    {
        if (string.IsNullOrWhiteSpace(s))
        {
            throw new ArgumentNullException(nameof(s));
        }

        var match = _occupancyRegex.Match(s);
        var adults = int.Parse(match.Groups["adults"].Value);
        var youths = match.Groups["youth"].Success ? int.Parse(match.Groups["youth"].Value) : 0;
        var children = match.Groups["children"].Success ? int.Parse(match.Groups["children"].Value) : 0;
        var infants = match.Groups["infants"].Success ? int.Parse(match.Groups["infants"].Value) : 0;

        return new PackageOccupancy(adults, youths, children, infants);
    }

    public static bool TryParse(string? s, IFormatProvider? provider, out PackageOccupancy result)
    {
        if (s != null && _occupancyRegex.IsMatch(s))
        {
            result = Parse(s, provider);
            return true;
        }

        result = default;
        return false;
    }

    public PackageOccupancy ToFlightSeatsOccupancy()
    {
        return new PackageOccupancy(Adults + Youths + Children, 0, 0, 0);
    }

    public bool Equals(PackageOccupancy other)
    {
        return Adults == other.Adults && 
               Youths == other.Youths && 
               Children == other.Children && 
               Infants == other.Infants;
    }

    public static bool IsSupportedInPrecomputedPackage(Occupancy occupancy)
    {
        foreach (var childrenAge in occupancy.ChildrenAges)
        {
            if (childrenAge != InfantAge &&
                childrenAge != ChildAge && 
                childrenAge != YouthAge)
            {
                return false;
            }
        }

        var packageOccupancy = Map(occupancy, shouldThrow: false);
        
        if (packageOccupancy == null)
        {
            return false;
        }

        return occupancy == ((PackageOccupancy)packageOccupancy).ToOccupancy();
    }
}