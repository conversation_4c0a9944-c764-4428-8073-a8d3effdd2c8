using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Model.PackageHotelOfferVariants;
using Esky.Packages.Domain.Policies;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Domain.Model.PackageHotelOffers;

public class PackageHotelOffer
{
    private const decimal CompensationPercentage = 0.01M;
    private const int DaysToCheckInToStoreNotRefundablePrices = 14;
    
    public PackageHotelOfferId Id { get; private init; }
    public string DefinitionId { get; private init; } = null!;
    public Currency Currency { get; private init; }
    public PackageOccupancy[] Occupancies { get; private init; } = null!;
    public string[] ProviderConfigurationIds { get; private init; } = null!;
    public bool OnlyRefundable { get; private set; }
    public bool MergeIntoPackage { get; private init; } = true;
    public Dictionary<string, Dictionary<PackageOccupancy, Dictionary<MealPlan, decimal>>> Prices { get; private init; } = new();
    public Dictionary<string, Dictionary<PackageOccupancy, DateTime>> PricesUpdatedAt { get; private init; } = new();
    public Dictionary<Airport, Airport[]> Airports { get; private init; } = null!; // <Arrival, Departure[]>
    public DateTime GeneratedAt { get; private init; }
    public DateTime UpdatedAt { get; private set; }

    private ICurrencyConversionPolicy _currencyConversionPolicy = null!;

    public static PackageHotelOffer Create(PackageHotelOfferId id, string definitionId, Currency currency, 
        PackageOccupancy[] occupancies, string[] providerConfigurationIds, Dictionary<Airport, Airport[]> airports, 
        HotelOfferQuote[] quotes, bool mergeIntoPackage, Action<PackageHotelOffer>? configure = null)
    {
        var onlyRefundable = id.CheckIn.DayNumber - DateOnly.FromDateTime(DateTime.UtcNow).DayNumber >
                             DaysToCheckInToStoreNotRefundablePrices;

        var packageHotelOffer = new PackageHotelOffer
        {
            Id = id,
            DefinitionId = definitionId,
            Currency = currency,
            Occupancies = occupancies,
            ProviderConfigurationIds = providerConfigurationIds,
            OnlyRefundable = onlyRefundable,
            MergeIntoPackage = mergeIntoPackage,
            Prices = [],
            Airports = airports,
            GeneratedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };
        
        configure?.Invoke(packageHotelOffer);
        packageHotelOffer.RequirePolicies();
        
        packageHotelOffer.ApplyQuotes(quotes);
        
        return packageHotelOffer;
    }
    
    public void ApplyPolicies(ICurrencyConversionPolicy currencyConversionPolicy)
    {
        _currencyConversionPolicy = currencyConversionPolicy;
    }

    // TODO: Unit test
    public bool Compare(PackageHotelOffer packageHotelOffer)
    {
        return Id == packageHotelOffer.Id &&
               DefinitionId == packageHotelOffer.DefinitionId &&
               Currency == packageHotelOffer.Currency &&
               MergeIntoPackage == packageHotelOffer.MergeIntoPackage &&
               Occupancies.SequenceEqual(packageHotelOffer.Occupancies) &&
               ProviderConfigurationIds.SequenceEqual(packageHotelOffer.ProviderConfigurationIds) &&
               OnlyRefundable == packageHotelOffer.OnlyRefundable &&
               Airports.DeepEquals(packageHotelOffer.Airports, (current, other) => current.SequenceEqual(other)) &&
               Prices.DeepEquals(packageHotelOffer.Prices, IsCompensatedPriceEqual);
    }
    
    public PackageHotelOfferStayKey GetStayKey()
    {
        return new PackageHotelOfferStayKey(Id.MetaCode, Id.CheckIn, Id.StayLength);
    }

    public Dictionary<PackageOccupancy, Dictionary<MealPlan, decimal>> GetLowestPrices()
    {
        return Prices
            .SelectMany(providerCodePair => providerCodePair.Value
                .SelectMany(occupancyPair => occupancyPair.Value
                    .Select(mealPlanPair => (ProviderCode: providerCodePair.Key, Occupancy: occupancyPair.Key,
                        MealPlan: mealPlanPair.Key, Price: mealPlanPair.Value))))
            .GroupBy(x => x.Occupancy)
            .ToDictionary(o => o.Key, o => o
                .GroupBy(y => y.MealPlan)
                .ToDictionary(m => m.Key, m => m
                    .MinBy(z => z.Price).Price));
    }

    public List<PackageHotelOfferVariant> GetVariants(List<PackageOccupancy>? occupancies, List<MealPlan>? mealPlans)
    {
        var variants = GetLowestPrices()
            .SelectMany(occupancyEntry => occupancyEntry.Value
                .Select(mealPlanEntry => PackageHotelOfferVariant.Create(
                    checkIn: Id.CheckIn,
                    stayLength: Id.StayLength,
                    marketId: Id.MarketId,
                    metaCode: Id.MetaCode,
                    occupancy: occupancyEntry.Key,
                    mealPlan: mealPlanEntry.Key,
                    price: (int)Math.Round(mealPlanEntry.Value, MidpointRounding.AwayFromZero))));
        
        if (occupancies is { Count: > 0 }) 
        {
            variants = variants.Where(v => occupancies.Contains(v.Id.Occupancy));
        }
        
        if (mealPlans is { Count: > 0 }) 
        {
            variants = variants.Where(v => mealPlans.Contains(v.Id.MealPlan));
        }
        
        return variants.ToList();
    }

    // TODO: Unit test
    public bool ApplyQuotes(Dictionary<PackageHotelOfferStayKey, List<HotelOfferQuote>> quotesByStayKey)
    {
        var updated = false;

        if (Id.CheckIn.DayNumber - DateOnly.FromDateTime(DateTime.UtcNow).DayNumber <=
            DaysToCheckInToStoreNotRefundablePrices)
        {
            OnlyRefundable = false;
        }

        if (quotesByStayKey.TryGetValue(new PackageHotelOfferStayKey(Id.MetaCode, Id.CheckIn, Id.StayLength), 
                out var quotes))
        {
            foreach (var quote in quotes)
            {
                updated |= ApplyQuote(quote);
            }
        }
        
        if (updated)
        {
            UpdatedAt = DateTime.UtcNow;
        }

        return updated;
    }

    private void ApplyQuotes(HotelOfferQuote[] quotes)
    {
        if (Id.CheckIn.DayNumber - DateOnly.FromDateTime(DateTime.UtcNow).DayNumber <=
            DaysToCheckInToStoreNotRefundablePrices)
        {
            OnlyRefundable = false;
        }

        if (OnlyRefundable && !HasAnyRefundablePrice(quotes))
        {
            OnlyRefundable = false;
        }

        foreach (var quote in quotes)
        {
            ApplyQuote(quote);
        }
    }
    
    private bool ApplyQuote(HotelOfferQuote quote)
    {
        if (!ProviderConfigurationIds.Contains(quote.ProviderConfigurationId))
        {
            return false;
        }

        if (!Occupancies.Contains(quote.Occupancy))
        {
            return false;
        }

        var currentProviderPrices = Prices.GetValueOrDefault(quote.ProviderConfigurationId,
            new Dictionary<PackageOccupancy, Dictionary<MealPlan, decimal>>());
        var currentOccupancyPrices = currentProviderPrices
            .GetValueOrDefault(quote.Occupancy, new Dictionary<MealPlan, decimal>());

        var newProviderPrices = quote.Prices
            .SelectMany(mealPlanPair => mealPlanPair.Value
                .Select(refundabilityPair => (
                    MealPlan: mealPlanPair.Key,
                    Refundability: refundabilityPair.Key,
                    Price: _currencyConversionPolicy
                        .Convert(
                            refundabilityPair.Value.Value,
                            refundabilityPair.Value.Currency)
                        .RoundAwayFromZero())))
            .Where(x => !OnlyRefundable || x.Refundability.IsRefundable)
            .GroupBy(x => x.MealPlan)
            .ToDictionary(m => m.Key, m => m
                .MinBy(g => g.Price).Price);

        // compare prices with compensation
        if (currentOccupancyPrices.DeepEquals(newProviderPrices, IsCompensatedPriceEqual))
        {
            return false;
        }
        
        // check if the quote isn't outdated
        var currentUpdatedAt = PricesUpdatedAt.GetValueOrDefault(quote.ProviderConfigurationId, 
            new Dictionary<PackageOccupancy, DateTime>());
        if (currentUpdatedAt.TryGetValue(quote.Occupancy, out var updatedAt) && quote.UpdatedAt <= updatedAt)
        {
            return false;
        }

        // update prices
        currentProviderPrices[quote.Occupancy] = newProviderPrices;
        Prices[quote.ProviderConfigurationId] = currentProviderPrices;
        
        // update updated at
        currentUpdatedAt[quote.Occupancy] = quote.UpdatedAt;
        PricesUpdatedAt[quote.ProviderConfigurationId] = currentUpdatedAt;

        return true;
    }
    
    private void RequirePolicies()
    {
        if (_currencyConversionPolicy == null)
        {
            throw new InvalidOperationException("Currency conversion policy is required.");
        }
    }
    
    private static bool IsCompensatedPriceEqual(decimal currentPrice, decimal newPrice)
    {
        if (currentPrice == 0 && newPrice == 0)
        {
            return true;
        }
        
        if (currentPrice == 0 || newPrice == 0)
        {
            return false;
        }
        
        return Math.Abs(currentPrice - newPrice) / currentPrice <= CompensationPercentage;
    }

    private static bool HasAnyRefundablePrice(HotelOfferQuote[] quotes)
    {
        return quotes.Any(quote =>
            quote.Prices.Any(price => price.Value.Any(refundability => refundability.Key.IsRefundable)));
    }

    public ICollection<PackageFlightId> GetPackageFlightIds()
    {
        return Airports
            .SelectMany(airports => airports.Value.Select(departureAirport =>
                new PackageFlightId(
                    checkIn: Id.CheckIn,
                    stayLength: Id.StayLength,
                    marketId: Id.MarketId,
                    arrivalAirport: airports.Key,
                    departureAirport: departureAirport)))
            .ToArray();
    }
}