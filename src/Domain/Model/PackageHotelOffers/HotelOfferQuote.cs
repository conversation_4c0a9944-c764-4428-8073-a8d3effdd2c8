using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageHotelOffers;

public class HotelOfferQuote
{
    public required int MetaCode { get; init; }
    public required DateOnly CheckIn { get; init; }
    public required int StayLength { get; init; }
    public required string ProviderConfigurationId { get; init; }
    public required PackageOccupancy Occupancy { get; init; }
    public required Dictionary<MealPlan, Dictionary<Refundability, Money>> Prices { get; init; }
    public required DateTime UpdatedAt { get; init; }
}
