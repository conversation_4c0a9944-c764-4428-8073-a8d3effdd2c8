using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.Markets;

public class Market
{
    public string Id { get; private init; } = null!;
    public Currency Currency { get; private init; }
    public string PartnerCode { get; private set; } = null!;
    public Airport[] DepartureAirports { get; private set; } = null!;
    
    public static Market Create(string id, Currency currency, string partnerCode, Airport[] departureAirports)
    {
        return new Market
        {
            Id = id,
            Currency = currency,
            PartnerCode = partnerCode,
            DepartureAirports = departureAirports
        };
    }

    public void Update(Market market)
    {
        if (Id != market.Id)
        {
            throw new InvalidOperationException("Cannot update market with different Id");
        }
        
        if (Currency != market.Currency) 
        {
            throw new InvalidOperationException("Cannot update market with different currency");
        }
        
        PartnerCode = market.PartnerCode;
        DepartureAirports = market.DepartureAirports;
    }
}