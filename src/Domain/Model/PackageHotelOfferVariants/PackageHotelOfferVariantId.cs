using System.Diagnostics.CodeAnalysis;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageHotelOfferVariants;

public readonly struct PackageHotelOfferVariantId : IParsable<PackageHotelOfferVariantId>, IEquatable<PackageHotelOfferVariantId>
{
    private const char KeySeparator = ':';
    
    public DateOnly CheckIn { get; private init; }
    public int StayLength { get; private init; }
    public string MarketId { get; private init;}
    public int MetaCode { get; private init; }
    public PackageOccupancy Occupancy { get; private init; }
    public MealPlan MealPlan { get; private init; }
    
    public PackageHotelOfferVariantId(string id)
    {
        (CheckIn, StayLength, MarketId, MetaCode, Occupancy, MealPlan) =
            Parse(id, shouldThrow: true) ?? throw new FormatException("Invalid package hotel offer id");
    }

    public PackageHotelOfferVariantId(DateOnly checkIn, int stayLength, string marketId, int metaCode, 
        PackageOccupancy occupancy, MealPlan mealPlan)
    {
        CheckIn = checkIn;
        StayLength = stayLength;
        MarketId = marketId;
        MetaCode = metaCode;
        Occupancy = occupancy;
        MealPlan = mealPlan;
    }

    private static (DateOnly CheckIn, int StayLength, string MarketId, int MetaCode, PackageOccupancy occupancy, MealPlan mealPlan)? 
        Parse(string id, bool shouldThrow)
    {
        var t = id.AsSpan();

        var separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("CheckIn/StayLength date separator not found");
            return null;
        }

        if (!DateOnly.TryParseExact(t.Slice(0, separatorPosition), "yyMMdd", out var checkIn))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(checkIn)} date");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow) 
                throw new FormatException("StayLength/MarketId date separator not found");
        }

        if (!int.TryParse(t.Slice(0, separatorPosition), out var stayLength))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid stay length");
            return null;
        }

        t = t.Slice(separatorPosition + 1);
       
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("MarketId/MetaCode separator not found");
            return null;
        }
        
        var marketId = t.Slice(0, separatorPosition).ToString();
        
        t = t.Slice(separatorPosition + 1);
        
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("MetaCode/Occupancy separator not found");
            return null;
        }
        
        if (!int.TryParse(t.Slice(0, separatorPosition), out var metaCode))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(metaCode)}");
            return null;
        }
        
        t = t.Slice(separatorPosition + 1);
        
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("Occupancy/MealPlan separator not found");
            return null;
        }
        
        var occupancy = PackageOccupancy.Parse(t.Slice(0, separatorPosition).ToString(), null);
        
        t = t.Slice(separatorPosition + 1);

        var mealPlan = MealPlan.FromShortString(t.ToString());
        
        return (checkIn, stayLength, marketId, metaCode, occupancy, mealPlan);
    }
    
    public static implicit operator PackageHotelOfferVariantId(string id) 
        => new(id);

    public static implicit operator string(PackageHotelOfferVariantId packageHotelOfferVariantId)
        => packageHotelOfferVariantId.ToString();

    public static bool operator ==(PackageHotelOfferVariantId packageHotelOfferVariantId, 
        PackageHotelOfferVariantId packageHotelOfferVariantId2)
        => packageHotelOfferVariantId.Equals(packageHotelOfferVariantId2);

    public static bool operator !=(PackageHotelOfferVariantId packageHotelOfferVariantId, 
        PackageHotelOfferVariantId packageHotelOfferVariantId2)
        => !(packageHotelOfferVariantId == packageHotelOfferVariantId2);

    public override bool Equals([NotNullWhen(true)] object? obj)
        => obj is PackageHotelOfferVariantId && Equals(obj);

    public override int GetHashCode() 
        => ToString().GetHashCode();

    public override string ToString()
        => $"{CheckIn.ToString("yyMMdd")}{KeySeparator}{StayLength}{KeySeparator}{MarketId}{KeySeparator}{MetaCode}" +
           $"{KeySeparator}{Occupancy}{KeySeparator}{MealPlan.ToShortString()}";

    public static PackageHotelOfferVariantId Parse(string s, IFormatProvider? provider)
        => (PackageHotelOfferVariantId)s;

    public static bool TryParse([NotNullWhen(true)] string? s, IFormatProvider? provider, 
        out PackageHotelOfferVariantId result)
    {
        if (s != null && Parse(s, false) != null)
        {
            result = new PackageHotelOfferVariantId(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(PackageHotelOfferVariantId other)
    {
        return CheckIn.Equals(other.CheckIn) && 
               StayLength == other.StayLength &&
               MetaCode == other.MetaCode && 
               MarketId == other.MarketId && 
               Occupancy == other.Occupancy && 
               MealPlan == other.MealPlan;
    }
}