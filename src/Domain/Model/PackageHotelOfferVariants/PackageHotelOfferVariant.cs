using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageHotelOfferVariants;

public class PackageHotelOfferVariant
{
    public PackageHotelOfferVariantId Id { get; private init; }
    public int Price { get; private init; }
    
    public static PackageHotelOfferVariant Create(DateOnly checkIn, int stayLength, string marketId, int metaCode,
        PackageOccupancy occupancy, MealPlan mealPlan, int price)
    {
        var id = new PackageHotelOfferVariantId(checkIn, stayLength, marketId, metaCode, occupancy, mealPlan);
        return new PackageHotelOfferVariant
        {
            Id = id,
            Price = price
        };
    }
}