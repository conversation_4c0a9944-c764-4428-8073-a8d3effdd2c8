using Esky.Packages.Domain.Model.LiveVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Factories;

public interface IHotelOfferVariantFactory
{
    HotelOfferLiveVariant Create(
        string offerId,
        MealPlan mealPlan,
        Refundability refundability,
        DateOnly? freeRefundUntil,
        Currency currency,
        Money price,
        Money priceAtHotel,
        int availability);
}
