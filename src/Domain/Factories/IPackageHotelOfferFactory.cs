using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Factories;

public interface IPackageHotelOfferFactory
{
    PackageHotelOffer Create(
        PackageHotelOfferId id, 
        string definitionId, 
        Currency currency, 
        PackageOccupancy[] occupancies, 
        string[] providerConfigurationIds, 
        HotelOfferQuote[] hotelOfferQuotes, 
        Dictionary<Airport, Airport[]> airports,
        bool mergeIntoPackage);

    void ApplyPolicies(PackageHotelOffer packageFlight);
}
