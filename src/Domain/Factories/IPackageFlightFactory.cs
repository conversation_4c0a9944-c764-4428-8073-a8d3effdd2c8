using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Factories;

public interface IPackageFlightFactory
{
    PackageFlight Create(
        PackageFlightId id, 
        string definitionId, 
        string partnerCode, 
        Currency currency, 
        PackageOccupancy[] occupancies, 
        FlightOffer[] flightOffers, 
        FlightQuote[] flightQuotes, 
        int[] metaCodes);
    
    void ApplyPolicies(PackageFlight packageFlight);
}