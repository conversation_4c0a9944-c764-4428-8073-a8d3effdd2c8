namespace Esky.Packages.Domain.Types;

public readonly struct Refundability : IEquatable<Refundability>, IParsable<Refundability>
{
    public readonly bool IsRefundable;

    public Refundability(bool isRefundable)
    {
        IsRefundable = isRefundable;
    }
    
    public static Refundability Refundable { get; } = new(true);
    public static Refundability NonRefundable { get; } = new(false);

    public static implicit operator Refundability(bool isRefundable)
        => new(isRefundable);

    public static implicit operator string(Refundability refundability)
        => refundability.IsRefundable.ToString();

    public static bool operator ==(Refundability refundability, Refundability refundability2)
        => refundability.IsRefundable == refundability2.IsRefundable;

    public static bool operator !=(Refundability refundability, Refundability refundability2)
        => !(refundability == refundability2);

    public override bool Equals(object? obj)
        => obj is Refundability other && Equals(other);

    public override int GetHashCode()
        => IsRefundable.GetHashCode();

    public override string ToString()
        => IsRefundable.ToString();

    public static Refundability Parse(string s, IFormatProvider? provider)
        => (Refundability)bool.Parse(s);

    public static bool TryParse(string? s, IFormatProvider? provider, out Refundability result)
    {
        if (s != null)
        {
            result = new Refundability(bool.Parse(s));
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(Refundability other)
    {
        return IsRefundable == other.IsRefundable;
    }
}
