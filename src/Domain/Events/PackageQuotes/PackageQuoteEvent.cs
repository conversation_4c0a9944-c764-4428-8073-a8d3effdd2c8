using System.Text.Json.Serialization;

namespace Esky.Packages.Domain.Events.PackageQuotes;

[JsonDerivedType(typeof(PackageFlightQuoteEvent), "packageFlightQuote")]
[JsonDerivedType(typeof(PackageHotelOfferQuoteEvent), "packageHotelOfferQuote")]
public abstract class PackageQuoteEvent
{
    public required string Id { get; init; }
    public required string MarketId { get; init; }
    public required DateOnly CheckIn { get; init; }
    public required int StayLength { get; init; }
    public required int MetaCode { get; init; }
}