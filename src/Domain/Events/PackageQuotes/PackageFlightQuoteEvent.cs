using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Events.PackageQuotes;

public class PackageFlightQuoteEvent : PackageQuoteEvent
{
    public required Airport DepartureAirport { get; init; }
    public required Airport ArrivalAirport { get; init; }
    public required Dictionary<PackageOccupancy, FlightPriceEntry> Prices { get; init; }
}
