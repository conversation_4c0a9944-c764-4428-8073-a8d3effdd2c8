using System.Text.Json.Serialization;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Events.PackageVariants;

[JsonDerivedType(typeof(PackageVariantUpdatedEvent), "packageVariantUpdated")]
[JsonDerivedType(typeof(PackageVariantDeletedEvent), "packageVariantDeleted")]
public class PackageVariantEvent
{
    public required string Id { get; init; }
    public required DateOnly CheckIn { get; init; }
    public required int StayLength { get; init; }
    public required string MarketId { get; init; }
    public required int MetaCode { get; init; }
    public required PackageOccupancy Occupancy { get; init; }
    public required Airport DepartureAirport { get; init; }
    public required MealPlan MealPlan { get; init; }
}