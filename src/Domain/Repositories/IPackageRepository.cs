using Esky.Packages.Domain.Events.PackageQuotes;
using Esky.Packages.Domain.Model.Packages;

namespace Esky.Packages.Domain.Repositories;

public interface IPackageRepository
{
    Task<Package?> GetById(PackageId id, CancellationToken cancellationToken = default);
    Task<List<Package>> ListByIds(List<PackageId> packagesIds,
            CancellationToken cancellationToken = default);
    Task ApplyQuotes(List<PackageQuoteEvent> packageQuotes, CancellationToken cancellationToken);
}