using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Repositories;

public interface IPackageVariantRepository
{
    Task<List<PackageVariant>> GetCheapestsPerMetaCode(List<int> metaCodes, List<int> stayLengths, string marketId, 
        List<MealPlan> mealPlans, PackageOccupancy occupancy, DateOnly departureDateFrom, DateOnly departureDateTo, 
        List<Airport> departureAirports, CancellationToken cancellationToken = default);
    
    Task<List<PackageVariant>> GetCheapestsPerDepartureDate(int metaCode, PackageOccupancy occupancy, string marketId, 
        List<MealPlan> mealPlans, List<Airport> departureAirports, CancellationToken cancellationToken = default);
    
    Task<PackageVariantAggregation> GetPackageVariantAggregation(int metaCode, string marketId, 
        CancellationToken cancellationToken = default);
}