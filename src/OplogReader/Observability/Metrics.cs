using System.Diagnostics.Metrics;

namespace Esky.Packages.OplogReader.Observability;

public static class Metrics
{
    public const string MeterName = "packages-oplog-reader";
    private static readonly Meter Meter = new(MeterName);

    private static long _lastEventMillis;
    
    private static readonly ObservableGauge<long> Lag = 
        Meter.CreateObservableGauge($"{MeterName}_lag", () => _lastEventMillis, "milliseconds");

    public static void RegisterLag(DateTime? lastEventTime)
    {
        if (lastEventTime == null)
        {
            _lastEventMillis = 0;
        }
        else
        {
            _lastEventMillis = (long) (DateTime.UtcNow - lastEventTime.Value).TotalMilliseconds;
        }
    }
    
}