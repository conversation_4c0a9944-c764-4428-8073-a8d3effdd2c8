using Esky.Hotels.Infrastructure.Kafka;
using Esky.Hotels.Infrastructure.Kafka.Producer;
using Esky.Packages.Infrastructure.Configuration;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.OplogReader.Observability;
using Esky.Packages.OplogReader.Options;
using MongoDB.Driver;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;

namespace Esky.Packages.OplogReader;

internal static class DependencyInjection
{
    public static IServiceCollection AddOplogReader(this IServiceCollection services, IConfiguration configuration)
    {
        services.RegisterOptions<OplogReaderOptions>(configuration, OplogReaderOptions.ConfigurationSection);
        services.RegisterOptions<PackageProducerOptions>(configuration,
            PackageProducerOptions.ConfigurationSection);
        
        services.AddKafka(configuration);

        services.AddSingleton<PackageOplogReader>(o =>
        {
            var database = o.GetRequiredService<PackageDatabase>();
            var logger = o.GetRequiredService<ILogger<PackageOplogReader>>();
            var producerFactory = o.GetRequiredService<KafkaProducerFactory>();
            var confirmationQueue = o.GetRequiredService<ConfirmationQueue>();
            var options = o.GetRequiredService<OplogReaderOptions>();
            var producerOptions = o.GetRequiredService<PackageProducerOptions>();
            var collectionName = "packages";
            var resumeTokenCollectionName = "oplogPackageResumeTokens";

            return new PackageOplogReader(database, logger, producerFactory, confirmationQueue, options, 
                producerOptions, collectionName, resumeTokenCollectionName);
        });

        services.AddHostedService(p => p.GetRequiredService<PackageOplogReader>());

        services.AddSingleton<ConfirmationQueue>();
        services.AddHostedService(p => p.GetRequiredService<ConfirmationQueue>());

        return services;
    }
    
    internal static IServiceCollection AddObservability(this IServiceCollection services)
    {
        var appName = "esky-packages-oplogreader";

        services.AddOpenTelemetry()
            .WithMetrics(options =>
                options.SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(appName))
                    .AddMeter(Metrics.MeterName)
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddProcessInstrumentation()
                    .AddPrometheusExporter()
            );

        return services;
    }
}