#!/bin/bash

OPLOGREADER__PARTITIONINDEX=$(echo "${HOSTNAME}" | rev | cut -d"-" -f 1 | rev) 
export OPLOGREADER__PARTITIONINDEX

# Start the main process and save its PID
# Use exec to replace the shell script process with the main process
exec dotnet OplogReader.dll &
pid=$!

# Trap the SIGTERM signal and forward it to the main process
trap 'kill -SIGTERM $pid; wait $pid' SIGTERM

# Wait for the main process to complete
wait $pid