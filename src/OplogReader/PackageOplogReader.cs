using System.Text.Json;
using Confluent.Kafka;
using Esky.Hotels.Infrastructure.Kafka.ConfirmationGroup;
using Esky.Hotels.Infrastructure.Kafka.Producer;
using Esky.Packages.Domain.Events.PackageVariants;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.OplogReaders;
using Esky.Packages.Infrastructure.Partitioners;
using Esky.Packages.Infrastructure.Serialization.Contexts;
using Esky.Packages.OplogReader.Observability;
using Esky.Packages.OplogReader.Options;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Esky.Packages.OplogReader;

public class PackageOplogReader(
    PackageDatabase database,
    ILogger<OplogReader<Package>> logger,
    KafkaProducerFactory producerFactory,
    ConfirmationQueue confirmationQueue,
    OplogReaderOptions options,
    PackageProducerOptions producerOptions,
    string collectionName,
    string resumeTokenCollectionName)
    : OplogReader<Package>(database.Database, logger, collectionName, resumeTokenCollectionName)
{
    private KafkaProducer<string, byte[]> _producer = null!;
    private readonly HotelMetaCodePartitioner _partitioner = new();

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _partitioner.Initialize(producerOptions.Topic, producerOptions.BootstrapServers);
        _producer = producerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(producerOptions.BootstrapServers)
            .WithAcks(Acks.All) // TODO: Check if this is the correct setting for the producer
            .WithDeliveryReportFields(ProducerDeliveryReportFields.Status)
            .WithClientId(nameof(PackageOplogReader))
            .WithQueueBufferingMaxMessages(1_000_000)
            .WithCompression(producerOptions.Compression));

        if (options.PartitionCount < 1 || options.PartitionIndex < 0 ||
            options.PartitionIndex > options.PartitionCount - 1)
            throw new InvalidOperationException("Invalid configuration - PartitionCount/PartitionIndex");

        ReaderName = $"{nameof(PackageOplogReader)}-{options.PartitionIndex}";
        BatchSize = 5000;

        return base.ExecuteAsync(stoppingToken);
    }

    protected override void ConfigureOptions(ChangeStreamOptions changeStreamOptions)
    {
        changeStreamOptions.FullDocument = ChangeStreamFullDocumentOption.UpdateLookup;
        changeStreamOptions.FullDocumentBeforeChange = ChangeStreamFullDocumentBeforeChangeOption.Required;
    }

    protected override PipelineDefinition<ChangeStreamDocument<Package>, ChangeStreamDocument<Package>>
        ConfigurePipeline(EmptyPipelineDefinition<ChangeStreamDocument<Package>> pipeline)
    {
        logger.LogInformation("Partition count: {count}", options.PartitionCount);
        logger.LogInformation("Partition index: {index}", options.PartitionIndex);

        return pipeline.Match(
            BsonDocument.Parse(
                // language=json
                $$"""
                  {
                    "$and": [
                      {
                        "documentKey._id.m": {
                           "$mod": [ {{options.PartitionCount}}, {{options.PartitionIndex}} ]
                         }
                      },
                      {
                        "operationType": { "$in": ["insert", "update", "delete"] }
                      }
                    ]
                  }
                  """
            )
        );
    }

    protected override void ProcessBatch(IReadOnlyCollection<ChangeStreamDocument<Package>> batch,
        Action acknowledge)
    {
        Metrics.RegisterLag(DateTimeOffset.FromUnixTimeSeconds(batch.First().ClusterTime.Timestamp).UtcDateTime);

        var events = new List<PackageVariantEvent>();

        foreach (var change in batch)
        {
            if (MapToEvents(change) is { } e)
                events.AddRange(e);
        }

        var confirmationCount = events.Count;
        var confirmationGroup = confirmationQueue.Queue.CreateAndEnqueue(confirmationCount, _ => acknowledge());

        foreach (var e in events)
            SendEvent(e, confirmationGroup);

        logger.LogDebug("Sent {count} events", confirmationCount);
    }

    protected override void ProcessEmptyBatch()
    {
        Metrics.RegisterLag(null);
    }

    private List<PackageVariantEvent> MapToEvents(ChangeStreamDocument<Package> change)
    {
        var before = change.FullDocumentBeforeChange;
        var after = change.FullDocument;

        var events = new List<PackageVariantEvent>();

        var beforeVariants = before?.GetVariants();
        var afterVariants = after?.GetVariants();

        if (before == null && after == null)
        {
            logger.LogWarning("FullDocument and FullDocumentBeforeChange of the change stream document are null");
        }
        else if (before == null) // added package
        {
            events.AddRange(afterVariants!.Select(v => new PackageVariantUpdatedEvent
            {
                Id = v.Id,
                CheckIn = v.Id.CheckIn,
                StayLength = v.Id.StayLength,
                MarketId = v.Id.MarketId,
                MetaCode = v.Id.MetaCode,
                Occupancy = v.Id.Occupancy,
                DepartureAirport = v.Id.DepartureAirport,
                MealPlan = v.Id.MealPlan,
                DepartureDate = v.DepartureDate,
                ReturnArrivalDate = v.ReturnArrivalDate,
                Price = v.Price
            }));
        }
        else if (after == null) // removed package
        {
            events.AddRange(beforeVariants!.Select(v => new PackageVariantDeletedEvent
            {
                Id = v.Id,
                CheckIn = v.Id.CheckIn,
                StayLength = v.Id.StayLength,
                MarketId = v.Id.MarketId,
                MetaCode = v.Id.MetaCode,
                Occupancy = v.Id.Occupancy,
                DepartureAirport = v.Id.DepartureAirport,
                MealPlan = v.Id.MealPlan,
            }));
        }
        else // updated package
        {
            // TODO: Simplify (use a dictionary)
            var deletedVariants = beforeVariants!.Where(b => afterVariants!.All(a => a.Id != b.Id));
            var addedVariants = afterVariants!.Where(a => beforeVariants!.All(b => a.Id != b.Id));
            var updatedVariants = afterVariants!.Where(a => beforeVariants!.Any(b =>
                a.Id == b.Id && (a.Price != b.Price || a.DepartureDate != b.DepartureDate ||
                                 a.ReturnArrivalDate != b.ReturnArrivalDate)));

            events.AddRange(deletedVariants.Select(v => new PackageVariantDeletedEvent
            {
                Id = v.Id,
                CheckIn = v.Id.CheckIn,
                StayLength = v.Id.StayLength,
                MarketId = v.Id.MarketId,
                MetaCode = v.Id.MetaCode,
                Occupancy = v.Id.Occupancy,
                DepartureAirport = v.Id.DepartureAirport,
                MealPlan = v.Id.MealPlan,
            }));

            events.AddRange(addedVariants.Select(v => new PackageVariantUpdatedEvent
            {
                Id = v.Id,
                CheckIn = v.Id.CheckIn,
                StayLength = v.Id.StayLength,
                MarketId = v.Id.MarketId,
                MetaCode = v.Id.MetaCode,
                Occupancy = v.Id.Occupancy,
                DepartureAirport = v.Id.DepartureAirport,
                MealPlan = v.Id.MealPlan,
                DepartureDate = v.DepartureDate,
                ReturnArrivalDate = v.ReturnArrivalDate,
                Price = v.Price
            }));

            events.AddRange(updatedVariants.Select(v => new PackageVariantUpdatedEvent
            {
                Id = v.Id,
                CheckIn = v.Id.CheckIn,
                StayLength = v.Id.StayLength,
                MarketId = v.Id.MarketId,
                MetaCode = v.Id.MetaCode,
                Occupancy = v.Id.Occupancy,
                DepartureAirport = v.Id.DepartureAirport,
                MealPlan = v.Id.MealPlan,
                DepartureDate = v.DepartureDate,
                ReturnArrivalDate = v.ReturnArrivalDate,
                Price = v.Price
            }));
        }

        return events;
    }

    private void SendEvent(PackageVariantEvent e, ConfirmationGroup confirmationGroup)
    {
        var partition = _partitioner.GetTopicPartition(e.MetaCode);

        var value = JsonSerializer.SerializeToUtf8Bytes(e, PackageVariantEventJsonContext.Default.PackageVariantEvent);

        _producer.Produce(partition, new Message<string, byte[]>
            {
                Key = e.Id,
                Value = value
            },
            report => MarkConfirmationGroupBasedOnDeliveryReport(report, confirmationGroup));
    }

    private void MarkConfirmationGroupBasedOnDeliveryReport<TKey, TValue>(
        DeliveryReport<TKey, TValue> deliveryReport, ConfirmationGroup confirmationGroup)
    {
        if (deliveryReport.Status == PersistenceStatus.Persisted)
        {
            confirmationGroup.ConfirmOne();
        }
        else
        {
            logger.LogError("Cannot deliver message to kafka: {error}", deliveryReport.Error);
            confirmationGroup.MarkAsFailed();
        }
    }
}