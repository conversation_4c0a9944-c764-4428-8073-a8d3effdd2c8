using System.Diagnostics;
using System.Diagnostics.Metrics;

namespace Esky.Packages.Merger.Observability;

public static class Metrics
{
    public const string MeterName = "packages-merger";
    private static readonly Meter Meter = new(MeterName);

    static Metrics()
    {
        PackageQuoteBatches.Add(0);
        PackageQuoteMessages.Add(0);
        PackageQuoteInvalidMessages.Add(0);
        PackageQuoteTombstoneMessages.Add(0);
    }
    
    internal static void RecordPackageQuoteMessages(int count, string type)
    {
        var tags = new TagList { { "type", type } }; 
        PackageQuoteMessages.Add(count, tags);
    }

    internal static readonly Counter<long> PackageQuoteBatches = 
        Meter.CreateCounter<long>($"{MeterName}_package_quote_batches", "count");
    
    internal static readonly Counter<long> PackageQuoteMessages = 
        Meter.CreateCounter<long>($"{MeterName}_package_quote_messages", "count");

    internal static readonly Counter<long> PackageQuoteInvalidMessages =
        Meter.CreateCounter<long>($"{MeterName}_package_quote_invalid_messages", "count");

    internal static readonly Counter<long> PackageQuoteTombstoneMessages =
        Meter.CreateCounter<long>($"{MeterName}_package_quote_tombstone_messages", "msg");

    internal static readonly Histogram<double> PackageQuoteBatchProcessDuration =
        Meter.CreateHistogram<double>($"{MeterName}_package_quote_batch_process", "milliseconds");

    internal static readonly Histogram<double> PackageQuoteDeserializationDuration =
        Meter.CreateHistogram<double>($"{MeterName}_package_quote_batch_deserialization", "milliseconds");
}