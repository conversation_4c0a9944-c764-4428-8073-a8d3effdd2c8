<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <RootNamespace>Esky.Packages.Merger</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Infrastructure\Infrastructure.csproj"/>
    </ItemGroup>

	<ItemGroup>
		<Content Update="appsettings.Development.json">
			<DependentUpon>appsettings.json</DependentUpon>
		</Content>
		<Content Update="appsettings.Staging.json">
			<DependentUpon>appsettings.json</DependentUpon>
		</Content>
		<Content Update="appsettings.Production.json">
			<DependentUpon>appsettings.json</DependentUpon>
		</Content>
		<Content Update="nlog.config">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

    <ItemGroup>
        <PackageReference Include="Esky.Hotels.Infrastructure.Kafka"/>
        <PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore"/>
        <PackageReference Include="OpenTelemetry.Extensions.Hosting"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.Http"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.Process"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.Runtime"/>
    </ItemGroup>

</Project>
