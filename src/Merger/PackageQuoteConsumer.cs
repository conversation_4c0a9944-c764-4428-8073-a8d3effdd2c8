using System.Diagnostics;
using System.Text.Json;
using Esky.Hotels.Infrastructure.Kafka.Consumer;
using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Events.PackageQuotes;
using Esky.Packages.Infrastructure.Serialization.Contexts;
using Esky.Packages.Merger.Observability;

namespace Esky.Packages.Merger;

public class PackageQuoteConsumer(
    KafkaConsumerFactory kafkaConsumerFactory,
    PackageQuoteConsumerOptions options,
    IPackageService packageService,
    ILogger<PackageQuoteConsumer> logger) : BackgroundService
{
    private KafkaConsumer<string, byte[]> _packageQuoteConsumer = null!;

    private void InitializeKafka()
    {
        _packageQuoteConsumer = kafkaConsumerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(options.BootstrapServers)
            .WithDefaultTopics(options.Topic)
            .WithConsumerGroupId(options.ConsumerGroup)
            .WithAutoOffsetReset(options.AutoOffsetReset)
            .WithAutoCommitIntervalMs(200)
            .WithLogPartitionRebalance()
        );

        _packageQuoteConsumer.Subscribe();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        InitializeKafka();

        while (!stoppingToken.IsCancellationRequested)
        {
            var batch = await _packageQuoteConsumer.ConsumeBatchAsync(new KafkaBatchOptions<string, byte[]>
            {
                MaxBatchSize = options.MaxBatchSize,
                MaxProcessedMessages = options.MaxProcessedMessagesPerBatch,
                StoppingToken = stoppingToken
            });

            logger.LogDebug("Received batch with {receivedMessageCount} messages.", batch.InterestingMessageCount);

            Metrics.PackageQuoteBatches.Add(1);

            await ProcessBatch(batch, stoppingToken);
        }
    }
    
    private async Task ProcessBatch(KafkaBatch<string, byte[]> batch, CancellationToken cancellationToken)
    {
        var watch = Stopwatch.StartNew();

        if (batch.InterestingResults.Count > 0)
        {
            var packageQuotes = DeserializePackageQuotes(batch);
            
            Metrics.RecordPackageQuoteMessages(packageQuotes.Count(p => p is PackageHotelOfferQuoteEvent), "hotelOffer");
            Metrics.RecordPackageQuoteMessages(packageQuotes.Count(p => p is PackageFlightQuoteEvent), "flight");
            
            await packageService.ApplyQuotes(packageQuotes, cancellationToken);
            
            logger.LogDebug("Processed {packageQuoteCount} package quotes", packageQuotes.Count);
            batch.StoreOffsets();
        }
        
        watch.Stop();
        Metrics.PackageQuoteBatchProcessDuration.Record(watch.ElapsedMilliseconds);
    }

    private List<PackageQuoteEvent> DeserializePackageQuotes(KafkaBatch<string, byte[]> batch)
    {
        var events = new List<PackageQuoteEvent>();
        var watch = Stopwatch.StartNew();

        foreach (var result in batch.InterestingResults)
        {
            if (result.Message.Value is null)
            {
                logger.LogWarning("Received unexpected tombstone message, ignoring");
                Metrics.PackageQuoteInvalidMessages.Add(1);
                continue;
            }
            
            try
            {
                var packageQuote = JsonSerializer.Deserialize(result.Message.Value, 
                    PackageQuoteEventJsonContext.Default.PackageQuoteEvent)!;
                events.Add(packageQuote);
            }
            catch (Exception e)
            {
                logger.LogError(e, "Failed to deserialize package quote");
                Metrics.PackageQuoteInvalidMessages.Add(1);

                throw;
            }
        }
        
        watch.Stop();
        Metrics.PackageQuoteDeserializationDuration.Record(watch.ElapsedMilliseconds);
        
        return events;
    }
}