using Esky.Hotels.Infrastructure.Kafka;
using Esky.Packages.Infrastructure.Configuration;
using Esky.Packages.Merger.Observability;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;

namespace Esky.Packages.Merger;

public static class DependencyInjection
{
    internal static IServiceCollection AddKafkaConsumers(this IServiceCollection services, IConfiguration configuration)
    {
        services.RegisterOptions<PackageQuoteConsumerOptions>(configuration,
            PackageQuoteConsumerOptions.ConfigurationSection);

        services.AddKafka(configuration);
        
        services.AddHostedService<PackageQuoteConsumer>();

        return services;
    }

    internal static IServiceCollection AddObservability(this IServiceCollection services)
    {
        var appName = "esky-packages-merger";

        services.AddOpenTelemetry()
            .WithMetrics(options =>
                options.SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(appName))
                    .AddMeter(Metrics.MeterName)
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddProcessInstrumentation()
                    .AddPrometheusExporter()
            );

        return services;
    }
}