using Esky.Packages.Generator.ConsumerDefinitions;
using Esky.Packages.Generator.Consumers;
using Esky.Packages.Generator.Observability;
using Esky.Packages.Generator.Options;
using Esky.Packages.Infrastructure.Serialization.Converters;
using Esky.Packages.Infrastructure.Tasks.Options;
using MassTransit;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using System.Text.Json.Serialization;

namespace Esky.Packages.Generator;

public static class DependencyInjection
{
    internal static IServiceCollection AddMessageBus(this IServiceCollection services, IConfiguration configuration)
    {
        var rabbitMqOptions = new RabbitMqOptions();
        configuration.GetSection(RabbitMqOptions.ConfigurationSection).Bind(rabbitMqOptions);
        
        services.AddMassTransit(x =>
        {
            x.AddConsumer<GeneratePackagesTaskConsumer, GeneratePackagesTaskConsumerDefinition>();
            x.AddConsumer<EnqueueGenerationsTaskConsumer, EnqueueGenerationsTaskConsumerDefinition>();
            x.AddConsumer<RetryPipelinesTaskConsumer, RetryPipelinesTaskConsumerDefinition>();
            x.AddConsumer<RetryAllFailedPipelinesTaskConsumer, RetryAllFailedPipelinesTaskConsumerDefinition>();
            
            x.UsingRabbitMq((context, cfg) =>
            {
                cfg.Host(rabbitMqOptions.Host);
                cfg.ConfigureEndpoints(context);
                cfg.ConfigureJsonSerializerOptions(options =>
                {
                    options.Converters.Add(new JsonStringEnumConverter());
                    options.Converters.Add(new PackageOccupancyJsonConverter());
                    options.Converters.Add(new CurrencyJsonConverter());
                    options.Converters.Add(new MealPlanJsonConverter());
                    options.Converters.Add(new AirportJsonConverter());
                    options.Converters.Add(new ProviderCodeJsonConverter());

                    return options;
                });
            });
        });

        return services;
    }
    
    internal static IServiceCollection AddObservability(this IServiceCollection services, IConfiguration configuration)
    {
        var tracingOptions = new TracingOptions();
        configuration.GetSection(TracingOptions.ConfigurationSection).Bind(tracingOptions);

        var appName = "esky-packages-generator";

        services.AddOpenTelemetry()
            .WithMetrics(options =>
                options.SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(appName))
                    .AddMeter(Metrics.MeterName)
                    .AddMeter("MassTransit")
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddProcessInstrumentation()
                    .AddPrometheusExporter()
            )
            .WithTracing(builder =>
            {
                builder
                    .SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(appName))
                    .AddSource("Esky")
                    .AddSource("MassTransit")
                    .AddHttpClientInstrumentation()
                    .AddJaegerExporter(o => { o.AgentHost = tracingOptions.AgentHost; });
            });

        // for some reason the Metrics class is not being initialized so we need to do it manually
        Metrics.Initialize();

        return services;
    }
}