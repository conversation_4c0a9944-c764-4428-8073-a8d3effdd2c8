using Esky.Packages.Api;
using Esky.Packages.Api.Endpoints;
using Esky.Packages.Application.DependencyInjections;
using Esky.Packages.Infrastructure.DependencyInjections;
using Esky.Packages.Infrastructure.Observability;
using NLog.Web;

var builder = WebApplication.CreateBuilder(args);
var services = builder.Services;
var configuration = builder.Configuration;

if (!builder.Environment.IsDevelopment())
{
    builder.Logging.ClearProviders();
    builder.Host.UseNLog();
    builder.Services.AddCommunicationLogging();
}

services
    .AddApplication(o =>
    {
        o.AddCurrencyConverter();
    })
    .AddInfrastructure(configuration, o =>
    {
        o.AddTaskScheduler();
        o.EnableHttpClientLoggingHandlers = !builder.Environment.IsDevelopment();
    });

services
    .ConfigureJson()
    .AddOpenApi()
    .AddObservability(configuration)
    .AddHealthChecks();

var app = builder.Build();

app.MapHealthChecks("/healthz");
app.MapPrometheusScrapingEndpoint();

app.MapApi();
app.UseOpenApi();

if (!builder.Environment.IsDevelopment())
{
    app.UseRequestLogging();
}

app.Run();

namespace Esky.Packages.Api
{
    // Required for WebApplicationFactory in tests
    public partial class Program { }
}