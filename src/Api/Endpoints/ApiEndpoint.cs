using Esky.Packages.Api.Endpoints.Api;

namespace Esky.Packages.Api.Endpoints;

internal static class ApiEndpoint
{
    public static IEndpointRouteBuilder MapApi(this IEndpointRouteBuilder builder)
    {
        return builder
            .MapGroup("api")
            .MapMarkets()
            .MapPackageDefinitions()
            .MapPackageGenerations()
            .MapPackageAvailabilities()
            .MapPackages()
            .MapSearch()
            .MapCalendars();
    }
}