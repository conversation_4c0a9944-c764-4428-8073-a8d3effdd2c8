using Esky.Packages.Application.Dtos.Common;
using Esky.Packages.Application.Services;
using Esky.Packages.Contract.Calendars;
using Esky.Packages.Domain.Types;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;

namespace Esky.Packages.Api.Endpoints.Api;

public static class CalendarsEndpoint
{
    private const string Prefix = "calendars";

    public static IEndpointRouteBuilder MapCalendars(this IEndpointRouteBuilder builder)
    {
        var group = builder.MapGroup(Prefix)
            .WithTags(Prefix);

        group.MapPost("", async Task<Results<Ok<CalendarsDto>, NotFound>>([FromBody] GetCalendarQueryDto query, 
            ICalendarService service, CancellationToken cancellationToken) =>
        {
            // TODO: Validate input
            
            var calendars = await service.GetCalendars(new CalendarsParameters(
                MarketId: query.MarketId,
                MetaCode: query.MetaCode,
                DepartureAirports: query.DepartureAirports?.Select(a => new Airport(a)).ToList() ?? [],
                MealPlans: query.MealPlans?.Select(m => m.ToDomain()).ToList() ?? [],
                InboundDepartures: query.InboundDepartures?.Select(td => td.ToDomain()).ToList() ?? [],
                OutboundDepartures: query.OutboundDepartures?.Select(td => td.ToDomain()).ToList() ?? [],
                Occupancy: Occupancy.FromDto(query.Occupancy)), cancellationToken);

            if (calendars.Calendars.Count == 0)
            {
                return TypedResults.NotFound();
            }

            return TypedResults.Ok(calendars);
        });
        
        group.MapPost("aggregation", async Task<Results<Ok<CalendarAggregationDto>, NotFound>> (
            [FromBody] GetCalendarAggregationQueryDto queryDto, ICalendarService service, 
            CancellationToken cancellationToken) =>
        {
            var aggregation = await service.GetCalendarAggregation(
                new CalendarAggregationParameters(
                    MarketId: queryDto.MarketId,
                    MetaCode: queryDto.MetaCode,
                    Occupancy: Occupancy.FromDto(queryDto.Occupancy)), cancellationToken);

            if (aggregation.DepartureAirports.Count == 0 && aggregation.DepartureAirports.Count == 0 &&
                aggregation.Occupancies.Count == 0)
            {
                return TypedResults.NotFound();
            } 
            
            return TypedResults.Ok(aggregation);
        });

        
        return group;
    }
}