using Esky.Packages.Application.Dtos.Common;
using Esky.Packages.Application.Services;
using Esky.Packages.Contract.Search;
using Esky.Packages.Domain.Types;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;

namespace Esky.Packages.Api.Endpoints.Api;

public static class SearchEndpoint
{
    private const string Prefix = "search";

    public static IEndpointRouteBuilder MapSearch(this IEndpointRouteBuilder builder)
    {
        var group = builder.MapGroup(Prefix)
            .WithTags(Prefix);

        group.MapPost("", async Task<Results<Ok<SearchDto>, NotFound>> ([FromBody] GetSearchQueryDto query,
            ISearchService service, CancellationToken cancellationToken) =>
        {
            // TODO: Validate input

            var search = await service.Search(new SearchParameters(
                MarketId: query.MarketId,
                MetaCodes: query.MetaCodes,
                StayLengths: query.StayLengths,
                DepartureDateFrom: query.DepartureDateFrom,
                DepartureDateTo: query.DepartureDateTo,
                DepartureAirports: query.DepartureAirports?.Select(d => new Airport(d)).ToList() ?? [],
                MealPlans: query.MealPlans?.Select(m => m.ToDomain()).ToList() ?? [],
                Occupancies: query.Occupancy != null
                    ? [Occupancy.FromDto(query.Occupancy)]
                    : query.Occupancies!.Select(Occupancy.FromDto).ToList(),
                InboundDepartures: query.InboundDepartures?.Select(td => td.ToDomain()).ToList() ?? [],
                OutboundDepartures: query.OutboundDepartures?.Select(td => td.ToDomain()).ToList() ?? [],
                MaxPrice: query.MaxPrice,
                UseDynamicSearchFallback: query.UseDynamicSearchFallback == true), cancellationToken);

            if (search.Variants.Count == 0)
                return TypedResults.NotFound();

            return TypedResults.Ok(search);
        });

        group.MapPost("count", async Task<Ok<SearchCountDto>> ([FromBody] GetSearchQueryDto query,
            ISearchService service, CancellationToken cancellationToken) =>
        {
            // TODO: Validate input

            var search = await service.Search(new SearchParameters(
                MarketId: query.MarketId,
                MetaCodes: query.MetaCodes,
                StayLengths: query.StayLengths,
                DepartureDateFrom: query.DepartureDateFrom,
                DepartureDateTo: query.DepartureDateTo,
                DepartureAirports: query.DepartureAirports?.Select(d => new Airport(d)).ToList() ?? [],
                MealPlans: query.MealPlans?.Select(m => m.ToDomain()).ToList() ?? [],
                Occupancies: query.Occupancy != null
                    ? [Occupancy.FromDto(query.Occupancy)]
                    : query.Occupancies!.Select(Occupancy.FromDto).ToList(),
                InboundDepartures: query.InboundDepartures?.Select(td => td.ToDomain()).ToList() ?? [],
                OutboundDepartures: query.OutboundDepartures?.Select(td => td.ToDomain()).ToList() ?? [],
                MaxPrice: query.MaxPrice,
                UseDynamicSearchFallback: query.UseDynamicSearchFallback == true), cancellationToken);

            return TypedResults.Ok(new SearchCountDto(search.Variants.Count));
        });

        return builder;
    }
}