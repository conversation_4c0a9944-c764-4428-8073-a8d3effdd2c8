using Esky.Packages.Application.Abstractions.Tasks;
using Esky.Packages.Application.Dtos.PackageGenerations;
using Esky.Packages.Application.Services;
using Esky.Packages.Application.Tasks;
using Esky.Packages.Contract.PackagesGeneration;
using Esky.Packages.Domain.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace Esky.Packages.Api.Endpoints.Api;

internal static class PackageGenerationsEndpoint
{
    private const string Prefix = "packageGenerations";

    public static IEndpointRouteBuilder MapPackageGenerations(this IEndpointRouteBuilder builder)
    {
        var group = builder
            .MapGroup(Prefix)
            .WithTags(Prefix);

        group
            .MapPost("/enqueue", async (
                    [FromBody] EnqueueGenerationsCommandDto command,
                    IPackageDefinitionRepository repository, 
                    ITaskScheduler scheduler,
                    CancellationToken cancellationToken) => 
            {
                var definitions = await repository.ListByIdsOrTags(
                    ids: command.DefinitionsIds, 
                    tags: command.Tags, 
                    cancellationToken);

                // TODO: Remove when INV-326 is done
                foreach (var definition in definitions.OrderBy(d => d.Id.Split("-").Last()))
                {
                    await scheduler.Publish(
                        new EnqueueGenerationsTask
                        {
                            DefinitionId = definition.Id,
                            Partitions = command.Partitions,
                            StartedByUserId = command.StartedByUserId,
                            GenerationMode = command.GenerationMode.ToApplication()
                        }, cancellationToken);
                }

                return new EnqueueGenerationsResponseDto
                {
                    DefinitionIds = definitions.Select(x => x.Id).ToList()
                };
            });
        
        group
            .MapPost("{definitionId}/retryPipelines", async (
                    string definitionId, 
                    [FromBody] RetryPipelinesCommandDto command, 
                    ITaskScheduler scheduler,
                    CancellationToken cancellationToken) 
                => await scheduler.Publish(
                    new RetryPipelinesTask
                    {
                        DefinitionId = definitionId,
                        PipelineIds = command.PipelineIds,
                        StartedByUserId = command.StartedByUserId,
                        GenerationMode = command.GenerationMode.ToApplication()
                    }, cancellationToken));
        
        group
            .MapPost("/retryAllFailedPipelines", async ([FromBody] RetryAllFailedPipelinesCommandDto command, CancellationToken cancellationToken, [FromServices] ITaskScheduler scheduler)
                => await scheduler.Publish(
                    new RetryAllFailedPipelinesTask
                    {
                        StartedByUserId = command.StartedByUserId,
                        GenerationMode = command.GenerationMode.ToApplication()
                    }, cancellationToken));

        group
            .MapGet("{definitionId}/latestRunStatistics", async (
                    string definitionId,
                    IPackageGenerationStatisticsService service,
                    CancellationToken cancellationToken)
                => await service.GenerationDetailedStatistics(definitionId, cancellationToken));

        group
            .MapGet("latestRunsStatistics", async (
                    IPackageGenerationStatisticsService service,
                    CancellationToken cancellationToken)
                => await service.GenerationsAggregatedStatistics(cancellationToken));

        return builder;
    }
}