using Esky.Packages.Application.Dtos.Common;
using Esky.Packages.Application.Services;
using Esky.Packages.Contract.Common;
using Esky.Packages.Contract.Packages;
using Esky.Packages.Contract.PackageVariants;
using Esky.Packages.Domain.Types;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;

namespace Esky.Packages.Api.Endpoints.Api;

internal static class PackagesEndpoint
{
    private const string Prefix = "packages";

    public static IEndpointRouteBuilder MapPackages(this IEndpointRouteBuilder builder)
    {
        var group = builder
            .MapGroup(Prefix)
            .WithTags(Prefix);

        group
            .MapPost("multiple", async ([FromBody] GetManyPackagesQueryDto query, CancellationToken cancellationToken, 
                    IPackageService service)
                => await service.GetByIds(query.PackageIds, cancellationToken));

        group
            .MapGet("{packageId}", async (string packageId, IPackageService service, CancellationToken cancellationToken) 
                => await service.GetById(packageId, cancellationToken));

        group
            .MapPost("{packageId}/live-variants", async Task<Results<Ok<PackageLiveVariantsDto>, BadRequest<ErrorDto>>> (string packageId,
                    [FromBody] GetPackageLiveVariantsQueryDto query, CancellationToken cancellationToken,
                    IPackageLiveVariantsService service)
                =>
            {
                if (!Validate(query, out var message))
                {
                    return TypedResults.BadRequest(new ErrorDto { Message = message });
                }
                
                //todo set to query.Occupancies after all clients are switched
                var occupancies = query.Occupancies?.Length > 0 ? query.Occupancies : [query.Occupancy!];

                return TypedResults.Ok(
                    await service.GetPackageLiveVariants(
                        new PackageLiveVariantsParameters(
                            PackageId: packageId,
                            Occupancies: Occupancy.FromDto(occupancies),
                            DepartureAirports: query.DepartureAirports,
                            FlightOptionId: query.FlightOptionId,
                            PreferredDepartureAirport: query.PreferredDepartureAirport,
                            InboundDepartures: query.InboundDepartures?.Select(td => td.ToDomain()).ToArray() ?? [],
                            OutboundDepartures: query.OutboundDepartures?.Select(td => td.ToDomain()).ToArray() ?? [],
                            PreferSelectedFlight: query.PreferSelectedFlight),
                        cancellationToken));
            });

        return builder;
    }
    
    private static bool Validate(GetPackageLiveVariantsQueryDto query, out string message)
    {
        //todo remove after all clients are switched
        if ((query.Occupancies?.FirstOrDefault() ?? query.Occupancy) == null)
        {
            message = "Occupancy is required.";
            return false;
        }
        
        message = string.Empty;
        return true;
    }
}