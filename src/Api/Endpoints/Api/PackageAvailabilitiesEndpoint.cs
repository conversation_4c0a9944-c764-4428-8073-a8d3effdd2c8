using Esky.Packages.Application.Services;

namespace Esky.Packages.Api.Endpoints.Api;

internal static class PackageAvailabilitiesEndpoint
{
    private const string Prefix = "packageAvailabilities";

    public static IEndpointRouteBuilder MapPackageAvailabilities(this IEndpointRouteBuilder builder)
    {
        var group = builder
            .MapGroup(Prefix)
            .WithTags(Prefix);

        group
            .MapGet("{definitionId}", async (string definitionId, CancellationToken cancellationToken, IPackageAvailabilityService service)
                => await service.GetByDefinitionId(definitionId, cancellationToken));

        return builder;
    }
}