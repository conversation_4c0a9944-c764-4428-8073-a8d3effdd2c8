<?xml version="1.0" encoding="utf-8"?>

<nlog	
	xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	throwExceptions="false" 
	throwConfigExceptions="true" 
	internalLogLevel="Error" 
	internalLogToConsoleError="true" 
	internalLogFile="/app/logs.log" 
	autoReload="true">

	<extensions>
		<add assembly="Esky.NLog.BigQueryTarget" />
	</extensions>
	
	<include file="${basedir}/nlog-common.config"/>

	<variable name="appname" value="${environment:variable=BaseConfiguration__LogApplicationName:whenEmpty=esky-packages-api}" />
	
	<targets>
		<target name="CommunicationBQ" xsi:type="BigQueryTarget"
		  DataSetId="EskyLogs"
		  TableId="CommunicationLog"
		  ProjectId="${environment:BigQuery_Logs_ProjectId}"
		  CredentialsEncoded="${environment:BigQuery_Logs_Credentials}"
		  IncludeAllProperties="True">
			<parameter name="CreateDate" layout="${date:universalTime=true:format=yyyy-MM-dd HH\:mm\:ss.fff}" />
			<parameter name="Type" layout="${event-properties:Type:whenEmpty=0}" />
			<parameter name="Headers" layout="${event-properties:Headers}" />
			<parameter name="Content" layout="${truncate:limit=80000:inner=${event-properties:Content}}" />
			<parameter name="Environment" layout="${environment:variable=BaseConfiguration__DefaultEnvironment}" />
			<parameter name="Application" layout="${var:appname}" />
			<parameter name="DeploymentSlot" layout="${aspnet-Request-Host}" />
			<parameter name="MachineName" layout="${machinename}" />
			<parameter name="ContentPreview" layout="${truncate:limit=80:inner=${event-properties:Content}}" />
			<parameter name="HeadersPreview" layout="${truncate:limit=128:inner=${event-properties:Headers}}" />
		</target>
	</targets>

	<rules>
		<logger name="CommunicationLogger" minlevel="Trace" writeTo="CommunicationBQ" final="true" />
	</rules>
</nlog>