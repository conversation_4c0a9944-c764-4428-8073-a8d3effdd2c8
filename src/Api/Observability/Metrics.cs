using System.Diagnostics.Metrics;
using Esky.Packages.Application.Observability;

namespace Esky.Packages.Api.Observability;

public class Metrics
{
    public const string MeterName = "packages-api";
    private static readonly Meter Meter = new(MeterName);
    
    public static void Initialize()
    {
        var name = Meter.Name;
    }
    
    internal static readonly ObservableCounter<long> PackageVariantSearchOffers =
        Meter.CreateObservableCounter($"{MeterName}_package_variant_search_offers",
            () => ApplicationMetrics.PackageVariantSearchOffers);
    
    internal static readonly ObservableCounter<long> PackageFactorsSearchOffers =
        Meter.CreateObservableCounter($"{MeterName}_package_factors_search_offers",
            () => ApplicationMetrics.PackageFactorsSearchOffers);
    
    internal static readonly ObservableCounter<long> PackageDynamicSearchOffers = 
        Meter.CreateObservableCounter($"{MeterName}_package_dynamic_search_offers",
            () => ApplicationMetrics.PackageDynamicSearchOffers);
}