using Esky.Hotels.Infrastructure.Kafka;
using Esky.Hotels.Infrastructure.Kafka.Producer;
using Esky.Packages.HotelOfferOplogReader.Observability;
using Esky.Packages.HotelOfferOplogReader.Options;
using Esky.Packages.Infrastructure.Configuration;
using Esky.Packages.Infrastructure.Database;
using MongoDB.Driver;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;

namespace Esky.Packages.HotelOfferOplogReader;

internal static class DependencyInjection
{
    public static IServiceCollection AddOplogReader(this IServiceCollection services, IConfiguration configuration)
    {
        services.RegisterOptions<OplogReaderOptions>(configuration, OplogReaderOptions.ConfigurationSection);
        services.RegisterOptions<PackageHotelOfferProducerOptions>(configuration,
            PackageHotelOfferProducerOptions.ConfigurationSection);
        
        services.AddKafka(configuration);

        services.AddSingleton<PackageHotelOfferOplogReader>(o =>
        {
            var database = o.GetRequiredService<PackageDatabase>();
            var logger = o.GetRequiredService<ILogger<PackageHotelOfferOplogReader>>();
            var producerFactory = o.GetRequiredService<KafkaProducerFactory>();
            var confirmationQueue = o.GetRequiredService<ConfirmationQueue>();
            var options = o.GetRequiredService<OplogReaderOptions>();
            var producerOptions = o.GetRequiredService<PackageHotelOfferProducerOptions>();
            var collectionName = "packageHotelOffers";
            var resumeTokenCollectionName = "oplogPackageHotelOfferResumeTokens";

            return new PackageHotelOfferOplogReader(database, logger, producerFactory, confirmationQueue, options, 
                producerOptions, collectionName, resumeTokenCollectionName);
        });

        services.AddHostedService(p => p.GetRequiredService<PackageHotelOfferOplogReader>());

        services.AddSingleton<ConfirmationQueue>();
        services.AddHostedService(p => p.GetRequiredService<ConfirmationQueue>());

        return services;
    }
    
    internal static IServiceCollection AddObservability(this IServiceCollection services)
    {
        var appName = "esky-packages-hotelofferoplogreader";

        services.AddOpenTelemetry()
            .WithMetrics(options =>
                options.SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(appName))
                    .AddMeter(Metrics.MeterName)
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddProcessInstrumentation()
                    .AddPrometheusExporter()
            );

        return services;
    }
}