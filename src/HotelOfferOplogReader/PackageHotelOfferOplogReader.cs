using System.Text.Json;
using Confluent.Kafka;
using Esky.Hotels.Infrastructure.Kafka.ConfirmationGroup;
using Esky.Hotels.Infrastructure.Kafka.Producer;
using Esky.Packages.Domain.Events.PackageQuotes;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;
using Esky.Packages.HotelOfferOplogReader.Observability;
using Esky.Packages.HotelOfferOplogReader.Options;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.OplogReaders;
using Esky.Packages.Infrastructure.Partitioners;
using Esky.Packages.Infrastructure.Serialization.Contexts;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Esky.Packages.HotelOfferOplogReader;

public class PackageHotelOfferOplogReader(
    PackageDatabase database,
    ILogger<OplogReader<PackageHotelOffer>> logger,
    KafkaProducerFactory producerFactory,
    ConfirmationQueue confirmationQueue,
    OplogReaderOptions options,
    PackageHotelOfferProducerOptions producerOptions,
    string collectionName,
    string resumeTokenCollectionName)
    : OplogReader<PackageHotelOffer>(database.Database, logger, collectionName, resumeTokenCollectionName)
{
    private KafkaProducer<string, byte[]> _producer = null!;
    private readonly HotelMetaCodePartitioner _partitioner = new();

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _partitioner.Initialize(producerOptions.Topic, producerOptions.BootstrapServers);
        _producer = producerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(producerOptions.BootstrapServers)
            .WithAcks(Acks.All) // TODO: Check if this is the correct setting for the producer
            .WithDeliveryReportFields(ProducerDeliveryReportFields.Status)
            .WithClientId(nameof(PackageHotelOfferOplogReader))
            .WithCompression(producerOptions.Compression));

        if (options.PartitionCount < 1 || options.PartitionIndex < 0 ||
            options.PartitionIndex > options.PartitionCount - 1)
            throw new InvalidOperationException("Invalid configuration - PartitionCount/PartitionIndex");

        ReaderName = $"{nameof(PackageHotelOfferOplogReader)}-{options.PartitionIndex}";

        return base.ExecuteAsync(stoppingToken);
    }

    protected override void ConfigureOptions(ChangeStreamOptions changeStreamOptions)
    {
        changeStreamOptions.FullDocument = ChangeStreamFullDocumentOption.Required;
        changeStreamOptions.FullDocumentBeforeChange = ChangeStreamFullDocumentBeforeChangeOption.Required;
    }

    protected override PipelineDefinition<ChangeStreamDocument<PackageHotelOffer>,
        ChangeStreamDocument<PackageHotelOffer>>
        ConfigurePipeline(EmptyPipelineDefinition<ChangeStreamDocument<PackageHotelOffer>> pipeline)
    {
        logger.LogInformation("Partition count: {count}", options.PartitionCount);
        logger.LogInformation("Partition index: {index}", options.PartitionIndex);

        return pipeline.Match(
            BsonDocument.Parse(
                // language=json
                $$"""
                  {
                    "$and": [
                      {
                        "documentKey._id.m": {
                          "$mod": [ {{options.PartitionCount}}, {{options.PartitionIndex}} ]
                        }
                      },
                      {
                        "$or": [
                          {
                            "operationType": "delete",
                            "fullDocumentBeforeChange.mergeIntoPackage": true
                          },
                          {
                            "operationType": "insert",
                            "fullDocument.mergeIntoPackage": true
                          },
                          {
                            "operationType": { "$in": ["update", "replace"] },
                            "$or": [
                              { "fullDocument.mergeIntoPackage": true },
                              {
                                "$expr": {
                                  "$ne": [
                                    "$fullDocument.mergeIntoPackage",
                                    "$fullDocumentBeforeChange.mergeIntoPackage"
                                  ]
                                }
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                  """
            )
        );
    }

    protected override void ProcessBatch(IReadOnlyCollection<ChangeStreamDocument<PackageHotelOffer>> batch,
        Action acknowledge)
    {
        Metrics.RegisterLag(DateTimeOffset.FromUnixTimeSeconds(batch.First().ClusterTime.Timestamp).UtcDateTime);

        var events = new List<PackageHotelOfferQuoteEvent>();

        foreach (var change in batch)
        {
            if (MapToEvent(change, logger) is { } e)
                events.AddRange(e);
        }

        var confirmationCount = events.Count;
        var confirmationGroup = confirmationQueue.Queue.CreateAndEnqueue(confirmationCount, _ => acknowledge());

        foreach (var e in events)
            SendEvent(e, confirmationGroup);

        logger.LogDebug("Sent {count} events", confirmationCount);
    }

    protected override void ProcessEmptyBatch()
    {
        Metrics.RegisterLag(null);
    }

    private static PackageHotelOfferQuoteEvent? MapToEvent(
        ChangeStreamDocument<PackageHotelOffer> change,
        ILogger logger)
    {
        var before = change.FullDocumentBeforeChange;
        var after = change.FullDocument;

        var beforePrices = before?.GetLowestPrices();
        var afterPrices = after?.GetLowestPrices();

        if (before == null && after == null)
        {
            logger.LogWarning("FullDocument and FullDocumentBeforeChange of the change stream document are null");
        }
        else if (before == null) // added hotel offer
        {
            if (after!.MergeIntoPackage)
            {
                return CreateEvent(after.Id, afterPrices!);
            }
        }
        else if (after == null)
        {
            if (before.MergeIntoPackage)
            {
                // removed hotel offer
                return CreateEvent(before.Id, new Dictionary<PackageOccupancy, Dictionary<MealPlan, decimal>>());
            }
        }
        else // updated hotel offer
        {
            if (IsMergeIntoPackageUnset(before, after))
            {
                // Flag was unset - send event with empty prices
                return CreateEvent(after.Id, new Dictionary<PackageOccupancy, Dictionary<MealPlan, decimal>>());
            }

            if (IsMergeIntoPackageSet(before, after))
            {
                // Flag was set - send event with new prices
                return CreateEvent(after.Id, afterPrices!);
            }

            if (IsMergeIntoPackageRemainingEnabled(before, after))
            {
                // Both have MergeIntoPackage set to true, check for price changes
                if (!beforePrices!.DeepEquals(afterPrices!)) // prices changed
                {
                    return CreateEvent(after.Id, afterPrices!);
                }
            }
        }

        return null;
    }

    private static PackageHotelOfferQuoteEvent CreateEvent(PackageHotelOfferId id, Dictionary<PackageOccupancy, Dictionary<MealPlan, decimal>> prices) =>
        new()
        {
            Id = id,
            MetaCode = id.MetaCode,
            CheckIn = id.CheckIn,
            StayLength = id.StayLength,
            MarketId = id.MarketId,
            Prices = prices
        };

    private static bool IsMergeIntoPackageUnset(PackageHotelOffer before, PackageHotelOffer after) =>
        before.MergeIntoPackage && !after.MergeIntoPackage;

    private static bool IsMergeIntoPackageSet(PackageHotelOffer before, PackageHotelOffer after) =>
        !before.MergeIntoPackage && after.MergeIntoPackage;

    private static bool IsMergeIntoPackageRemainingEnabled(PackageHotelOffer before, PackageHotelOffer after) =>
        before.MergeIntoPackage && after.MergeIntoPackage;

    private void SendEvent(PackageHotelOfferQuoteEvent e, ConfirmationGroup confirmationGroup)
    {
        var partition = _partitioner.GetTopicPartition(e.MetaCode);

        var value = JsonSerializer.SerializeToUtf8Bytes(e, PackageQuoteEventJsonContext.Default.PackageQuoteEvent);

        _producer.Produce(partition, new Message<string, byte[]>
        {
            Key = e.Id,
            Value = value
        },
            report => MarkConfirmationGroupBasedOnDeliveryReport(report, confirmationGroup));
    }

    private void MarkConfirmationGroupBasedOnDeliveryReport<TKey, TValue>(
        DeliveryReport<TKey, TValue> deliveryReport, ConfirmationGroup confirmationGroup)
    {
        if (deliveryReport.Status == PersistenceStatus.Persisted)
        {
            confirmationGroup.ConfirmOne();
        }
        else
        {
            logger.LogError("Cannot deliver message to kafka: {error}", deliveryReport.Error);
            confirmationGroup.MarkAsFailed();
        }
    }
}