using Esky.Packages.Application.DependencyInjections;
using Esky.Packages.HotelOfferOplogReader;
using Esky.Packages.Infrastructure.DependencyInjections;
using NLog.Web;

var builder = WebApplication.CreateBuilder(args);
var services = builder.Services;
var configuration = builder.Configuration;

if (!builder.Environment.IsDevelopment())
{
    builder.Logging.ClearProviders();
    builder.Host.UseNLog();
}

services
    .AddApplication()
    .AddInfrastructure(configuration, config =>
    {
        config.AddOplogReaderTokenSaver<PackageHotelOfferOplogReader>("oplogPackageHotelOfferResumeTokens");
    })
    .AddHealthChecks();

services
    .AddOplogReader(configuration)
    .AddObservability()
    .AddHealthChecks();

var app = builder.Build();

app.MapHealthChecks("/healthz");
app.UseOpenTelemetryPrometheusScrapingEndpoint();

app.Run();

namespace Esky.Packages.HotelOfferOplogReader
{
    // Required for WebApplicationFactory in tests
    public partial class Program { }
}