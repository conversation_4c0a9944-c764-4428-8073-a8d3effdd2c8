{"Logging": {"ServiceBus": {"Clustermembers": "esky-ets-hotels-pro.rabbitmq-logs-k8s.service.gcp-pro.consul", "UserName": "SECRET", "Password": "SECRET"}}, "Mongo": {"ConnectionString": "SECRET"}, "BloomFilterNotificationConsumer": {"BootstrapServers": "esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-0.service.gcp-pro.consul:9092,esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-1.service.gcp-pro.consul:9092,esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-2.service.gcp-pro.consul:9092", "Topic": "bloomFilterNotificationsNew"}, "FlightQuoteConsumer": {"ConsumerGroup": "packageRepartitionerNew", "BootstrapServers": "esky-ets-flightscontent-pro.kafka-flightscontent-pro-kafka-node-0.service.gcp-pro.consul:9092,esky-ets-flightscontent-pro.kafka-flightscontent-pro-kafka-node-1.service.gcp-pro.consul:9092,esky-ets-flightscontent-pro.kafka-flightscontent-pro-kafka-node-2.service.gcp-pro.consul:9092", "Topic": "flightQuotes", "AutoOffsetReset": "Latest"}, "HotelOfferQuoteConsumer": {"ConsumerGroup": "packageRepartitionerNew", "BootstrapServers": "esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-0.service.gcp-pro.consul:9092,esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-1.service.gcp-pro.consul:9092,esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-2.service.gcp-pro.consul:9092", "Topic": "hotelOfferCacheQuotes", "AutoOffsetReset": "Latest"}, "RepartitionedFlightQuoteProducer": {"BootstrapServers": "esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-0.service.gcp-pro.consul:9092,esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-1.service.gcp-pro.consul:9092,esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-2.service.gcp-pro.consul:9092", "Topic": "repartitionedFlightQuotesNew", "Compression": "Lz4"}, "RepartitionedHotelOfferQuoteProducer": {"BootstrapServers": "esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-0.service.gcp-pro.consul:9092,esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-1.service.gcp-pro.consul:9092,esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-2.service.gcp-pro.consul:9092", "Topic": "repartitionedHotelOfferQuotesNew", "Compression": "Lz4"}}