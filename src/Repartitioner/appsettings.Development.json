{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Esky": "Debug"}}, "Mongo": {"ConnectionString": "mongodb://localhost:27017/esky-packages?retryWrites=true&replicaSet=packages&compressors=snappy,zlib,zstd&w=1&journal=true&readPreference=secondaryPreferred"}, "MongoSearch": {"ConnectionString": "mongodb://localhost:27017/esky-packages-search?retryWrites=true&replicaSet=packages&compressors=snappy,zlib,zstd&w=1&journal=true&readPreference=secondaryPreferred"}, "BloomFilterNotificationConsumer": {"BootstrapServers": "localhost:9092", "Topic": "bloomFilterNotifications"}, "FlightQuoteConsumer": {"ConsumerGroup": "packageRepartitionerNew-dev", "BootstrapServers": "esky-ets-flightscontent-ci.kafka-flightscontent-ci-kafka-node-0.service.gcp-staging.consul:9092,esky-ets-flightscontent-ci.kafka-flightscontent-ci-kafka-node-1.service.gcp-staging.consul:9092,esky-ets-flightscontent-ci.kafka-flightscontent-ci-kafka-node-2.service.gcp-staging.consul:9092", "Topic": "flightQuotes", "AutoOffsetReset": "Latest"}, "HotelOfferQuoteConsumer": {"ConsumerGroup": "packageRepartitionerNew-dev", "BootstrapServers": "localhost:9092", "Topic": "hotelOfferCacheQuotes", "AutoOffsetReset": "Latest"}, "RepartitionedFlightQuoteProducer": {"BootstrapServers": "localhost:9092", "Topic": "repartitionedFlightQuotes", "Compression": "Lz4"}, "RepartitionedHotelOfferQuoteProducer": {"BootstrapServers": "localhost:9092", "Topic": "repartitionedHotelOfferQuotes", "Compression": "Lz4"}}