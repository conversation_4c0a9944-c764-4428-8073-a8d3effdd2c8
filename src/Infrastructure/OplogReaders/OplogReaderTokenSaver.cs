using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.OplogReaders;

public sealed class OplogReaderTokenSaver<TOplogReader>(
    IMongoDatabase database,
    ILogger<OplogReaderTokenSaver<TOplogReader>> logger,
    OplogReader oplogReader,
    string resumeTokenCollectionName,
    int resumeTokenSaveIntervalInMilliseconds)
    : BackgroundService
{
    private IMongoCollection<ResumeToken> _resumeTokens = null!;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        logger.LogInformation("Starting {name} service", $"OplogReaderTokenSaver for {oplogReader.ReaderName}");

        _resumeTokens = database.GetCollection<ResumeToken>(resumeTokenCollectionName);
        while (!stoppingToken.IsCancellationRequested)
        {
            await TrySaveResumeToken();

            try
            {
                await Task.Delay(resumeTokenSaveIntervalInMilliseconds, stoppingToken);
            }
            catch (TaskCanceledException)
            {
                logger.LogInformation("OplogReaderTokenSaver {name} shutting down. Saving the last resume token",
                    oplogReader.ReaderName);

                await TrySaveResumeToken();
                break;
            }
        }
    }

    private async Task TrySaveResumeToken()
    {
        var resumePointToSave = oplogReader.GetResumePointToSave();
        if (resumePointToSave != null)
            await WriteResumeToken(resumePointToSave);
    }

    private async Task WriteResumeToken(ResumePoint resumePoint)
    {
        var resumeToken = new ResumeToken
        {
            Id = oplogReader.ReaderName,
            SavedAt = DateTime.UtcNow,
            ClusterTime = resumePoint.ClusterTime,
            WallTime = resumePoint.WallTime,
            Token = resumePoint.ResumeToken
        };

        try
        {
            var res = await _resumeTokens.ReplaceOneAsync(Builders<ResumeToken>.Filter.Eq("_id", oplogReader.ReaderName),
                resumeToken, new ReplaceOptions { IsUpsert = true });

            if (!res.IsAcknowledged)
            {
                var e = new InvalidOperationException(
                    $"OplogReaderTokenSaver {oplogReader.ReaderName} cannot commit resume token {resumeToken.Token}");

                logger.LogError(e, "OplogReaderTokenSaver {name} cannot commit resume token {token}", oplogReader.ReaderName,
                    resumeToken.Token);

                throw e;
            }
        }
        catch (Exception e)
        {
            logger.LogError(e, "OplogReaderTokenSaver {name} cannot commit resume token {token}", oplogReader.ReaderName,
                resumeToken.Token);
            throw;
        }

        if (resumeToken.WallTime.HasValue)
        {
            logger.LogInformation("OplogReaderTokenSaver {name} wrote resume token from {time}", oplogReader.ReaderName,
                resumeToken.WallTime);
        }
        else
        {
            logger.LogInformation("OplogReaderTokenSaver {name} wrote resume token from ~{time}", oplogReader.ReaderName,
                resumeToken.SavedAt);
        }
    }
}