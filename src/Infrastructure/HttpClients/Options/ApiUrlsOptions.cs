namespace Esky.Packages.Infrastructure.HttpClients.Options;

internal class ApiUrlsOptions
{
    public const string OptionKeyName = "Apis";

    public string FlightCache { get; set; } = default!;
    public string FlightLive { get; set; } = default!;
    public string HotelStatic { get; set; } = default!;
    public string HotelCache { get; set; } = default!;
    public string HotelTransaction { get; set; } = default!;
    public string HotelApi { get; set; } = default!;
    public string CurrencyConverter { get; set; } = default!;
    public ApiSettings OfferAccuracy { get; set; } = default!;
    
    public record ApiSettings(string Url, int TimeoutInMiliseconds);
}