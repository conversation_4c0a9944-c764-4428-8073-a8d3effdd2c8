using System.Text.Json;
using System.Text.Json.Serialization;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Serialization.Converters;

public class RefundabilityJsonConverter : JsonConverter<Refundability>
{
    public override Refundability Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options) 
        => new(reader.GetBoolean());

    public override void Write(Utf8JsonWriter writer, Refundability value, JsonSerializerOptions options)
        => writer.WriteBooleanValue(value.IsRefundable);

    public override Refundability ReadAsPropertyName(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => new(reader.GetBoolean());

    public override void WriteAsPropertyName(Utf8JsonWriter writer, Refundability value, JsonSerializerOptions options)
        => writer.WritePropertyName(value.ToString());
}