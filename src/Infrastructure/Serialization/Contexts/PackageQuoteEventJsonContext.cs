using System.Text.Json.Serialization;
using Esky.Packages.Domain.Events.PackageQuotes;
using Esky.Packages.Infrastructure.Serialization.Converters;

namespace Esky.Packages.Infrastructure.Serialization.Contexts;

[JsonSourceGenerationOptions(
    PropertyNamingPolicy = JsonKnownNamingPolicy.CamelCase,
    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
    Converters =
    [
        typeof(CurrencyJsonConverter), typeof(MealPlanJsonConverter), typeof(PackageOccupancyJsonConverter),
        typeof(AirportJsonConverter), typeof(TimeOfDayJsonConverter)
    ])]
[JsonSerializable(typeof(PackageQuoteEvent))]
[JsonSerializable(typeof(PackageFlightQuoteEvent))]
[JsonSerializable(typeof(PackageHotelOfferQuoteEvent))]
public partial class PackageQuoteEventJsonContext : JsonSerializerContext;