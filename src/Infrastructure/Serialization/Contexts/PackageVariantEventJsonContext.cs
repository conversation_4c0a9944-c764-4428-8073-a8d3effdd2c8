using System.Text.Json.Serialization;
using Esky.Packages.Domain.Events.PackageVariants;
using Esky.Packages.Infrastructure.Serialization.Converters;

namespace Esky.Packages.Infrastructure.Serialization.Contexts;

[JsonSourceGenerationOptions(
    PropertyNamingPolicy = JsonKnownNamingPolicy.CamelCase,
    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
    Converters =
    [
        typeof(MealPlanJsonConverter), typeof(PackageOccupancyJsonConverter),
        typeof(AirportJsonConverter), typeof(TimeOfDayJsonConverter)
    ])]
[JsonSerializable(typeof(PackageVariantEvent))]
[JsonSerializable(typeof(PackageVariantUpdatedEvent))]
[JsonSerializable(typeof(PackageVariantDeletedEvent))]
public partial class PackageVariantEventJsonContext : JsonSerializerContext;