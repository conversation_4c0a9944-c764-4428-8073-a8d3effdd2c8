using System.Text.Json.Serialization;
using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.Infrastructure.Serialization.Converters;

namespace Esky.Packages.Infrastructure.Serialization.Contexts;

[JsonSourceGenerationOptions(
    PropertyNamingPolicy = JsonKnownNamingPolicy.CamelCase,
    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
    Converters =
    [
        typeof(PackageOccupancyJsonConverter),
        typeof(AirportJsonConverter),
        typeof(TimeOfDayJsonConverter)
    ])]
[JsonSerializable(typeof(PackageFlightVariantEvent))]
[JsonSerializable(typeof(PackageFlightVariantUpdatedEvent))]
[JsonSerializable(typeof(PackageFlightVariantDeletedEvent))]
public partial class PackageFlightVariantEventJsonContext : JsonSerializerContext;