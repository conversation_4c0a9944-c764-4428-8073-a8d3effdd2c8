using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Policies;

namespace Esky.Packages.Infrastructure.Factories;

public class PackageFlightFactory(ICurrencyConverterService currencyConverterService) : IPackageFlightFactory
{
    public PackageFlight Create(PackageFlightId id, string definitionId, string partnerCode, Currency currency, 
        PackageOccupancy[] occupancies, FlightOffer[] flightOffers, FlightQuote[] flightQuotes, int[] metaCodes)
    {
        var packageFlight = PackageFlight.Create(id, definitionId, partnerCode, currency, occupancies, flightOffers, 
            flightQuotes, metaCodes, ApplyPolicies);
        
        return packageFlight;
    }
    
    public void ApplyPolicies(PackageFlight packageFlight)
    {
        var currencyConversionPolicy = new CurrencyConversionPolicy(packageFlight.Currency, currencyConverterService);

        packageFlight.ApplyPolicies(currencyConversionPolicy);
    }
}