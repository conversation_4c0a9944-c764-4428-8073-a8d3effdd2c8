using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.LiveVariants;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Policies;

namespace Esky.Packages.Infrastructure.Factories;

public class FlightVariantFactory(ICurrencyConverterService currencyConverterService) : IFlightVariantFactory
{
    public FlightLiveVariant Create(
        string key, 
        string departureAirport, 
        string arrivalAirport, 
        DateTime departureDate, 
        DateTime arrivalDate,
        DateTime returnDepartureDate, 
        DateTime returnArrivalDate, 
        int stops, 
        int providerCode, 
        string[] airlineCodes, 
        string[] flightIds,
        string[] legLocators,
        Currency currency, 
        Money[] prices,
        bool registeredBaggageIncluded)
    {
        var currencyConversionPolicy = new CurrencyConversionPolicy(currency, currencyConverterService);

        var alternativeFlight = FlightLiveVariant.Create(
            key: key,
            departureAirport: departureAirport,
            arrivalAirport: arrivalAirport,
            departureDate: departureDate,
            arrivalDate: arrivalDate,
            returnDepartureDate: returnDepartureDate,
            returnArrivalDate: returnArrivalDate,
            stops: stops,
            providerCode: providerCode,
            airlineCodes: airlineCodes,
            flightIds: flightIds,
            legLocators: legLocators,
            currency: currency,
            prices: prices,
            registeredBaggageIncluded: registeredBaggageIncluded,
            configure: alternativeFlight => alternativeFlight.ApplyPolicies(currencyConversionPolicy));

        return alternativeFlight;
    }
}
