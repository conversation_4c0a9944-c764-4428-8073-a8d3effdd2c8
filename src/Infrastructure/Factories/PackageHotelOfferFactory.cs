using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Policies;

namespace Esky.Packages.Infrastructure.Factories;

public class PackageHotelOfferFactory(ICurrencyConverterService currencyConverterService) : IPackageHotelOfferFactory
{
    public PackageHotelOffer Create(PackageHotelOfferId id, string definitionId, Currency currency, 
        PackageOccupancy[] occupancies, string[] providerConfigurationIds, HotelOfferQuote[] hotelOfferQuotes, 
        Dictionary<Airport, Airport[]> airports, bool mergeIntoPackage)
    {
        var packageHotelOffer = PackageHotelOffer.Create(id, definitionId, currency, occupancies, 
            providerConfigurationIds, airports, hotelOfferQuotes, mergeIntoPackage, ApplyPolicies);

        return packageHotelOffer;
    }

    public void ApplyPolicies(PackageHotelOffer packageHotelOffer)
    {
        var currencyConversionPolicy = new CurrencyConversionPolicy(packageHotelOffer.Currency, 
            currencyConverterService);

        packageHotelOffer.ApplyPolicies(currencyConversionPolicy);
    }
}
