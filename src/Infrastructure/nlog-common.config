<?xml version="1.0" encoding="utf-8"?>

<nlog
	xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	throwExceptions="false"
	throwConfigExceptions="true"
	internalLogLevel="Error"
	internalLogToConsoleError="true"
	internalLogFile="/app/logs.log"
	autoReload="true">
	
	<extensions>
		<add assembly="NLog.Web.AspNetCore"/>
		<add assembly="NLog.Appsettings.Standard" />
		<add assembly="Esky.NLog.RabbitMQ.Target" />
	</extensions>

	<variable name="rabbitmq-clustermembers" value="${appsettings:name=Logging.ServiceBus.Clustermembers}" />
	<variable name="rabbitmq-vhost" value="${appsettings:name=Logging.ServiceBus.Vhost}" />
	<variable name="rabbitmq-username" value="${environment:variable=Logging__ServiceBus__UserName}" />
	<variable name="rabbitmq-password" value="${environment:variable=Logging__ServiceBus__Password}" />

	<variable name="app-name" value="${appsettings:name=Logging.ServiceBus.ApplicationName}" />

	<targets async="true">
		<target name="Rabbit_BQ_Elastic" xsi:type="RabbitMQ" Compression="GZip" username="${var:rabbitmq-username}" password="${var:rabbitmq-password}" vhost="${var:rabbitmq-vhost}" clustermembers="${var:rabbitmq-clustermembers}" >
			<field key="HostName" name="HostName" layout="${machinename}" />
			<field key="Date" name="Date" layout="${date:universalTime=True:format=s}" />
			<field key="Environment" name="Environment" layout="${aspnet-environment}" />
			<field key="Application" name="Application" layout="${var:app-name}" />
			<field key="Exception" name="Exception" layout="${exception:format=Message}"/>
			<field key="AdditionalMessage" name="AdditionalMessage" layout="${exception:format=toString,Data}"/>
			<field key="StackTrace" name="StackTrace" layout="${exception:format=StackTrace}"/>
			<field Key="PipelineId" name="PipelineId" layout="${scopeproperty:pipelineId}" />
			<field key="StructureLog" name="StructureLog" layout="${event-properties:StructureLog:format=@}" />
		</target>

		<target name="JsonConsole" xsi:type="Console">
			<layout xsi:type="JsonLayout">
				<attribute name="logLevel" layout="${level}" />
				<attribute name="hostName" layout="${machinename}" />
				<attribute name="timestamp" layout="${date:universalTime=True:format=s}" />
				<attribute name="environment" layout="${aspnet-environment}" />
				<attribute name="application" layout="${var:app-name}" />
				<attribute name="exception" layout="${exception:format=Message}"/>
				<attribute name="stackTrace" layout="${exception:format=StackTrace}"/>
				<attribute name="pipelineId" layout="${scopeproperty:pipelineId}" />
				<attribute name="message" layout="${message}" />
				<attribute name="logger" layout="${logger}" />
				<attribute name="structureLog" layout="${event-properties:StructureLog:format=@}" />
			</layout>
		</target>

		<target name="BlackHole" xsi:type="Null" formatMessage="false" />
	</targets>

	<rules>
		<logger name="Polly" maxLevel="Info" writeTo="BlackHole" final="true" />
		<logger name="System.Net.Http.HttpClient.*" maxLevel="Info" writeTo="BlackHole" final="true" />
		<logger name="PipelineLogger" maxLevel="Info" writeTo="BlackHole" final="true" />
		<logger name="Microsoft.*" maxLevel="Info" writeTo="BlackHole" final="true" />
		<logger name="*" minlevel="Info" writeTo="Rabbit_BQ_Elastic,JsonConsole" />
	</rules>
</nlog>