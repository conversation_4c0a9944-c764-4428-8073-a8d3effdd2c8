using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Infrastructure.Configuration;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Factories;
using Esky.Packages.Infrastructure.Gateways.CurrencyConverter;
using Esky.Packages.Infrastructure.Gateways.FlightCacheGateway;
using Esky.Packages.Infrastructure.Gateways.FlightLiveGateway;
using Esky.Packages.Infrastructure.Gateways.HotelCacheGateway;
using Esky.Packages.Infrastructure.Gateways.HotelGateway;
using Esky.Packages.Infrastructure.Gateways.HotelsApiGateway;
using Esky.Packages.Infrastructure.Gateways.HotelTransactionGateway;
using Esky.Packages.Infrastructure.Gateways.OfferAccuracyMonitorGateway;
using Esky.Packages.Infrastructure.HttpClients.Options;
using Esky.Packages.Infrastructure.Pipelines;
using Esky.Packages.Infrastructure.Pipelines.Logging;
using Esky.Packages.Infrastructure.Serialization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.Packages.Infrastructure.DependencyInjections;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration, 
        Action<IInfrastructureOptions>? builder = null)
    {
        var options = new InfrastructureOptions(services, configuration);
        builder?.Invoke(options);
        
        services
            .RegisterOptions<ApiUrlsOptions>(configuration, ApiUrlsOptions.OptionKeyName)
            .AddMongo(configuration)
            .AddServices()
            .AddFactories()
            .AddGateways(configuration, options.EnableHttpClientLoggingHandlers)
            .AddJsonSerialization();
        
        return services;
    }

    private static IServiceCollection AddServices(this IServiceCollection services)
    {
        return services
            .AddSingleton<IPipelineIdGenerator, PipelineIdGenerator>();
    }

    private static IServiceCollection AddFactories(this IServiceCollection services)
    {
        return services
            .AddSingleton<IPackageFlightFactory, PackageFlightFactory>()
            .AddSingleton<IPackageHotelOfferFactory, PackageHotelOfferFactory>()
            .AddSingleton<IFlightVariantFactory, FlightVariantFactory>()
            .AddSingleton<IHotelOfferVariantFactory, HotelOfferVariantFactory>()
            .AddSingleton<IPipelineLoggerFactory, PipelineLoggerFactory>();
    }

    private static IServiceCollection AddGateways(this IServiceCollection services, IConfiguration configuration, 
        bool enableLoggingHandlers = false)
    {
        return services
            .AddFlightCacheGateway(enableLoggingHandlers)
            .AddFlightLiveGateway(enableLoggingHandlers)
            .AddHotelGateway(configuration, enableLoggingHandlers)
            .AddHotelCacheGateway(enableLoggingHandlers)
            .AddHotelTransactionGateway(enableLoggingHandlers)
            .AddHotelsLiveSearchGateway(enableLoggingHandlers)
            .AddCurrencyConverterGateway(enableLoggingHandlers)
            .AddOfferAccuracyGateway(enableLoggingHandlers);
    }
}