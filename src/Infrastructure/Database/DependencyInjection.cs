using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors;
using Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias.DurationStrategies;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Infrastructure.Configuration;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using Esky.Packages.Infrastructure.Database.Mappings;
using Esky.Packages.Infrastructure.Database.Options;
using Esky.Packages.Infrastructure.Database.Serializers;
using Esky.Packages.Infrastructure.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Bson.Serialization.Serializers;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Database;

internal static class DependencyInjection
{
    public static IServiceCollection AddMongo(this IServiceCollection services, IConfiguration configuration)
    {
        RegisterConventions();
        RegisterSerializers();
        RegisterClassMappings();

        return services
            .AddPackageDatabase(configuration)
            .AddPackageSearchDatabase(configuration)
            .AddRepository<IMarketRepository, MarketRepository>()
            .AddRepository<IPackageRepository, PackageRepository>()
            .AddRepository<IPackageHotelOfferRepository, PackageHotelOfferRepository>()
            .AddRepository<IPackageFlightRepository, PackageFlightRepository>()
            .AddRepository<IPackageFlightVariantRepository, PackageFlightVariantRepository>()
            .AddRepository<IPackageDefinitionRepository, PackageDefinitionRepository>()
            .AddRepository<IPackageAvailabilitiesRepository, PackageAvailabilitiesRepository>()
            .AddRepository<IPipelineRepository, PipelineRepository>()
            .AddRepository<IPackageHotelAirportsRepository, PackageHotelAirportsRepository>()
            .AddRepository<IPackageVariantRepository, PackageVariantRepository>()
            .AddIndexInitializer();
    }

    private static void RegisterConventions()
    {
        const string conventionsName = "MyConventions";

        var conventions = new ConventionPack
        {
            new CamelCaseElementNameConvention(),
            new IgnoreIfNullConvention(ignoreIfNull: true),
            new IgnoreExtraElementsConvention(ignoreExtraElements: true)
        };
        ConventionRegistry.Register(conventionsName, conventions, filter: _ => true);
    }

    private static void RegisterSerializers()
    {
        BsonSerializer.TryRegisterSerializer(new ObjectSerializer(allowedTypes: _ => true));
        BsonSerializer.TryRegisterSerializer(new DecimalSerializer(BsonType.Decimal128));
        BsonSerializer.TryRegisterSerializer(new AirportSerializer());
        BsonSerializer.TryRegisterSerializer(new CurrencySerializer());
        BsonSerializer.TryRegisterSerializer(new MealPlanSerializer());
        BsonSerializer.TryRegisterSerializer(new PackageFlightPartitionKeySerializer());
        BsonSerializer.TryRegisterSerializer(new PackageOccupancySerializer());
        BsonSerializer.TryRegisterSerializer(new ProviderCodeSerializer());
        BsonSerializer.TryRegisterSerializer(new RefundabilitySerializer());
        BsonSerializer.TryRegisterSerializer(new TimeOfDaySerializer());
        BsonSerializer.TryRegisterSerializer(typeof(Dictionary<int, decimal>), new IntDictionarySerializer<decimal>());
    }

    private static void RegisterClassMappings()
    {
        BsonClassMapping.RegisterClassMapTypeWithJsonDerivedType<HotelSelector>();
        BsonClassMapping.RegisterClassMapTypeWithJsonDerivedType<DurationStrategy>();

        BsonClassMap.TryRegisterClassMap(new PackageIdMap());
        BsonClassMap.TryRegisterClassMap(new PackageMap());
        BsonClassMap.TryRegisterClassMap(new PackageVariantIdMap());
        BsonClassMap.TryRegisterClassMap(new PackageVariantMap());
        BsonClassMap.TryRegisterClassMap(new PackageHotelAirportsIdMap());
        BsonClassMap.TryRegisterClassMap(new PackageVariantAggregationMap());
        BsonClassMap.TryRegisterClassMap(new PackageHotelOfferIdMap());
        BsonClassMap.TryRegisterClassMap(new PackageFlightIdMap());
        BsonClassMap.TryRegisterClassMap(new PackageFlightVariantIdMap());
        BsonClassMap.TryRegisterClassMap(new PackageFlightVariantMap());
        BsonClassMap.TryRegisterClassMap(new PackageAvailabilitiesIdMap());
        BsonClassMap.TryRegisterClassMap(new FlightPriceEntryMap());
        
        BsonClassMap.TryRegisterClassMap(new GenerationAggregatedStatisticsMap());
        BsonClassMap.TryRegisterClassMap(new GenerationDetailedStatisticsMap());
        BsonClassMap.TryRegisterClassMap(new PackageVariantProjectionMap());
        BsonClassMap.TryRegisterClassMap(new PackageFlightVariantProjectionMap());
        BsonClassMap.TryRegisterClassMap(new PackageFlightDepartureAirportsProjectionMap());
    }

    private static IServiceCollection AddPackageDatabase(this IServiceCollection services, IConfiguration configuration)
    {
        return services
            .RegisterOptions<MongoOptions>(configuration, MongoOptions.ConfigurationSection)
            .AddSingleton(s =>
            {
                var options = s.GetRequiredService<MongoOptions>();
                var mongoUrl = CreateMongoUrl(options);

                if (string.IsNullOrEmpty(mongoUrl.DatabaseName))
                    throw new InvalidOperationException("Database not specified in connection string");

                var mongoClient = new MongoClient(mongoUrl);
                var mongoDatabase = mongoClient.GetDatabase(mongoUrl.DatabaseName);

                return new PackageDatabase(mongoDatabase);
            });
    }
    
    private static IServiceCollection AddPackageSearchDatabase(this IServiceCollection services, 
        IConfiguration configuration)
    {
        return services
            .RegisterOptions<MongoSearchOptions>(configuration, MongoSearchOptions.ConfigurationSection)
            .AddSingleton(s =>
            {
                var options = s.GetRequiredService<MongoSearchOptions>();
                var mongoUrl = CreateMongoUrl(options);

                if (string.IsNullOrEmpty(mongoUrl.DatabaseName))
                    throw new InvalidOperationException("Database not specified in connection string");

                var mongoClient = new MongoClient(mongoUrl);
                var mongoDatabase = mongoClient.GetDatabase(mongoUrl.DatabaseName);

                return new PackageSearchDatabase(mongoDatabase);
            });
    }

    private static MongoUrl CreateMongoUrl(MongoOptions options)
    {
        var mongoUrlBuilder = new MongoUrlBuilder(options.ConnectionString);

        if (!string.IsNullOrWhiteSpace(options.Username) && 
            !string.IsNullOrWhiteSpace(options.Password))
        {
            mongoUrlBuilder.Username = options.Username;
            mongoUrlBuilder.Password = options.Password;
        }

        return mongoUrlBuilder.ToMongoUrl();
    }

    private static MongoUrl CreateMongoUrl(MongoSearchOptions options)
    {
        var mongoUrlBuilder = new MongoUrlBuilder(options.ConnectionString);

        if (!string.IsNullOrWhiteSpace(options.Username) && 
            !string.IsNullOrWhiteSpace(options.Password))
        {
            mongoUrlBuilder.Username = options.Username;
            mongoUrlBuilder.Password = options.Password;
        }

        return mongoUrlBuilder.ToMongoUrl();
    }    

    private static IServiceCollection AddRepository<TInterface, TRepository>(this IServiceCollection services)
        where TRepository : class, TInterface
        where TInterface : class
    {
        services
            .AddSingleton<TRepository>()
            .AddSingleton<TInterface>(p => p.GetRequiredService<TRepository>());

        if (typeof(IIndexInitializer).IsAssignableFrom(typeof(TRepository)))
        {
            services.AddSingleton(typeof(IIndexInitializer), p => p.GetRequiredService<TRepository>());
        }

        return services;
    }
}