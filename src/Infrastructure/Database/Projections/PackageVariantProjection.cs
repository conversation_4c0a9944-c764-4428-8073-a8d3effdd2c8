using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Database.Projections;

public class PackageVariantProjection
{
    public int MetaCode { get; init; }
    public int StayLength { get; init; }
    public DateOnly CheckIn { get; init; }
    public Airport DepartureAirport { get; init; }
    public MealPlan MealPlan { get; init; }
    public DateOnly DepartureDate { get; init; }
    public DateOnly ReturnArrivalDate { get; init; }
    public int Price { get; init; }
}