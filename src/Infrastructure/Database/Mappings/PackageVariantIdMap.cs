using Esky.Packages.Domain.Model.PackageVariants;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageVariantIdMap : BsonClassMap<PackageVariantId>
{
    public PackageVariantIdMap()
    {
        MapProperty(x => x.CheckIn)
            .SetElementName("c")
            .SetOrder(1);        
        
        MapProperty(x => x.<PERSON>th)
            .SetElementName("s")
            .SetOrder(2);
        
        MapProperty(x => x.MarketId)
            .SetElementName("k")
            .SetOrder(3);
        
        MapProperty(x => x.MetaCode)
            .SetElementName("m")
            .SetOrder(4);
        
        MapProperty(x => x.DepartureAirport)
            .SetElementName("a")
            .SetOrder(5);
        
        MapProperty(x => x.Occupancy)
            .SetElementName("o")
            .SetOrder(6);
        
        MapProperty(x => x.<PERSON><PERSON>)
            .SetElementName("p")
            .SetOrder(7);
    }
}