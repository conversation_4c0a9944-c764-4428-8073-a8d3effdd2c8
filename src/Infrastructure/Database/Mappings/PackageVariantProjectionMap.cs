using Esky.Packages.Infrastructure.Database.Projections;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageVariantProjectionMap : BsonClassMap<PackageVariantProjection>
{
    public PackageVariantProjectionMap()
    {
        MapProperty(x => x.MetaCode)
            .SetElementName("m");

        MapProperty(x => x.StayLength)
            .SetElementName("s");

        MapProperty(x => x.CheckIn)
            .SetElementName("c");

        MapProperty(x => x.DepartureAirport)
            .SetElementName("a");

        MapProperty(x => x.<PERSON>alPlan)
            .SetElementName("mp");

        MapProperty(x => x.DepartureDate)
            .SetElementName("d");

        MapProperty(x => x.ReturnArrivalDate)
            .SetElementName("ra");

        MapProperty(x => x.Price)
            .SetElementName("p");
    }
}