using Esky.Packages.Domain.Model.Packages;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageMap : BsonClassMap<Package>
{
    public PackageMap()
    {
        MapIdProperty(x => x.Id);
        MapProperty(x => x.FlightPricesByOccupancyByDepartureByArrival).SetElementName("f");
        MapProperty(x => x.HotelOfferPrices).SetElementName("h");
        MapProperty(x => x.RemoveAt).SetElementName("r");
    }
}