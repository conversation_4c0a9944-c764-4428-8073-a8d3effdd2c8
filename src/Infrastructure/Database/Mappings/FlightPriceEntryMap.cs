using Esky.Packages.Domain.Model.Packages;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class FlightPriceEntryMap : BsonClassMap<FlightPriceEntry>
{
    public FlightPriceEntryMap()
    {
        MapProperty(x => x.Price).SetElementName("p");
        MapProperty(x => x.DepartureDate).SetElementName("d");
        MapProperty(x => x.ReturnArrivalDate).SetElementName("ra");
    }
}