using Esky.Packages.Domain.Model.PackageFlightVariants;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageFlightVariantMap : BsonClassMap<PackageFlightVariant>
{
    public PackageFlightVariantMap()
    {
        MapIdProperty(x => x.Id);
        
        MapProperty(x => x.DepartureDate)
            .SetElementName("d")
            .SetOrder(1);
        
        MapProperty(x => x.ReturnArrivalDate)
            .SetElementName("ra")
            .SetOrder(2);
        
        MapProperty(x => x.Price)
            .SetElementName("p")
            .SetOrder(3);
    }
}