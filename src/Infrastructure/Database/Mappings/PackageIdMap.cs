using Esky.Packages.Domain.Model.Packages;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageIdMap : BsonClassMap<PackageId>
{
    public PackageIdMap()
    {
        MapProperty(x => x.CheckIn)
            .SetElementName("c")
            .SetOrder(1);

        MapProperty(x => x.StayLength)
            .SetElementName("s")
            .SetOrder(2);

        MapProperty(x => x.MarketId)
            .SetElementName("k")
            .SetOrder(3);

        MapProperty(x => x.MetaCode)
            .SetElementName("m")
            .SetOrder(4);
    }
}
