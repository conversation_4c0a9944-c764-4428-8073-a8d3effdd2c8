using System.Runtime.CompilerServices;
using System.Threading.Channels;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types.Extensions;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Repositories;

internal class PackageHotelOfferRepository(PackageDatabase database, IPackageHotelOfferFactory factory)
    : IPackageHotelOfferRepository, IIndexInitializer
{
    private const string CollectionName = "packageHotelOffers";

    private readonly IMongoCollection<PackageHotelOffer> _packageHotelOffers =
        database.Database.GetCollection<PackageHotelOffer>(CollectionName);
    
    public async Task<PackageHotelOffer?> GetById(PackageHotelOfferId id, CancellationToken cancellationToken = default)
    {
        var hotelOffer = await _packageHotelOffers
            .Find(x => x.Id == id)
            .FirstOrDefaultAsync(cancellationToken);

        if (hotelOffer != null)
        {
            factory.ApplyPolicies(hotelOffer);
        }

        return hotelOffer;
    }

    public async IAsyncEnumerable<PackageHotelOffer> EnumerateByIds(List<PackageHotelOfferId> ids, int batchSize,
        int maxDegreeOfParallelism, [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        var f = Builders<PackageHotelOffer>.Filter;

        var channel = Channel.CreateUnbounded<PackageHotelOffer>();
        var chunks = ids.Chunk(batchSize).ToList();

        var loop = Parallel.ForAsync(0, chunks.Count, new ParallelOptions
        {
            MaxDegreeOfParallelism = maxDegreeOfParallelism
        }, async (i, c) =>
        {
            var query = f.In(x => x.Id, chunks[i]);

            var cursor = await _packageHotelOffers.Find(query).ToCursorAsync(c);

            while (await cursor.MoveNextAsync(c))
            {
                foreach (var hotelOffer in cursor.Current)
                {
                    factory.ApplyPolicies(hotelOffer);
                    await channel.Writer.WriteAsync(hotelOffer, c);
                }
            }
        });

        var cleanup = Task.Run(async () =>
        {
            try
            {
                await loop;
            }
            finally
            {
                channel.Writer.Complete();
            }
        }, cancellationToken);

        await foreach (var hotelOffer in channel.Reader.ReadAllAsync(cancellationToken))
        {
            yield return hotelOffer;
        }

        await cleanup;
    }

    public async Task<List<PackageHotelOffer>> ListByIds(List<PackageHotelOfferId> ids, 
        CancellationToken cancellationToken)
    {
        var f = Builders<PackageHotelOffer>.Filter;

        var query = f.In(x => x.Id, ids);

        var hotelOffers = await _packageHotelOffers
            .Find(query)
            .ToListAsync(cancellationToken);

        foreach (var hotelOffer in hotelOffers)
        {
            factory.ApplyPolicies(hotelOffer);
        }

        return hotelOffers;
    }

    public async Task<List<PackageHotelOffer>> ListByStayKeys(List<PackageHotelOfferStayKey> stayKeys,
        CancellationToken cancellationToken = default)
    {
        var f = Builders<PackageHotelOffer>.Filter;

        var filters = stayKeys
            .Select(x => f.And(
                f.Eq(p => p.Id.MetaCode, x.MetaCode),
                f.Eq(p => p.Id.CheckIn, x.CheckIn),
                f.Eq(p => p.Id.StayLength, x.StayLength)
            ))
            .ToList();

        var query = f.Or(filters);

        var hotelOffers = await _packageHotelOffers
            .Find(query)
            .ToListAsync(cancellationToken);

        foreach (var hotelOffer in hotelOffers)
        {
            factory.ApplyPolicies(hotelOffer);
        }

        return hotelOffers;
    }

    public async Task UpsertWithoutConcurrency(List<PackageHotelOffer> packageHotelOffers,
        CancellationToken cancellationToken = default)
    {
        var bulkOperations = packageHotelOffers
            .Select(item =>
                new ReplaceOneModel<PackageHotelOffer>(Builders<PackageHotelOffer>.Filter.Eq(d => d.Id, item.Id), item)
                {
                    IsUpsert = true
                });

        var bulkWriteOptions = new BulkWriteOptions
        {
            IsOrdered = false
        };

        await _packageHotelOffers.BulkWriteAsync(bulkOperations, bulkWriteOptions, cancellationToken);
    }

    public Task Update(List<PackageHotelOffer> packageHotelOffers, CancellationToken cancellationToken = default)
    {
        var filter = Builders<PackageHotelOffer>.Filter;

        var batch = packageHotelOffers
            .Select(item => new ReplaceOneModel<PackageHotelOffer>(filter.And(
                filter.Eq(d => d.Id, item.Id),
                filter.Eq(d => d.GeneratedAt, item.GeneratedAt)), item));

        return _packageHotelOffers.BulkWriteAsync(batch, new BulkWriteOptions
        {
            IsOrdered = false,
        }, cancellationToken: cancellationToken);
    }

    public async IAsyncEnumerable<PackageHotelOfferStayKey> EnumerateStayKeys(int chunks = 32,
        int maxDegreeOfParallelism = 4,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var f = Builders<PackageHotelOffer>.Filter;

        // TODO: Get max checkin date from the database
        var minCheckIn = DateOnly.FromDateTime(DateTime.UtcNow);
        var maxCheckIn = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(365));
        
        var dateRanges = DateExtensions.GetDateChunks(minCheckIn, maxCheckIn, chunks);
        
        var projection = Builders<PackageHotelOffer>.Projection
            .Expression(x => new PackageHotelOfferStayKey(x.Id.MetaCode, x.Id.CheckIn, x.Id.StayLength));

        var channel = Channel.CreateUnbounded<PackageHotelOfferStayKey>();

        var loop = Parallel.ForAsync(0, chunks, new ParallelOptions
        {
            MaxDegreeOfParallelism = maxDegreeOfParallelism
        }, async (i, c) =>
        {
            var query = f.And(
                f.Gte(x => x.Id.CheckIn, dateRanges[i].MinDate),
                f.Lt(x => x.Id.CheckIn, dateRanges[i].MaxDate)
            );

            var cursor = await _packageHotelOffers.Find(query, new FindOptions
                {
                    BatchSize = 1000
                })
                .Project(projection)
                .ToCursorAsync(c);

            while (await cursor.MoveNextAsync(c))
            {
                foreach (var stayKey in cursor.Current)
                {
                    await channel.Writer.WriteAsync(stayKey, c);
                }
            }
        });

        var cleanup = Task.Run(async () =>
        {
            try
            {
                await loop;
            }
            finally
            {
                channel.Writer.Complete();
            }
        }, cancellationToken);

        await foreach (var stayKey in channel.Reader.ReadAllAsync(cancellationToken))
        {
            yield return stayKey;
        }

        await cleanup;
    }

    public async Task<int> RemoveExceptSpecificTimeRanges(
        string definitionId,
        DateOnly minCheckIn,
        DateOnly maxCheckIn,
        List<(DateOnly CheckIn, int StayLength)> timeRanges,
        CancellationToken cancellationToken = default)
    {
        var filter = Builders<PackageHotelOffer>.Filter;

        var query = filter.And(
            filter.Where(p => p.DefinitionId == definitionId),
            filter.Where(p => p.Id.CheckIn >= minCheckIn && p.Id.CheckIn <= maxCheckIn),
            timeRanges.Count != 0
                ? filter.Not(filter.Or(timeRanges.Select(r => filter.Where(x => x.Id.CheckIn == r.CheckIn && x.Id.StayLength == r.StayLength))))
                : filter.Empty
        );

        var res = await _packageHotelOffers.DeleteManyAsync(query, cancellationToken);

        return (int)res.DeletedCount;
    }

    public async Task<int> RemoveFromTimeRangesExceptSpecificIds(
        string definitionId,
        List<(DateOnly CheckIn, int StayLength)> timeRanges,
        IEnumerable<PackageHotelOfferId> ids,
        CancellationToken cancellationToken = default)
    {
        var filter = Builders<PackageHotelOffer>.Filter;

        var query = filter.And(
            filter.Where(p => p.DefinitionId == definitionId),
            timeRanges.Count != 0 
                ? filter.Or(timeRanges.Select(r => filter.Where(x => x.Id.CheckIn == r.CheckIn && x.Id.StayLength == r.StayLength)))
                : filter.Empty,
            filter.Not(filter.In(p => p.Id, ids))
        );
        var res = await _packageHotelOffers.DeleteManyAsync(query, cancellationToken);

        return (int)res.DeletedCount;
    }

    public async Task<List<PackageHotelOffer>> ListByMarketAndMetaCode(string marketId, int metaCode, 
        CancellationToken cancellationToken)
    {
        var filter = Builders<PackageHotelOffer>.Filter;

        var query = filter.And(
            filter.Eq(x => x.Id.MetaCode, metaCode),
            filter.Eq(x => x.Id.MarketId, marketId)
        );

        return await _packageHotelOffers
            .Find(query)
            .ToListAsync(cancellationToken);
    }

    public async Task EnsureIndexes()
    {
        await database.Database.CreateCollectionAsync(CollectionName, new CreateCollectionOptions<PackageHotelOffer>
        {
            ChangeStreamPreAndPostImagesOptions = new ChangeStreamPreAndPostImagesOptions
            {
                Enabled = true
            }
        });

        await _packageHotelOffers.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageHotelOffer>(
                Builders<PackageHotelOffer>.IndexKeys
                    .Ascending(x => x.DefinitionId)
                    .Ascending(x => x.Id.CheckIn)
                    .Ascending(x => x.Id.StayLength)
                    .Ascending(x => x.Id)
            )
        );

        await _packageHotelOffers.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageHotelOffer>(
                Builders<PackageHotelOffer>.IndexKeys
                    .Ascending(x => x.Id.CheckIn)
                    .Ascending(x => x.Id.StayLength)
                    .Ascending(x => x.Id.MetaCode)
            )
        );
        
        await _packageHotelOffers.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageHotelOffer>(
                Builders<PackageHotelOffer>.IndexKeys
                    .Ascending(x => x.Id.MarketId)
                    .Ascending(x => x.Id.MetaCode)
            )
        );

        // for sharding purposes
        await _packageHotelOffers.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageHotelOffer>(
                Builders<PackageHotelOffer>.IndexKeys
                    .Hashed(x => x.Id.MetaCode)
            )
        );
    }
}