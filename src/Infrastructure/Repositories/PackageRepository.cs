using Esky.Packages.Domain.Events.PackageQuotes;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Repositories;

internal class PackageRepository(PackageDatabase database) : IPackageRepository, IIndexInitializer
{
    private const string CollectionName = "packages";

    private readonly IMongoCollection<Package> _packages = database.Database.GetCollection<Package>(CollectionName);

    public async Task<Package?> GetById(PackageId id, CancellationToken cancellationToken = default)
    {
        return await _packages
            .Find(x => x.Id == id)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<List<Package>> ListByIds(List<PackageId> packagesIds,
        CancellationToken cancellationToken = default)
    {
        var packages = await _packages
            .Find(x => packagesIds.Contains(x.Id), new FindOptions
            {
                BatchSize = 1000
            })
            .ToListAsync(cancellationToken);

        return packages;
    }

    public async Task ApplyQuotes(List<PackageQuoteEvent> packageQuotes, CancellationToken cancellationToken)
    {
        var groupedUpdated = packageQuotes.GroupBy(p =>
            new PackageId(p.CheckIn, p.StayLength, p.MarketId, p.MetaCode));

        var updates = new List<WriteModel<Package>>();

        foreach (var group in groupedUpdated)
        {
            var filter = Builders<Package>.Filter.Eq(x => x.Id, group.Key);
            var update = CreateUpdate(group);

            updates.Add(new UpdateOneModel<Package>(filter, new PipelineUpdateDefinition<Package>(update))
            {
                IsUpsert = true
            });
        }

        if (updates.Count > 0)
        {
            await _packages.BulkWriteAsync(updates, new BulkWriteOptions
            {
                IsOrdered = false
            }, cancellationToken: cancellationToken);
        }
    }

    public async Task EnsureIndexes()
    {
        await database.Database.CreateCollectionAsync(CollectionName,
            new CreateCollectionOptions<Package>
            {
                ChangeStreamPreAndPostImagesOptions = new ChangeStreamPreAndPostImagesOptions
                {
                    Enabled = true
                }
            }
        );

        // for sharding purposes
        await _packages.Indexes.CreateOneAsync(
            new CreateIndexModel<Package>(
                Builders<Package>.IndexKeys
                    .Hashed(x => x.Id.MetaCode)
            )
        );

        // for autoremove
        await _packages.Indexes.CreateOneAsync(
            new CreateIndexModel<Package>(
                Builders<Package>.IndexKeys.Ascending(x => x.RemoveAt),
                new CreateIndexOptions
                {
                    ExpireAfter = TimeSpan.Zero
                }
            )
        );
    }

    // TODO: Remove hardcoded property names and use shorter names
    private static BsonDocument[] CreateUpdate(IEnumerable<PackageQuoteEvent> packageQuotes)
    {
        var update = new List<BsonDocument>();
        
        PackageHotelOfferQuoteEvent? hotelOfferQuoteEvent = null;
        var flightQuoteEvents =
            new Dictionary<(string ArrivalAirport, string DepartureAirport), PackageFlightQuoteEvent>();

        // take only last event per path in document to prevent conflicts
        foreach (var packageQuote in packageQuotes)
        {
            if (packageQuote is PackageHotelOfferQuoteEvent h)
            {
                hotelOfferQuoteEvent = h;
            }
            else if (packageQuote is PackageFlightQuoteEvent f)
            {
                flightQuoteEvents[(f.ArrivalAirport, f.DepartureAirport)] = f;
            }
            else
            {
                throw new NotSupportedException($"Unsupported package quote event type: {packageQuote.GetType()}");
            }
        }
        
        update.AddRange(GetHotelOfferUpdate(hotelOfferQuoteEvent));
        update.AddRange(GetFlightUpdate(flightQuoteEvents.Values.ToList()));
        update.AddRange(GetRemoveAtUpdate());

        return update.ToArray();
    }
    
    private static BsonDocument[] GetHotelOfferUpdate(PackageHotelOfferQuoteEvent? hotelOfferQuoteEvent)
    {
        if (hotelOfferQuoteEvent == null)
        {
            return [];
        }   
        
        if (hotelOfferQuoteEvent.Prices.Count == 0)
        {
            return [new BsonDocument("$unset", "h")];
        }

        return
        [
            new BsonDocument("$replaceWith", new BsonDocument("$mergeObjects",
                new BsonArray
                {
                    "$$ROOT",
                    new BsonDocument("h", hotelOfferQuoteEvent.Prices.ToBsonDocument())
                }
            ))
        ];
    }
    
    private static BsonDocument[] GetFlightUpdate(List<PackageFlightQuoteEvent> flightQuoteEvents)
    {
        var toUnset = flightQuoteEvents
            .Where(f => f.Prices.Count == 0)
            .GroupBy(f => f.ArrivalAirport)
            .ToDictionary(g => g.Key, g => g.Select(f => f.DepartureAirport).ToArray());

        var toSet = flightQuoteEvents
            .Where(f => f.Prices.Count != 0)
            .GroupBy(f => f.ArrivalAirport)
            .ToDictionary(g => g.Key, g => g.ToDictionary(f => f.DepartureAirport, f => Package.MapPrices(f.Prices)));
        
        var updates = new List<BsonDocument>();
        
        if (toUnset.Count != 0)
        {
            // unset sold out airports
            updates.Add(new BsonDocument("$unset", new BsonArray(GetAirportPaths(toUnset).Select(p => $"f.{p}"))));
        }

        if (toSet.Count != 0)
        {
            // update prices
            var set = new BsonDocument();
            foreach (var (arrivalAirport, departureAirportEntry) in toSet)
            {
                foreach (var (departureAirport, price) in departureAirportEntry)
                {
                    set[$"f.{arrivalAirport}.{departureAirport}"] = price.ToBsonDocument();
                }
            }
            
            updates.Add(new BsonDocument("$set", set));
        }

        if (toUnset.Count != 0)
        {
            var airportsToClear = toUnset.Keys.Except(toSet.Keys).ToList();
            if (airportsToClear.Count != 0)
            {
                // clear airports that are not set
                var airportsClear = new BsonDocument();
                foreach (var airportToClear in airportsToClear)
                {
                    airportsClear[$"f.{airportToClear}"] = new BsonDocument
                    {
                        {
                            "$cond", new BsonArray
                            {
                                new BsonDocument("$eq", new BsonArray
                                {
                                    $"$f.{airportToClear}",
                                    new BsonDocument()
                                }),
                                "$$REMOVE",
                                $"$f.{airportToClear}"
                            }
                        }
                    };
                }

                updates.Add(new BsonDocument("$set", airportsClear));
            }
        }
        
        
        
        return updates.ToArray();
    }

    private static BsonDocument[] GetRemoveAtUpdate()
    {
        var update = new List<BsonDocument>
        {
            new("$set", new BsonDocument
            {
                {
                    "r", new BsonDocument
                    {
                        {
                            "$cond", new BsonArray
                            {
                                new BsonDocument("$and", new BsonArray
                                {
                                    new BsonDocument("$not", new BsonDocument("$ifNull", new BsonArray { "$h", false })),
                                    new BsonDocument("$or", new BsonArray
                                    {
                                        new BsonDocument("$eq", new BsonArray { "$f", new BsonDocument() }),
                                        new BsonDocument("$not", new BsonDocument("$ifNull", new BsonArray { "$f", false }))
                                    })
                                }),
                                "$$NOW",
                                "$$REMOVE"
                            }
                        }
                    }
                }
            })
        };

        return update.ToArray();
    }

    private static string[] GetAirportPaths(Dictionary<Airport, Airport[]> airports)
    {
        var paths = new List<string>();
        
        foreach (var (arrivalAirport, departureAirports) in airports)
        {
            foreach (var departureAirport in departureAirports)
            {
                paths.Add($"{arrivalAirport}.{departureAirport}");
            }
        }
        
        return paths.ToArray();
    }

    private static List<string> GetArrivalAirportsToClear(
        IEnumerable<(string ArrivalAirport, string DepartureAirport)> flightsUnset,
        IEnumerable<(string ArrivalAirport, string DepartureAirport)> flightsSet)
    {
        var arrivalAirportsUnset = flightsUnset.Select(f => f.ArrivalAirport).ToList();
        var arrivalAirportsSet = flightsSet.Select(f => f.ArrivalAirport).ToList();

        return arrivalAirportsUnset.Except(arrivalAirportsSet).ToList();
    }
}