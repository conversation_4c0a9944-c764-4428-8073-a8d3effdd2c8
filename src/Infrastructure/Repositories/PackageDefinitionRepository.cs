using Esky.Packages.Domain.Model.PackageDefinitions;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Repositories;

internal class PackageDefinitionRepository(PackageDatabase database) : IPackageDefinitionRepository, IIndexInitializer
{
    private const string CollectionName = "packageDefinitions";

    private readonly IMongoCollection<PackageDefinition> _packageDefinitions = database.Database.GetCollection<PackageDefinition>(CollectionName);

    public async Task Add(PackageDefinition packageDefinition, CancellationToken cancellationToken = default)
    {
        if (packageDefinition.Version != 0)
        {
            throw new InvalidOperationException("Version should be 0 for Add");
        }

        await _packageDefinitions.InsertOneAsync(packageDefinition, cancellationToken: cancellationToken);
    }

    public async Task<PackageDefinition> Update(PackageDefinition packageDefinition, CancellationToken cancellationToken = default)
    {
        var filter = Builders<PackageDefinition>.Filter.And(
            Builders<PackageDefinition>.Filter.Eq(x => x.Id, packageDefinition.Id),
            Builders<PackageDefinition>.Filter.Eq(x => x.Version, packageDefinition.Version));

        var update = Builders<PackageDefinition>.Update
            .Set(x => x.Parameters, packageDefinition.Parameters)
            .Set(x => x.Metadata, packageDefinition.Metadata)
            .Set(x => x.UpdatedAt, DateTime.UtcNow)
            .Inc(x => x.Version, 1);

        var options = new FindOneAndUpdateOptions<PackageDefinition>
        {
            ReturnDocument = ReturnDocument.After,
            IsUpsert = false
        };

        var updatedDocument = await _packageDefinitions.FindOneAndUpdateAsync(filter, update, options, cancellationToken);
        if (updatedDocument is null)
        {
            throw new InvalidOperationException("Document was already modified - version missmatch.");
        }

        return updatedDocument;
    }

    public async Task<PackageDefinition?> GetById(string id, CancellationToken cancellationToken = default)
    {
        return await _packageDefinitions
            .Find(x => x.Id == id)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<List<PackageDefinition>> ListByIdsOrTags(
        string[] ids, 
        string[] tags, 
        CancellationToken cancellationToken)
    {
        return await _packageDefinitions
            .Find(x => ids.Contains(x.Id) || tags.Any(t => x.Metadata.Tags.Contains(t)))
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> Remove(string id, CancellationToken cancellationToken = default)
    {
        var result = await _packageDefinitions
            .DeleteOneAsync(Builders<PackageDefinition>.Filter.Eq(f => f.Id, id), cancellationToken);

        return result.IsAcknowledged && result.DeletedCount == 1;
    }

    public async Task<IReadOnlyCollection<PackageDefinition>> GetAll(CancellationToken cancellationToken = default)
    {
        return await _packageDefinitions
            .Find(_ => true)
            .ToListAsync(cancellationToken);
    }

    public async Task EnsureIndexes()
    {
        await database.Database.CreateCollectionAsync(CollectionName);
    }
}