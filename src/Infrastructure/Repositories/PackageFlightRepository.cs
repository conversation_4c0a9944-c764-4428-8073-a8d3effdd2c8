using System.Runtime.CompilerServices;
using System.Threading.Channels;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types.Extensions;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Repositories;

internal class PackageFlightRepository(PackageDatabase database, IPackageFlightFactory factory)
    : IPackageFlightRepository, IIndexInitializer
{
    private const string CollectionName = "packageFlights";

    private readonly IMongoCollection<PackageFlight> _packageFlights =
        database.Database.GetCollection<PackageFlight>(CollectionName);

    public async Task<PackageFlight?> GetById(PackageFlightId id, CancellationToken cancellationToken = default)
    {
        var package = await _packageFlights
            .Find(x => x.Id == id)
            .FirstOrDefaultAsync(cancellationToken);

        if (package != null)
        {
            factory.ApplyPolicies(package);
        }

        return package;
    }

    public async Task<List<PackageFlight>> ListByIds(List<PackageFlightId> packageFlightsIds,
        CancellationToken cancellationToken = default)
    {
        var packages = await _packageFlights
            .Find(x => packageFlightsIds.Contains(x.Id), new FindOptions
            {
                BatchSize = 1000
            })
            .ToListAsync(cancellationToken);

        foreach (var package in packages)
        {
            factory.ApplyPolicies(package);
        }

        return packages;
    }

    public async IAsyncEnumerable<PackageFlight> EnumerateByIds(List<PackageFlightId> ids, int batchSize,
        int maxDegreeOfParallelism, [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        var f = Builders<PackageFlight>.Filter;

        var channel = Channel.CreateUnbounded<PackageFlight>();
        var chunks = ids.Chunk(batchSize).ToList();

        var loop = Parallel.ForAsync(0, chunks.Count, new ParallelOptions
        {
            MaxDegreeOfParallelism = maxDegreeOfParallelism
        }, async (i, c) =>
        {
            var query = f.In(x => x.Id, chunks[i]);

            var cursor = await _packageFlights.Find(query).ToCursorAsync(c);

            while (await cursor.MoveNextAsync(c))
            {
                foreach (var package in cursor.Current)
                {
                    factory.ApplyPolicies(package);
                    await channel.Writer.WriteAsync(package, c);
                }
            }
        });

        var cleanup = Task.Run(async () =>
        {
            try
            {
                await loop;
            }
            finally
            {
                channel.Writer.Complete();
            }
        }, cancellationToken);

        await foreach (var package in channel.Reader.ReadAllAsync(cancellationToken))
        {
            yield return package;
        }

        await cleanup;
    }

    public async Task<List<PackageFlight>> ListByFlightIdAndPartitionKeyPairs(
        List<(string FlightId, PackageFlightPartitionKey PartitionKey)> flightIdAndPartitionKeyPairs,
        CancellationToken cancellationToken = default)
    {
        // TODO: Use nameof or dbname and serializers
        var filters = flightIdAndPartitionKeyPairs.Select(p => new BsonDocument
        {
            { "flightOffers.flights._id", p.Item1 },
            { "partitionKey", p.Item2.ToString() }
        });

        var query = new BsonDocument
        {
            { "$or", new BsonArray(filters) }
        };

        var packages = await _packageFlights
            .Find(query)
            .ToListAsync(cancellationToken);

        foreach (var package in packages)
        {
            factory.ApplyPolicies(package);
        }

        return packages;
    }

    public async Task UpsertWithoutConcurrency(List<PackageFlight> packageFlights,
        CancellationToken cancellationToken = default)
    {
        var bulkOperations = packageFlights
            .Select(item =>
                new ReplaceOneModel<PackageFlight>(Builders<PackageFlight>.Filter.Eq(d => d.Id, item.Id), item)
                {
                    IsUpsert = true
                });

        var bulkWriteOptions = new BulkWriteOptions
        {
            IsOrdered = false
        };

        await _packageFlights.BulkWriteAsync(bulkOperations, bulkWriteOptions, cancellationToken);
    }

    public Task Update(List<PackageFlight> packageFlights, CancellationToken cancellationToken = default)
    {
        var filter = Builders<PackageFlight>.Filter;

        var batch = packageFlights
            .Select(item => new ReplaceOneModel<PackageFlight>(filter.And(
                filter.Eq(d => d.Id, item.Id),
                filter.Eq(d => d.GeneratedAt, item.GeneratedAt)), item));

        return _packageFlights.BulkWriteAsync(batch, new BulkWriteOptions
        {
            IsOrdered = false
        }, cancellationToken: cancellationToken);
    }

    // TODO: Try different approach that doesn't require checkIn in the index. Maybe use airports (partitionKey)
    public async IAsyncEnumerable<string> EnumerateFlightIds(int chunks = 32, int maxDegreeOfParallelism = 4,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        // TODO: Get max checkin date from the database
        var minCheckIn = DateOnly.FromDateTime(DateTime.UtcNow);
        var maxCheckIn = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(365));
        
        var dateRanges = DateExtensions.GetDateChunks(minCheckIn, maxCheckIn, chunks);
        
        var projection = Builders<PackageFlight>.Projection
            .Expression(x => x.FlightOffers.SelectMany(r => r.Flights).Select(f => f.Id));

        var channel = Channel.CreateUnbounded<IEnumerable<string>>();

        var loop = Parallel.ForAsync(0, chunks, new ParallelOptions
        {
            MaxDegreeOfParallelism = maxDegreeOfParallelism
        }, async (i, c) =>
        {
            var query = Builders<PackageFlight>.Filter.And(
                Builders<PackageFlight>.Filter.Gte(x => x.Id.CheckIn, dateRanges[i].MinDate),
                Builders<PackageFlight>.Filter.Lt(x => x.Id.CheckIn, dateRanges[i].MaxDate)
            );

            var cursor = await _packageFlights.Find(query)
                .Project(projection)
                .ToCursorAsync(c);

            while (await cursor.MoveNextAsync(c))
            {
                foreach (var flightIds in cursor.Current)
                {
                    await channel.Writer.WriteAsync(flightIds, c);
                }
            }
        });

        var cleanup = Task.Run(async () =>
        {
            try
            {
                await loop;
            }
            finally
            {
                channel.Writer.Complete();
            }
        }, cancellationToken);

        await foreach (var flightIds in channel.Reader.ReadAllAsync(cancellationToken))
        {
            foreach (var flightId in flightIds)
            {
                yield return flightId;
            }
        }

        await cleanup;
    }

    public async Task<int> RemoveExceptSpecificTimeRanges(
        string definitionId,
        DateOnly minCheckIn,
        DateOnly maxCheckIn,
        List<(DateOnly CheckIn, int StayLength)> timeRanges,
        CancellationToken cancellationToken = default)
    {
        var filter = Builders<PackageFlight>.Filter;

        var query = filter.And(
            filter.Where(p => p.DefinitionId == definitionId),
            filter.Where(p => p.Id.CheckIn >= minCheckIn && p.Id.CheckIn <= maxCheckIn),
            timeRanges.Count != 0
                ? filter.Not(filter.Or(timeRanges.Select(r => filter.Where(x => x.Id.CheckIn == r.CheckIn && x.Id.StayLength == r.StayLength))))
                : filter.Empty
        );

        var res = await _packageFlights.DeleteManyAsync(query, cancellationToken);

        return (int)res.DeletedCount;
    }

    public async Task<int> RemoveFromTimeRangesExceptSpecificIds(
        string definitionId,
        List<(DateOnly CheckIn, int[] StayLengths)> timeRanges,
        IEnumerable<PackageFlightId> ids,
        CancellationToken cancellationToken = default)
    {
        var filter = Builders<PackageFlight>.Filter;

        var query = filter.And(
            filter.Where(p => p.DefinitionId == definitionId),
            timeRanges.Count != 0 
                ? filter.Or(timeRanges.SelectMany(r => r.StayLengths.Select(stayLength => filter.Where(x => x.Id.CheckIn == r.CheckIn && x.Id.StayLength == stayLength))))
                : filter.Empty,
            filter.Not(filter.In(p => p.Id, ids))
        );
        var res = await _packageFlights.DeleteManyAsync(query, cancellationToken);

        return (int)res.DeletedCount;
    }

    public async Task EnsureIndexes()
    {
        await database.Database.CreateCollectionAsync(CollectionName, new CreateCollectionOptions<PackageFlight>
        {
            ChangeStreamPreAndPostImagesOptions = new ChangeStreamPreAndPostImagesOptions
            {
                Enabled = true
            }
        });

        await _packageFlights.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageFlight>(
                Builders<PackageFlight>.IndexKeys
                    .Ascending("FlightOffers.Flights.Id") // TODO: Use nameof or dbname
            )
        );

        // TODO: Remove when not needed by Repartitioner
        await _packageFlights.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageFlight>(
                Builders<PackageFlight>.IndexKeys
                    .Ascending(x => x.Id.CheckIn)
                    .Ascending("FlightOffers.Flights.Id") // TODO: Use nameof or dbname
            )
        );

        await _packageFlights.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageFlight>(
                Builders<PackageFlight>.IndexKeys
                    .Ascending(x => x.DefinitionId)
                    .Ascending(x => x.Id.CheckIn)
                    .Ascending(x => x.Id.StayLength)
                    .Ascending(x => x.Id)
            )
        );

        // for sharding purposes
        await _packageFlights.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageFlight>(
                Builders<PackageFlight>.IndexKeys
                    .Hashed(x => x.PartitionKey)
            )
        );
    }
}