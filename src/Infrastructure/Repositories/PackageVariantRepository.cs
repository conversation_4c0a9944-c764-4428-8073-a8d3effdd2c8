using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using Esky.Packages.Infrastructure.Database.Projections;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Repositories;

public class PackageVariantRepository(PackageSearchDatabase database) : IPackageVariantRepository, IIndexInitializer
{
    private const string CollectionName = "packageVariants";
    
    private readonly IMongoCollection<PackageVariant> _packageVariants =
        database.Database.GetCollection<PackageVariant>(CollectionName);

    public async Task<List<PackageVariant>> GetCheapestsPerMetaCode(List<int> metaCodes, List<int> stayLengths, string marketId, 
        List<MealPlan> mealPlans, PackageOccupancy occupancy, DateOnly departureDateFrom, DateOnly departureDateTo, 
        List<Airport> departureAirports, CancellationToken cancellationToken)
    {
        var match = new BsonDocument("$match",
            new BsonDocument
            {
                { "_id.o", occupancy.ToString() },
                { "_id.k", marketId },
                {
                    "_id.s", new BsonDocument("$in", new BsonArray(stayLengths))
                },
                {
                    "_id.m", new BsonDocument("$in", new BsonArray(metaCodes))
                },
                {
                    "$and",
                    new BsonArray
                    {
                        new BsonDocument("d", new BsonDocument("$gte", departureDateFrom.ToDateTime(TimeOnly.MinValue))),
                        new BsonDocument("d", new BsonDocument("$lte", departureDateTo.ToDateTime(TimeOnly.MaxValue))),
                    }
                }
            });
        
        if (mealPlans.Count > 0)
        {
            match["$match"]["_id.p"] = new BsonDocument("$in", new BsonArray(mealPlans.Select(x => x.ToShortString())));
        }

        if (departureAirports.Count > 0)
        {
            match["$match"]["_id.a"] = new BsonDocument("$in", new BsonArray(departureAirports.Select(x => x.ToString())));
        }
        
        var pipeline = new[]
        {
            match,
            new("$group",
                new BsonDocument
                {
                    { "_id", "$_id.m" },
                    {
                        "m",
                        new BsonDocument("$first",
                            new BsonDocument
                            {
                                { "p", "$p" },
                                { "d", "$d" },
                                { "a", "$_id.a" },
                                { "mp", "$_id.p" },
                                { "s", "$_id.s" },
                                { "c", "$_id.c" },
                                { "ra", "$ra" },
                            })
                    }
                }),
            new("$project",
                new BsonDocument
                {
                    { "m", "$_id" },
                    { "s", "$m.s" },
                    { "c", "$m.c" },
                    { "p", "$m.p" },
                    { "a", "$m.a" },
                    { "mp", "$m.mp" },
                    { "d", "$m.d" },
                    { "ra", "$m.ra" },
                    { "_id", 0 }
                })
        };

        var cursor = await _packageVariants.AggregateAsync<PackageVariantProjection>(pipeline,
            cancellationToken: cancellationToken);
        var result = await cursor.ToListAsync(cancellationToken);

        return result.Select(p => PackageVariant.Create(
            checkIn: p.CheckIn,
            departureAirport: p.DepartureAirport,
            mealPlan: p.MealPlan,
            marketId: marketId,
            metaCode: p.MetaCode,
            occupancy: occupancy,
            price: p.Price,
            returnArrivalDate: p.ReturnArrivalDate,
            stayLength: p.StayLength,
            departureDate: p.DepartureDate)).ToList();
    }

    public async Task<List<PackageVariant>> GetCheapestsPerDepartureDate(int metaCode, PackageOccupancy occupancy, 
        string marketId, List<MealPlan> mealPlans, List<Airport> departureAirports, 
        CancellationToken cancellationToken = default)
    {
        var match = new BsonDocument("$match", new BsonDocument
        {
            { "_id.m", metaCode },
            { "_id.o", occupancy.ToString() },
            { "_id.k", marketId }
        });

        if (mealPlans.Count > 0)
        {
            match["$match"]["_id.p"] = new BsonDocument("$in", new BsonArray(mealPlans.Select(x => x.ToShortString())));
        }

        if (departureAirports.Count > 0)
        {
            match["$match"]["_id.a"] = new BsonDocument("$in", new BsonArray(departureAirports.Select(x => x.ToString())));
        }

        var pipeline = new[]
        {
            match,
            new BsonDocument
            {
                {
                    "$group", new BsonDocument
                    {
                        { "_id", new BsonDocument { { "d", "$d" }, { "s", "$_id.s" } } },
                        {
                            "m", new BsonDocument
                            {
                                {
                                    "$min", new BsonDocument
                                    {
                                        { "p", "$p" },
                                        { "m", "$_id.m"},
                                        { "a", "$_id.a" },
                                        { "mp", "$_id.p" },
                                        { "c", "$_id.c" },
                                        { "ra", "$ra" },
                                    }
                                }
                            }
                        }
                    }
                }
            },
            new BsonDocument
            {
                {
                    "$project", new BsonDocument
                    {
                        { "m", "$m.m"},
                        { "s", "$_id.s" },
                        { "c", "$m.c" },
                        { "p", "$m.p" },
                        { "a", "$m.a" },
                        { "mp", "$m.mp" },
                        { "d", "$_id.d" },
                        { "ra", "$m.ra" },
                        { "_id", 0 },
                    }
                }
            }
        };

        var cursor = await _packageVariants.AggregateAsync<PackageVariantProjection>(pipeline,
            cancellationToken: cancellationToken);
        var result = await cursor.ToListAsync(cancellationToken);

        return result.Select(p => PackageVariant.Create(
            checkIn: p.CheckIn,
            departureAirport: p.DepartureAirport,
            mealPlan: p.MealPlan,
            marketId: marketId,
            metaCode: p.MetaCode,
            occupancy: occupancy,
            price: p.Price,
            returnArrivalDate: p.ReturnArrivalDate,
            stayLength: p.StayLength,
            departureDate: p.DepartureDate)).ToList();
    }

    public async Task<PackageVariantAggregation> GetPackageVariantAggregation(int metaCode, string marketId, 
        CancellationToken cancellationToken = default)
    {
        var pipeline = new[]
        {
            new BsonDocument
            {
                {
                    "$match", new BsonDocument
                    {
                        { "_id.m", metaCode },
                        { "_id.k", marketId }
                    }
                },
            },
            new BsonDocument
            {
                {
                    "$group", new BsonDocument
                    {
                        { "_id", "$_id.m" },
                        { "a", new BsonDocument { { "$addToSet", "$_id.a" } } },
                        { "mp", new BsonDocument { { "$addToSet", "$_id.p" } } },
                        { "o", new BsonDocument { { "$addToSet", "$_id.o" } } }
                    }
                }
            },
            new BsonDocument
            {
                {
                    "$project", new BsonDocument
                    {
                        { "_id", 0 },
                        { "a", 1 },
                        { "mp", 1 },
                        { "o", 1 },
                    }
                }
            }
        };

        var cursor = await _packageVariants.AggregateAsync<PackageVariantAggregation>(pipeline,
            cancellationToken: cancellationToken);
        var result = await cursor.SingleOrDefaultAsync(cancellationToken);
        
        if (result == null)
        {
            return PackageVariantAggregation.Empty;
        }

        return result;
    }

    public async Task EnsureIndexes()
    {
        await database.Database.CreateCollectionAsync(CollectionName);
        
        await _packageVariants.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageVariant>(
                Builders<PackageVariant>.IndexKeys
                    .Ascending(x => x.Id.MetaCode)
                    .Ascending(x => x.Id.MarketId)
                    .Ascending(x => x.Id.Occupancy)
                    .Ascending(x => x.Price)
                    .Ascending(x => x.Id.StayLength)
                    .Ascending(x => x.Id.DepartureAirport)
                    .Ascending(x => x.Id.MealPlan)
                    .Ascending(x => x.DepartureDate)
            )
        );

        // for sharding purposes
        await _packageVariants.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageVariant>(
                Builders<PackageVariant>.IndexKeys
                    .Hashed(x => x.Id.MetaCode)
            )
        );
    }
}