using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelAirports;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Repositories;

public class PackageHotelAirportsRepository(PackageDatabase packageDatabase) : IPackageHotelAirportsRepository, IIndexInitializer
{
    private const string CollectionName = "packageHotelAirports";
    
    private readonly IMongoCollection<PackageHotelAirports> _packageHotelAirports = packageDatabase.Database.GetCollection<PackageHotelAirports>(CollectionName);

    public async Task<List<PackageHotelAirports>> ListByMarketIdAndMetaCodes(string marketId, IEnumerable<int> metaCodes,
        CancellationToken cancellationToken = default)
    {
        return await _packageHotelAirports
            .Find(x => x.Id.MarketId == marketId && metaCodes.Contains(x.Id.MetaCode))
            .ToListAsync(cancellationToken);
    }
    
    public async Task Upsert(
        List<PackageHotelAirports> packageHotelAirports,
        CancellationToken cancellationToken)
    {
        var bulkOperations = packageHotelAirports
            .Select(item =>
                new ReplaceOneModel<PackageHotelAirports>(Builders<PackageHotelAirports>.Filter.Eq(x => x.Id, item.Id),
                    item)
                {
                    IsUpsert = true
                });

        var bulkWriteOptions = new BulkWriteOptions
        {
            IsOrdered = false
        };
        
        await _packageHotelAirports.BulkWriteAsync(bulkOperations, bulkWriteOptions, cancellationToken);
    }
    
    public async Task<int> RemoveExceptMetaCodes(
        string marketId,
        int[] metaCodes,
        string definitionId,
        CancellationToken cancellationToken = default)
    {
        var filter = Builders<PackageHotelAirports>.Filter;
        
        var query = filter.And(
            filter.Eq(x => x.Id.MarketId, marketId),
            filter.Nin(x => x.Id.MetaCode, metaCodes),
            filter.Eq(x => x.DefinitionId, definitionId)
        );
        
        var result = await _packageHotelAirports.DeleteManyAsync(query, cancellationToken);
        
        return (int)result.DeletedCount;
    }

    public async Task EnsureIndexes()
    {
        await packageDatabase.Database.CreateCollectionAsync(CollectionName);
        
        await _packageHotelAirports.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageHotelAirports>(
                Builders<PackageHotelAirports>.IndexKeys
                    .Ascending(x => x.Id.MarketId)
                    .Ascending(x => x.Id.MetaCode)
            )
        );
    }
}