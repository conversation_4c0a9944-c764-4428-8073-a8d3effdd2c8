//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

#pragma warning disable 108 // Disable "CS0108 '{derivedDto}.To<PERSON><PERSON>()' hides inherited member '{dtoBase}.To<PERSON>son()'. Use the new keyword if hiding was intended."
#pragma warning disable 114 // Disable "CS0114 '{derivedDto}.RaisePropertyChanged(String)' hides inherited member 'dtoBase.RaisePropertyChanged(String)'. To make the current member override that implementation, add the override keyword. Otherwise add the new keyword."
#pragma warning disable 472 // Disable "CS0472 The result of the expression is always 'false' since a value of type 'Int32' is never equal to 'null' of type 'Int32?'
#pragma warning disable 612 // Disable "CS0612 '...' is obsolete"
#pragma warning disable 649 // Disable "CS0649 Field is never assigned to, and will always have its default value null"
#pragma warning disable 1573 // Disable "CS1573 Parameter '...' has no matching param tag in the XML comment for ...
#pragma warning disable 1591 // Disable "CS1591 Missing XML comment for publicly visible type or member ..."
#pragma warning disable 8073 // Disable "CS8073 The result of the expression is always 'false' since a value of type 'T' is never equal to 'null' of type 'T?'"
#pragma warning disable 3016 // Disable "CS3016 Arrays as attribute arguments is not CLS-compliant"
#pragma warning disable 8603 // Disable "CS8603 Possible null reference return"
#pragma warning disable 8604 // Disable "CS8604 Possible null reference argument for parameter"
#pragma warning disable 8625 // Disable "CS8625 Cannot convert null literal to non-nullable reference type"
#pragma warning disable 8765 // Disable "CS8765 Nullability of type of parameter doesn't match overridden member (possibly because of nullability attributes)."

namespace Infrastructure.Gateways.HotelTransactionGateway.HotelTransaction
{
    using System = global::System;

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelTransactionClient 
    {
        private System.Net.Http.HttpClient _httpClient;
        private static System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(CreateSerializerSettings, true);
        private Newtonsoft.Json.JsonSerializerSettings _instanceSettings;

    #pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public HotelTransactionClient(System.Net.Http.HttpClient httpClient)
    #pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        {
            _httpClient = httpClient;
            Initialize();
        }

        private static Newtonsoft.Json.JsonSerializerSettings CreateSerializerSettings()
        {
            var settings = new Newtonsoft.Json.JsonSerializerSettings();
            UpdateJsonSerializerSettings(settings);
            return settings;
        }

        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _instanceSettings ?? _settings.Value; } }

        static partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);

        partial void Initialize();

        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<HotelPriceCheckResult> PriceCheckAsync(string version, HotelPriceCheckParameters body)
        {
            return PriceCheckAsync(version, body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<HotelPriceCheckResult> PriceCheckAsync(string version, HotelPriceCheckParameters body, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = Newtonsoft.Json.JsonConvert.SerializeObject(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.StringContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/PriceCheck"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/PriceCheck");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<HotelPriceCheckResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<HotelPriceResult> PricingAsync(string version, object esky_hotel_pricing_contextname, HotelPriceParameters body)
        {
            return PricingAsync(version, esky_hotel_pricing_contextname, body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<HotelPriceResult> PricingAsync(string version, object esky_hotel_pricing_contextname, HotelPriceParameters body, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {

                    if (esky_hotel_pricing_contextname != null)
                        request_.Headers.TryAddWithoutValidation("esky-hotel-pricing-contextname", ConvertToString(esky_hotel_pricing_contextname, System.Globalization.CultureInfo.InvariantCulture));
                    var json_ = Newtonsoft.Json.JsonConvert.SerializeObject(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.StringContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/Pricing"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Pricing");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<HotelPriceResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Bad Request", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 404)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Not Found", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 410)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Gone", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 422)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Unprocessable Content", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 500)
                        {
                            string responseText_ = ( response_.Content == null ) ? string.Empty : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("Internal Server Error", status_, responseText_, headers_, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<OffersRepriceResult> OfferAsync(string version, OffersRepriceParameters body)
        {
            return OfferAsync(version, body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<OffersRepriceResult> OfferAsync(string version, OffersRepriceParameters body, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = Newtonsoft.Json.JsonConvert.SerializeObject(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.StringContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/Repricing/Offer"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Repricing/Offer");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<OffersRepriceResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Bad Request", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 500)
                        {
                            string responseText_ = ( response_.Content == null ) ? string.Empty : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("Internal Server Error", status_, responseText_, headers_, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<HotelRepriceResult> HotelAsync(string version, HotelRepriceParameters body)
        {
            return HotelAsync(version, body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<HotelRepriceResult> HotelAsync(string version, HotelRepriceParameters body, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = Newtonsoft.Json.JsonConvert.SerializeObject(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.StringContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/Repricing/Hotel"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Repricing/Hotel");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<HotelRepriceResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Bad Request", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 500)
                        {
                            string responseText_ = ( response_.Content == null ) ? string.Empty : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("Internal Server Error", status_, responseText_, headers_, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Created</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<HotelBookResult> ReservationPOSTAsync(string version, HotelBookParameters body)
        {
            return ReservationPOSTAsync(version, body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Created</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<HotelBookResult> ReservationPOSTAsync(string version, HotelBookParameters body, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = Newtonsoft.Json.JsonConvert.SerializeObject(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.StringContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/Reservation"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Reservation");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 201)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<HotelBookResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 500)
                        {
                            string responseText_ = ( response_.Content == null ) ? string.Empty : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("Internal Server Error", status_, responseText_, headers_, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<GetReservationResult> ReservationGETAsync(string providerBookingId, string confirmationId, int? providerCode, string partnerCode, string locale, string version)
        {
            return ReservationGETAsync(providerBookingId, confirmationId, providerCode, partnerCode, locale, version, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<GetReservationResult> ReservationGETAsync(string providerBookingId, string confirmationId, int? providerCode, string partnerCode, string locale, string version, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/Reservation"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Reservation");
                    urlBuilder_.Append('?');
                    if (providerBookingId != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ProviderBookingId")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(providerBookingId, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (confirmationId != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ConfirmationId")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(confirmationId, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (providerCode != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ProviderCode")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(providerCode, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (partnerCode != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("PartnerCode")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(partnerCode, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (locale != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("Locale")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(locale, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    urlBuilder_.Length--;

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<GetReservationResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<CancelReservationResult> ReservationDELETEAsync(string providerBookingId, string confirmationId, int? providerCode, string partnerCode, string version)
        {
            return ReservationDELETEAsync(providerBookingId, confirmationId, providerCode, partnerCode, version, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<CancelReservationResult> ReservationDELETEAsync(string providerBookingId, string confirmationId, int? providerCode, string partnerCode, string version, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Method = new System.Net.Http.HttpMethod("DELETE");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/Reservation"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Reservation");
                    urlBuilder_.Append('?');
                    if (providerBookingId != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ProviderBookingId")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(providerBookingId, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (confirmationId != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ConfirmationId")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(confirmationId, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (providerCode != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ProviderCode")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(providerCode, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (partnerCode != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("PartnerCode")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(partnerCode, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    urlBuilder_.Length--;

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<CancelReservationResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        protected struct ObjectResponseResult<T>
        {
            public ObjectResponseResult(T responseObject, string responseText)
            {
                this.Object = responseObject;
                this.Text = responseText;
            }

            public T Object { get; }

            public string Text { get; }
        }

        public bool ReadResponseAsString { get; set; }

        protected virtual async System.Threading.Tasks.Task<ObjectResponseResult<T>> ReadObjectResponseAsync<T>(System.Net.Http.HttpResponseMessage response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Threading.CancellationToken cancellationToken)
        {
            if (response == null || response.Content == null)
            {
                return new ObjectResponseResult<T>(default(T), string.Empty);
            }

            if (ReadResponseAsString)
            {
                var responseText = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                try
                {
                    var typedBody = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(responseText, JsonSerializerSettings);
                    return new ObjectResponseResult<T>(typedBody, responseText);
                }
                catch (Newtonsoft.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body string as " + typeof(T).FullName + ".";
                    throw new ApiException(message, (int)response.StatusCode, responseText, headers, exception);
                }
            }
            else
            {
                try
                {
                    using (var responseStream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false))
                    using (var streamReader = new System.IO.StreamReader(responseStream))
                    using (var jsonTextReader = new Newtonsoft.Json.JsonTextReader(streamReader))
                    {
                        var serializer = Newtonsoft.Json.JsonSerializer.Create(JsonSerializerSettings);
                        var typedBody = serializer.Deserialize<T>(jsonTextReader);
                        return new ObjectResponseResult<T>(typedBody, string.Empty);
                    }
                }
                catch (Newtonsoft.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body stream as " + typeof(T).FullName + ".";
                    throw new ApiException(message, (int)response.StatusCode, string.Empty, headers, exception);
                }
            }
        }

        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value == null)
            {
                return "";
            }

            if (value is System.Enum)
            {
                var name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value != null ? attribute.Value : name;
                        }
                    }

                    var converted = System.Convert.ToString(System.Convert.ChangeType(value, System.Enum.GetUnderlyingType(value.GetType()), cultureInfo));
                    return converted == null ? string.Empty : converted;
                }
            }
            else if (value is bool) 
            {
                return System.Convert.ToString((bool)value, cultureInfo).ToLowerInvariant();
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value is string[])
            {
                return string.Join(",", (string[])value);
            }
            else if (value.GetType().IsArray)
            {
                var valueArray = (System.Array)value;
                var valueTextArray = new string[valueArray.Length];
                for (var i = 0; i < valueArray.Length; i++)
                {
                    valueTextArray[i] = ConvertToString(valueArray.GetValue(i), cultureInfo);
                }
                return string.Join(",", valueTextArray);
            }

            var result = System.Convert.ToString(value, cultureInfo);
            return result == null ? "" : result;
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelRepriceParameters
    {
        [Newtonsoft.Json.JsonProperty("hotelMetaCode", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int HotelMetaCode { get; set; }

        [Newtonsoft.Json.JsonProperty("partnerCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PartnerCode { get; set; }

        [Newtonsoft.Json.JsonProperty("stayInformation", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public StayInformation StayInformation { get; set; }

        [Newtonsoft.Json.JsonProperty("occupancy", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<RoomConfiguration> Occupancy { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelRepriceResult
    {
        [Newtonsoft.Json.JsonProperty("metaCode", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int MetaCode { get; set; }

        [Newtonsoft.Json.JsonProperty("variants", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<Variant> Variants { get; set; }

        [Newtonsoft.Json.JsonProperty("groupedVariants", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<System.Collections.Generic.IDictionary<string, System.Collections.Generic.ICollection<Variant>>> GroupedVariants { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Variant
    {
        [Newtonsoft.Json.JsonProperty("offerId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string OfferId { get; set; }

        [Newtonsoft.Json.JsonProperty("mealPlan", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public MealPlan MealPlan { get; set; }

        [Newtonsoft.Json.JsonProperty("pricing", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public VariantPricing Pricing { get; set; }

        [Newtonsoft.Json.JsonProperty("refundability", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Refundability Refundability { get; set; }

        [Newtonsoft.Json.JsonProperty("paymentType", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public PaymentType PaymentType { get; set; }

        [Newtonsoft.Json.JsonProperty("receivedAt", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime ReceivedAt { get; set; }

        [Newtonsoft.Json.JsonProperty("availability", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Availability { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class VariantPricing
    {
        [Newtonsoft.Json.JsonProperty("providerNetPrice", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Price ProviderNetPrice { get; set; }

        [Newtonsoft.Json.JsonProperty("summaryToPayAtHotel", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Price SummaryToPayAtHotel { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class OffersRepriceParameters
    {
        [Newtonsoft.Json.JsonProperty("offerIds", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<string> OfferIds { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class OffersRepriceResult
    {
        [Newtonsoft.Json.JsonProperty("results", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.IDictionary<string, OfferRepriceResult> Results { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class OfferRepriceResult
    {
        [Newtonsoft.Json.JsonProperty("offerId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string OfferId { get; set; }

        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public OfferRepriceStatus Status { get; set; }

        [Newtonsoft.Json.JsonProperty("price", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Price Price { get; set; }

        [Newtonsoft.Json.JsonProperty("availability", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Availability { get; set; }

        [Newtonsoft.Json.JsonProperty("receivedAt", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? ReceivedAt { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public enum OfferRepriceStatus
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Unknown")]
        Unknown = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Available")]
        Available = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"NotAvailable")]
        NotAvailable = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"ArgumentError")]
        ArgumentError = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"HotelNotFoundError")]
        HotelNotFoundError = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"ProviderNotFoundError")]
        ProviderNotFoundError = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"OfferNotAvailable")]
        OfferNotAvailable = 6,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Booker
    {
        [Newtonsoft.Json.JsonProperty("phone", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Phone Phone { get; set; }

        [Newtonsoft.Json.JsonProperty("email", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Email { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class BookingRoom
    {
        [Newtonsoft.Json.JsonProperty("roomId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RoomId { get; set; }

        [Newtonsoft.Json.JsonProperty("specialRequests", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SpecialRequests { get; set; }

        [Newtonsoft.Json.JsonProperty("smoking", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool Smoking { get; set; }

        [Newtonsoft.Json.JsonProperty("bedType", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string BedType { get; set; }

        [Newtonsoft.Json.JsonProperty("guests", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<Guest> Guests { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class CreditCard
    {
        [Newtonsoft.Json.JsonProperty("cardId", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Guid CardId { get; set; }

        [Newtonsoft.Json.JsonProperty("cardType", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CardType { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Guest
    {
        [Newtonsoft.Json.JsonProperty("firstName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string FirstName { get; set; }

        [Newtonsoft.Json.JsonProperty("lastName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string LastName { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Phone
    {
        [Newtonsoft.Json.JsonProperty("countryCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CountryCode { get; set; }

        [Newtonsoft.Json.JsonProperty("areaCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AreaCode { get; set; }

        [Newtonsoft.Json.JsonProperty("number", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Number { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelBookParameters
    {
        [Newtonsoft.Json.JsonProperty("token", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Token { get; set; }

        [Newtonsoft.Json.JsonProperty("booker", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Booker Booker { get; set; }

        [Newtonsoft.Json.JsonProperty("creditCard", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public CreditCard CreditCard { get; set; }

        [Newtonsoft.Json.JsonProperty("bookingRooms", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<BookingRoom> BookingRooms { get; set; }

        [Newtonsoft.Json.JsonProperty("userInformation", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public UserInformation UserInformation { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelPriceCheckParameters
    {
        [Newtonsoft.Json.JsonProperty("metaCode", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int MetaCode { get; set; }

        [Newtonsoft.Json.JsonProperty("providerCode", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int ProviderCode { get; set; }

        /// <summary>
        /// Provider-specific package identifier (raw format, no encoding)
        /// </summary>
        [Newtonsoft.Json.JsonProperty("packageId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PackageId { get; set; }

        [Newtonsoft.Json.JsonProperty("bedConfigurationCodes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<string> BedConfigurationCodes { get; set; }

        [Newtonsoft.Json.JsonProperty("userInformation", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public UserInformation UserInformation { get; set; }

        [Newtonsoft.Json.JsonProperty("searchContext", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public SearchContext SearchContext { get; set; }

        [Newtonsoft.Json.JsonProperty("currency", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Currency { get; set; }

        [Newtonsoft.Json.JsonProperty("language", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Language { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelPriceParameters
    {
        [Newtonsoft.Json.JsonProperty("metaCode", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int MetaCode { get; set; }

        [Newtonsoft.Json.JsonProperty("providerCode", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int ProviderCode { get; set; }

        [Newtonsoft.Json.JsonProperty("packageId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PackageId { get; set; }

        [Newtonsoft.Json.JsonProperty("userInformation", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public UserInformation UserInformation { get; set; }

        [Newtonsoft.Json.JsonProperty("searchContext", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public SearchContext SearchContext { get; set; }

        [Newtonsoft.Json.JsonProperty("currency", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Currency { get; set; }

        [Newtonsoft.Json.JsonProperty("language", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Language { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class BedConfiguration
    {
        [Newtonsoft.Json.JsonProperty("code", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Code { get; set; }

        [Newtonsoft.Json.JsonProperty("bedTypes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<BedType> BedTypes { get; set; }

        [Newtonsoft.Json.JsonProperty("description", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Description { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class BedType
    {
        [Newtonsoft.Json.JsonProperty("name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Name { get; set; }

        [Newtonsoft.Json.JsonProperty("description", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Description { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class CancelError
    {
        [Newtonsoft.Json.JsonProperty("errorCode", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int ErrorCode { get; set; }

        [Newtonsoft.Json.JsonProperty("errorMesage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ErrorMesage { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class CancelReservationResult
    {
        [Newtonsoft.Json.JsonProperty("reservationCancelled", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool ReservationCancelled { get; set; }

        [Newtonsoft.Json.JsonProperty("cancelError", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public CancelError CancelError { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class CancellationInfo
    {
        [Newtonsoft.Json.JsonProperty("cancellationDetails", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<CancellationInfoDetails> CancellationDetails { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class CancellationInfoDetails
    {
        /// <summary>
        /// Last day for cancellation for a specified price
        /// </summary>
        [Newtonsoft.Json.JsonProperty("until", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateOnly Until { get; set; }

        [Newtonsoft.Json.JsonProperty("price", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Price Price { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Context
    {
        [Newtonsoft.Json.JsonProperty("partnerCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PartnerCode { get; set; }

        [Newtonsoft.Json.JsonProperty("contextsApplied", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore, ItemConverterType = typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public System.Collections.Generic.ICollection<Context2> ContextsApplied { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Coordinates
    {
        [Newtonsoft.Json.JsonProperty("longitude", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Longitude { get; set; }

        [Newtonsoft.Json.JsonProperty("latitude", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double Latitude { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetReservationResult
    {
        [Newtonsoft.Json.JsonProperty("metaCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? MetaCode { get; set; }

        [Newtonsoft.Json.JsonProperty("stayInformation", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public StayInformation StayInformation { get; set; }

        [Newtonsoft.Json.JsonProperty("reservationStatus", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ReservationStatus ReservationStatus { get; set; }

        [Newtonsoft.Json.JsonProperty("reservationDate", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime ReservationDate { get; set; }

        [Newtonsoft.Json.JsonProperty("hotel", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ProviderHotel Hotel { get; set; }

        [Newtonsoft.Json.JsonProperty("rooms", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<ReservationRoom> Rooms { get; set; }

        [Newtonsoft.Json.JsonProperty("cancellationInfo", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public CancellationInfo CancellationInfo { get; set; }

        [Newtonsoft.Json.JsonProperty("internalPriceDetails", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public InternalPriceDetailsWithBreakdown InternalPriceDetails { get; set; }

        [Newtonsoft.Json.JsonProperty("priceDetails", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PriceDetailsWithBreakdown PriceDetails { get; set; }

        [Newtonsoft.Json.JsonProperty("buyoutType", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public BuyoutType BuyoutType { get; set; }

        [Newtonsoft.Json.JsonProperty("information", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<Information> Information { get; set; }

        [Newtonsoft.Json.JsonProperty("paymentType", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public PaymentType PaymentType { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Guest2
    {
        [Newtonsoft.Json.JsonProperty("firstName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string FirstName { get; set; }

        [Newtonsoft.Json.JsonProperty("lastName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string LastName { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class ReservationRoom
    {
        [Newtonsoft.Json.JsonProperty("roomName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RoomName { get; set; }

        [Newtonsoft.Json.JsonProperty("roomId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RoomId { get; set; }

        [Newtonsoft.Json.JsonProperty("description", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Description { get; set; }

        [Newtonsoft.Json.JsonProperty("bedType", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string BedType { get; set; }

        [Newtonsoft.Json.JsonProperty("specialRequests", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SpecialRequests { get; set; }

        [Newtonsoft.Json.JsonProperty("smoking", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool Smoking { get; set; }

        [Newtonsoft.Json.JsonProperty("facilities", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<string> Facilities { get; set; }

        [Newtonsoft.Json.JsonProperty("roomConfiguration", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public RoomConfiguration RoomConfiguration { get; set; }

        [Newtonsoft.Json.JsonProperty("mealPlan", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public MealPlan MealPlan { get; set; }

        [Newtonsoft.Json.JsonProperty("guests", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<Guest2> Guests { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public enum ReservationStatus
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Unknown")]
        Unknown = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Created")]
        Created = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Cancelled")]
        Cancelled = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"NoShow")]
        NoShow = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"Stayed")]
        Stayed = 4,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelAddress
    {
        [Newtonsoft.Json.JsonProperty("street", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Street { get; set; }

        [Newtonsoft.Json.JsonProperty("city", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string City { get; set; }

        [Newtonsoft.Json.JsonProperty("countryCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CountryCode { get; set; }

        [Newtonsoft.Json.JsonProperty("coordinates", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Coordinates Coordinates { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelBookResult
    {
        [Newtonsoft.Json.JsonProperty("providerBookingId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ProviderBookingId { get; set; }

        [Newtonsoft.Json.JsonProperty("confirmationId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ConfirmationId { get; set; }

        [Newtonsoft.Json.JsonProperty("providerCode", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int ProviderCode { get; set; }

        [Newtonsoft.Json.JsonProperty("context", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Context Context { get; set; }

        [Newtonsoft.Json.JsonProperty("isTest", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool IsTest { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public enum BuyoutType
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Esky")]
        Esky = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Client")]
        Client = 1,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelPriceResult
    {
        [Newtonsoft.Json.JsonProperty("offer", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Offer Offer { get; set; }

        [Newtonsoft.Json.JsonProperty("context", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Context Context { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Offer
    {
        /// <summary>
        /// Provider-specific package identifier (raw format, no encoding)
        /// </summary>
        [Newtonsoft.Json.JsonProperty("packageId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PackageId { get; set; }

        [Newtonsoft.Json.JsonProperty("metaCode", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int MetaCode { get; set; }

        [Newtonsoft.Json.JsonProperty("qualifiedHotelCode", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int QualifiedHotelCode { get; set; }

        [Newtonsoft.Json.JsonProperty("internalPriceDetails", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public InternalPriceDetailsWithBreakdown InternalPriceDetails { get; set; }

        [Newtonsoft.Json.JsonProperty("priceDetails", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PriceDetailsWithBreakdown PriceDetails { get; set; }

        [Newtonsoft.Json.JsonProperty("rooms", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<Room> Rooms { get; set; }

        [Newtonsoft.Json.JsonProperty("providerCode", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int ProviderCode { get; set; }

        [Newtonsoft.Json.JsonProperty("mealPlan", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public MealPlan MealPlan { get; set; }

        [Newtonsoft.Json.JsonProperty("buyoutType", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public BuyoutType BuyoutType { get; set; }

        [Newtonsoft.Json.JsonProperty("clientCreditCardRequired", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool ClientCreditCardRequired { get; set; }

        [Newtonsoft.Json.JsonProperty("depositRequired", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool DepositRequired { get; set; }

        [Newtonsoft.Json.JsonProperty("cancellationInfo", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public CancellationInfo CancellationInfo { get; set; }

        [Newtonsoft.Json.JsonProperty("policies", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<Information> Policies { get; set; }

        [Newtonsoft.Json.JsonProperty("importantInformation", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<Information> ImportantInformation { get; set; }

        [Newtonsoft.Json.JsonProperty("providerHotel", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ProviderHotel ProviderHotel { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Room
    {
        [Newtonsoft.Json.JsonProperty("code", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Code { get; set; }

        [Newtonsoft.Json.JsonProperty("name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Name { get; set; }

        [Newtonsoft.Json.JsonProperty("description", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Description { get; set; }

        [Newtonsoft.Json.JsonProperty("bedConfigurations", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<BedConfiguration> BedConfigurations { get; set; }

        [Newtonsoft.Json.JsonProperty("roomConfiguration", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public RoomConfiguration RoomConfiguration { get; set; }

        [Newtonsoft.Json.JsonProperty("photos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<Photos> Photos { get; set; }

        [Newtonsoft.Json.JsonProperty("facilities", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<string> Facilities { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Information
    {
        [Newtonsoft.Json.JsonProperty("code", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Code { get; set; }

        [Newtonsoft.Json.JsonProperty("text", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Text { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class InternalPriceDetailsWithBreakdown
    {
        [Newtonsoft.Json.JsonProperty("providerIncome", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Price ProviderIncome { get; set; }

        [Newtonsoft.Json.JsonProperty("providerPriceDetails", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ProviderPriceDetails ProviderPriceDetails { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public enum PaymentType
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Unknown")]
        Unknown = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"OnSite")]
        OnSite = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"AtCheckout")]
        AtCheckout = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Installments")]
        Installments = 3,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceBreakdown
    {
        [Newtonsoft.Json.JsonProperty("type", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Type { get; set; }

        [Newtonsoft.Json.JsonProperty("name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Name { get; set; }

        [Newtonsoft.Json.JsonProperty("includedInNet", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool IncludedInNet { get; set; }

        [Newtonsoft.Json.JsonProperty("price", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Price Price { get; set; }

        [Newtonsoft.Json.JsonProperty("code", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Code { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelPriceCheckResult
    {
        [Newtonsoft.Json.JsonProperty("token", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Token { get; set; }

        [Newtonsoft.Json.JsonProperty("internalPriceDetails", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public InternalPriceDetailsWithBreakdown InternalPriceDetails { get; set; }

        [Newtonsoft.Json.JsonProperty("priceDetails", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PriceDetailsWithBreakdown PriceDetails { get; set; }

        [Newtonsoft.Json.JsonProperty("context", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Context Context { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceDetails
    {
        [Newtonsoft.Json.JsonProperty("currency", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Currency { get; set; }

        [Newtonsoft.Json.JsonProperty("netPrice", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal NetPrice { get; set; }

        [Newtonsoft.Json.JsonProperty("taxPrice", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal TaxPrice { get; set; }

        [Newtonsoft.Json.JsonProperty("extraCharges", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal ExtraCharges { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceDetailsWithBreakdown
    {
        [Newtonsoft.Json.JsonProperty("priceDetails", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PriceDetails PriceDetails { get; set; }

        [Newtonsoft.Json.JsonProperty("summaryToPayNow", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Price SummaryToPayNow { get; set; }

        [Newtonsoft.Json.JsonProperty("summaryToPayAtHotel", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Price SummaryToPayAtHotel { get; set; }

        [Newtonsoft.Json.JsonProperty("summaryToPayAtHotelInClientCurrency", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Price SummaryToPayAtHotelInClientCurrency { get; set; }

        [Newtonsoft.Json.JsonProperty("taxesBreakdown", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<PriceBreakdown> TaxesBreakdown { get; set; }

        [Newtonsoft.Json.JsonProperty("extraChargesBreakdown", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<PriceBreakdown> ExtraChargesBreakdown { get; set; }

        [Newtonsoft.Json.JsonProperty("chargesBreakdown", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<PriceBreakdown> ChargesBreakdown { get; set; }

        [Newtonsoft.Json.JsonProperty("salesMargin", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Price SalesMargin { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProviderHotel
    {
        [Newtonsoft.Json.JsonProperty("originalHotelCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string OriginalHotelCode { get; set; }

        [Newtonsoft.Json.JsonProperty("providerCode", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int ProviderCode { get; set; }

        [Newtonsoft.Json.JsonProperty("hotelName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string HotelName { get; set; }

        [Newtonsoft.Json.JsonProperty("phone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Phone { get; set; }

        [Newtonsoft.Json.JsonProperty("address", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public HotelAddress Address { get; set; }

        [Newtonsoft.Json.JsonProperty("countryCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CountryCode { get; set; }

        [Newtonsoft.Json.JsonProperty("webPage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string WebPage { get; set; }

        [Newtonsoft.Json.JsonProperty("stars", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Stars { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProviderPriceDetails
    {
        [Newtonsoft.Json.JsonProperty("currency", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Currency { get; set; }

        [Newtonsoft.Json.JsonProperty("netPrice", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal NetPrice { get; set; }

        [Newtonsoft.Json.JsonProperty("taxPrice", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal TaxPrice { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Refundability
    {
        [Newtonsoft.Json.JsonProperty("isRefundable", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool IsRefundable { get; set; }

        [Newtonsoft.Json.JsonProperty("freeRefundUntil", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateOnly? FreeRefundUntil { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class RoomConfiguration
    {
        [Newtonsoft.Json.JsonProperty("adults", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Adults { get; set; }

        [Newtonsoft.Json.JsonProperty("childrenAges", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<int> ChildrenAges { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchContext
    {
        [Newtonsoft.Json.JsonProperty("stayInformation", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public StayInformation StayInformation { get; set; }

        [Newtonsoft.Json.JsonProperty("settingsKey", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public SettingsKey SettingsKey { get; set; }

        [Newtonsoft.Json.JsonProperty("roomsConfiguration", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.ICollection<RoomConfiguration> RoomsConfiguration { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class SettingsKey
    {
        [Newtonsoft.Json.JsonProperty("partnerCode", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PartnerCode { get; set; }

        [Newtonsoft.Json.JsonProperty("flightBookingId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string FlightBookingId { get; set; }

        [Newtonsoft.Json.JsonProperty("mobileDevice", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? MobileDevice { get; set; }

        [Newtonsoft.Json.JsonProperty("userId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string UserId { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class UserInformation
    {
        [Newtonsoft.Json.JsonProperty("userAgent", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string UserAgent { get; set; }

        [Newtonsoft.Json.JsonProperty("userIp", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string UserIp { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public enum Context2
    {

        [System.Runtime.Serialization.EnumMember(Value = @"CROSSSELLING")]
        CROSSSELLING = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"MOBILE")]
        MOBILE = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"MEMBER")]
        MEMBER = 2,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public enum MealPlan
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Unknown")]
        Unknown = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"None")]
        None = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Breakfast")]
        Breakfast = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"HalfBoard")]
        HalfBoard = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"FullBoard")]
        FullBoard = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"AllInclusive")]
        AllInclusive = 5,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Photo
    {
        [Newtonsoft.Json.JsonProperty("src", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Src { get; set; }

        [Newtonsoft.Json.JsonProperty("width", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Width { get; set; }

        [Newtonsoft.Json.JsonProperty("height", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Height { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Price
    {
        [Newtonsoft.Json.JsonProperty("amount", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal Amount { get; set; }

        [Newtonsoft.Json.JsonProperty("currency", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Currency { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class StayInformation
    {
        [Newtonsoft.Json.JsonProperty("checkInDate", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime CheckInDate { get; set; }

        [Newtonsoft.Json.JsonProperty("checkOutDate", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime CheckOutDate { get; set; }

        [Newtonsoft.Json.JsonProperty("nights", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int Nights { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProblemDetails
    {
        [Newtonsoft.Json.JsonProperty("type", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Type { get; set; }

        [Newtonsoft.Json.JsonProperty("title", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Title { get; set; }

        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Status { get; set; }

        [Newtonsoft.Json.JsonProperty("detail", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Detail { get; set; }

        [Newtonsoft.Json.JsonProperty("instance", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Instance { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [Newtonsoft.Json.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class Photos
    {
        [Newtonsoft.Json.JsonProperty("Thumbnail", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Photo Thumbnail { get; set; }

        [Newtonsoft.Json.JsonProperty("Medium", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Photo Medium { get; set; }

        [Newtonsoft.Json.JsonProperty("Large", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Photo Large { get; set; }

    }



    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class ApiException : System.Exception
    {
        public int StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public ApiException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception innerException)
            : base(message + "\n\nStatus: " + statusCode + "\nResponse: \n" + ((response == null) ? "(null)" : response.Substring(0, response.Length >= 512 ? 512 : response.Length)), innerException)
        {
            StatusCode = statusCode;
            Response = response;
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v1******* (Newtonsoft.Json v13.0.0.0))")]
    public partial class ApiException<TResult> : ApiException
    {
        public TResult Result { get; private set; }

        public ApiException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception innerException)
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}

#pragma warning restore  108
#pragma warning restore  114
#pragma warning restore  472
#pragma warning restore  612
#pragma warning restore 1573
#pragma warning restore 1591
#pragma warning restore 8073
#pragma warning restore 3016
#pragma warning restore 8603
#pragma warning restore 8604
#pragma warning restore 8625