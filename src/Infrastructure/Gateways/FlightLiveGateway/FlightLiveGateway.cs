using Esky.Packages.Application.Abstractions.Gateways.FlightLiveGateway;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Infrastructure.Gateways.FlightCacheGateway.Proxies.FlightLiveSearch;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Infrastructure.Gateways.FlightLiveGateway;

public class FlightLiveGateway : IFlightLiveGateway
{
    private readonly FlightLiveSearchClient _flightLiveSearchClient;
    private readonly ILogger<FlightLiveGateway> _logger;

    public FlightLiveGateway(IHttpClientFactory httpClientFactory, ILogger<FlightLiveGateway> logger)
    {
        var httpClient = httpClientFactory.CreateClient(ApiHttpClientConsts.FlightLiveClient);

        _flightLiveSearchClient = new FlightLiveSearchClient(httpClient);
        _logger = logger;
    }

    public async Task<FlightLiveCheckDto?> LiveCheck(
        LiveCheckCriteria criteria, 
        CancellationToken cancellationToken = default)
    {
        OfferLiveCheckResponse? response;

        try
        {
            response = await _flightLiveSearchClient.ByFlightKeysAsync(new OfferLiveCheckRequest
            {
                FlightOfferKeys = [criteria.FlightOfferKey],
                PartnerCode = criteria.PartnerCode,
                UsePartnerCurrency = false,
                Passengers = MapOccupancy(criteria.Occupancy).ToArray()
            }, cancellationToken);
        }
        catch (TaskCanceledException e)
        {
            _logger.LogWarning(e, "Task canceled for flight live check");
            return new FlightLiveCheckDto(
                Price: null,
                Status: FlightLiveCheckStatusDto.Timeout,
                LegLocators: []);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error while calling flight live check");
            return null;
        }

        var liveCheckResult = response.Offers?.FirstOrDefault();
        if (liveCheckResult is null)
        {
            return null;
        }

        return new FlightLiveCheckDto(
            Price: liveCheckResult.ProviderOfferDetails is not null
                ? new FlightLiveCheckMoneyDto(
                    Value: liveCheckResult.ProviderOfferDetails.Price,
                    Currency: liveCheckResult.Currency)
                : null,
            Status: MapStatus(liveCheckResult),
            LegLocators: liveCheckResult.ProviderOfferDetails?.LegLocators.ToArray() ?? []);
    }
    
    private static IEnumerable<SearchFlightsRequestPassenger> MapOccupancy(Occupancy occupancy)
    {
        var passengers = FlightPassengers.FromOccupancy(occupancy);
        
        if (passengers.Adults > 0)
        {
            yield return new SearchFlightsRequestPassenger
            {
                Code = PersonTypeEnum._1,
                Count = passengers.Adults,
            };
        }

        if (passengers.Youths > 0)
        {
            yield return new SearchFlightsRequestPassenger
            {
                Code = PersonTypeEnum._3,
                Count = passengers.Youths,
                Ages = passengers.YouthsAges,
            };
        }

        if (passengers.Children > 0)
        {
            yield return new SearchFlightsRequestPassenger
            {
                Code = PersonTypeEnum._2,
                Count = passengers.Children,
                Ages = passengers.ChildrenAges,
            };
        }

        if (passengers.Infants > 0)
        {
            yield return new SearchFlightsRequestPassenger
            {
                Code = PersonTypeEnum._4,
                Count = passengers.Infants,
                Ages = passengers.InfantsAges,
            };
        }
    }

    private static FlightLiveCheckStatusDto MapStatus(FlightOffer flight)
    {
        return flight.Status switch
        {
            "Unconfirmed" => FlightLiveCheckStatusDto.Unconfirmed,
            "Available" => FlightLiveCheckStatusDto.Available,
            "NotAvailable" => FlightLiveCheckStatusDto.NotAvailable,
            _ => throw new ArgumentOutOfRangeException(nameof(flight.Status), flight.Status, null)
        };
    }
}