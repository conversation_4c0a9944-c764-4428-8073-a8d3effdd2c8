using Esky.Packages.Application.Abstractions.Gateways.FlightGateway;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Gateways.OfferAccuracyMonitorGateway.Client;
using Esky.Packages.Infrastructure.HttpClients.Constants;

namespace Esky.Packages.Infrastructure.Gateways.OfferAccuracyMonitorGateway;

public class OfferAccuracyGateway : IOfferAccuracyGateway
{
    private readonly OfferAccuracyClient _offerAccuracyClient;

    public OfferAccuracyGateway(IHttpClientFactory httpClientFactory)
    {
        var httpClient = httpClientFactory.CreateClient(ApiHttpClientConsts.OfferAccuracyClient);

        _offerAccuracyClient = new OfferAccuracyClient(httpClient);
    }
    
    public async Task<HashSet<string>> GetUnavailableFlightOffers(string[] flightIds, Occupancy occupancy, 
        CancellationToken cancellationToken = default)
    {
        var query = new GetUnavailableFlightOffersQuery
        {
            Context = new Context
            {
                PartnerCode = string.Empty,
                SessionId = string.Empty
            },
            FlightIds = flightIds,
            PaxConfiguration = FlightPassengers.FromOccupancy(occupancy).ToString()
        };

        try
        {
            //todo mb: add to span count and all flightIds
            var response = await _offerAccuracyClient.UnavailabilityAsync(query, cancellationToken);
            //todo mb: add to span count and all unavailableOffers
            
            return response.UnavailableOffers.ToHashSet();
        }
        catch (TaskCanceledException e)
        {
            //todo mb: add to span, log error, increase counter of timeouts
            Console.WriteLine(e);
            return [];
        }
        catch (Exception e)
        {
            //todo mb: add to span, log error, increase counter of errors
            Console.WriteLine(e);
            return [];
        }
    }
    
    public async Task<IEnumerable<string>> GetUnavailableHotelRoomOffers(int provider, int hotelMetaCode, 
        DateOnly checkinDate, DateOnly checkoutDate, CancellationToken cancellationToken = default)
    {
        var query = new GetUnavailableHotelRoomOffersQuery
        {
            Context = new Context
            {
                PartnerCode = string.Empty,
                SessionId = string.Empty
            },
            Provider = provider,
            HotelMetaCode = hotelMetaCode,
            CheckinDate = checkinDate,
            CheckoutDate = checkoutDate
        };

        var response = await _offerAccuracyClient.Unavailability2Async(query, cancellationToken);

        return response.UnavailableOffers.Select(o => o.PackageId);
    }
}