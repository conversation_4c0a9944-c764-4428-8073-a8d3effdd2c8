using System.Net;
using Esky.Packages.Application.Abstractions.Gateways.FlightGateway;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Esky.Packages.Infrastructure.HttpClients.Extensions;
using Esky.Packages.Infrastructure.HttpClients.Options;
using Esky.Packages.Infrastructure.Observability;
using Microsoft.Extensions.DependencyInjection;
using Polly;

namespace Esky.Packages.Infrastructure.Gateways.FlightCacheGateway;

internal static class DependencyInjection
{
    public static IServiceCollection AddFlightCacheGateway(this IServiceCollection services, bool enableLoggingHandlers = false)
    {
        var builder = services.AddHttpClient(ApiHttpClientConsts.FlightCacheClient,
            (sp, client) =>
            {
                client.BaseAddress = new Uri(sp.GetRequiredService<ApiUrlsOptions>().FlightCache);
                client.Timeout = TimeSpan.FromSeconds(30);
            })
            .AddCompression();

        if (enableLoggingHandlers)
        {
            builder.WithLoggingHandler();
        }

        builder.AddResilienceHandler("flightCache", (b, _) =>
            b.AddRetry(new()
            {
                ShouldHandle = static args => ValueTask.FromResult(args.Outcome.Result?.StatusCode is
                        HttpStatusCode.BadGateway
                        or HttpStatusCode.ServiceUnavailable
                        or HttpStatusCode.GatewayTimeout
                        or HttpStatusCode.InternalServerError // we very often get 500 for no reason
                ),
                BackoffType = DelayBackoffType.Exponential,
                UseJitter = true,
                MaxRetryAttempts = 5,
                MaxDelay = TimeSpan.FromSeconds(30),
                Delay = TimeSpan.FromMilliseconds(1000),
            })
        );

        services.AddSingleton<IFlightGateway, FlightCacheGateway>();

        return services;
    }
}