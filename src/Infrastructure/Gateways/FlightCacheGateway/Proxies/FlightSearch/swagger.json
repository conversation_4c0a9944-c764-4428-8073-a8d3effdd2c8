{"openapi": "3.0.1", "info": {"title": "Flight Search Api", "version": "v1"}, "paths": {"/api/v1.0/Calendar/rt/route/{departureCode}-{arrivalCode}/departure/{departureDate}/cheapestDate": {"get": {"tags": ["Calendar"], "parameters": [{"name": "departureDate", "in": "path", "required": true, "schema": {"type": "string", "format": "date-time"}}, {"name": "Partner-Code", "in": "header", "schema": {"type": "string", "nullable": true}}, {"name": "departureCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "arrivalCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1.0/Calendar/rt/airlineCode/{airlineCode}/currency/{currency}/minPricesForAirline": {"get": {"tags": ["Calendar"], "parameters": [{"name": "airlineCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "currency", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "Partner-Code", "in": "header", "schema": {"type": "string", "nullable": true}}, {"name": "Environment", "in": "header", "schema": {"type": "string", "nullable": true}}, {"name": "ConfigProfile", "in": "query", "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1.0/FlightCache/SpecialOfferFlights": {"post": {"tags": ["FlightCache"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.SpecialOfferFlightsQuery"}, "example": {"partnerCode": "FLY4FREEDEALS", "currencyCode": "EUR", "departureDate": "2025-06-05T00:00:00Z", "returnDepartureDate": "2025-06-08T00:00:00Z", "departureCode": "KTW", "arrivalCode": "LON", "passengers": [{"code": "Adult", "count": 2}]}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.SpecialOfferFlightsQuery"}, "example": {"partnerCode": "FLY4FREEDEALS", "currencyCode": "EUR", "departureDate": "2025-06-05T00:00:00Z", "returnDepartureDate": "2025-06-08T00:00:00Z", "departureCode": "KTW", "arrivalCode": "LON", "passengers": [{"code": "Adult", "count": 2}]}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.SpecialOfferFlightsQuery"}, "example": {"partnerCode": "FLY4FREEDEALS", "currencyCode": "EUR", "departureDate": "2025-06-05T00:00:00Z", "returnDepartureDate": "2025-06-08T00:00:00Z", "departureCode": "KTW", "arrivalCode": "LON", "passengers": [{"code": "Adult", "count": 2}]}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.SpecialOfferFlightsQuery"}, "example": {"partnerCode": "FLY4FREEDEALS", "currencyCode": "EUR", "departureDate": "2025-06-05T00:00:00Z", "returnDepartureDate": "2025-06-08T00:00:00Z", "departureCode": "KTW", "arrivalCode": "LON", "passengers": [{"code": "Adult", "count": 2}]}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.SpecialOfferFlightsView"}}}}}}}, "/api/v1.0/FlightCache/BestOffersQuery": {"post": {"tags": ["FlightCache"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQuery"}, "examples": {"Simple": {"value": {"partnerCode": "ESKY", "currencyCode": "EUR", "departureDestinationSet": {"countryCodes": ["GB"]}, "arrivalDestinationSet": {"airportCodes": ["FRA"]}, "departureMonth": "2025-05-29T00:00:00Z", "orderBy": "StandardScore", "showDebugInfo": true}}, "Grouped": {"value": {"partnerCode": "ESKY", "currencyCode": "EUR", "departureDestinationSet": {"countryCodes": ["GB"]}, "arrivalDestinationSet": {"airportCodes": ["FRA"]}, "departureMonth": "2025-05-29T00:00:00Z", "groupBy": [{"by": "ARRIVAL.CITY", "limit": 5}, {"by": "DEPARTURE.COUNTRY", "limit": 5}, {"by": "DEPARTURE.CITY", "limit": 5}, {"limit": 10}], "orderBy": "StandardScore", "showDebugInfo": true}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQuery"}, "examples": {"Simple": {"value": {"partnerCode": "ESKY", "currencyCode": "EUR", "departureDestinationSet": {"countryCodes": ["GB"]}, "arrivalDestinationSet": {"airportCodes": ["FRA"]}, "departureMonth": "2025-05-29T00:00:00Z", "orderBy": "StandardScore", "showDebugInfo": true}}, "Grouped": {"value": {"partnerCode": "ESKY", "currencyCode": "EUR", "departureDestinationSet": {"countryCodes": ["GB"]}, "arrivalDestinationSet": {"airportCodes": ["FRA"]}, "departureMonth": "2025-05-29T00:00:00Z", "groupBy": [{"by": "ARRIVAL.CITY", "limit": 5}, {"by": "DEPARTURE.COUNTRY", "limit": 5}, {"by": "DEPARTURE.CITY", "limit": 5}, {"limit": 10}], "orderBy": "StandardScore", "showDebugInfo": true}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQuery"}, "examples": {"Simple": {"value": {"partnerCode": "ESKY", "currencyCode": "EUR", "departureDestinationSet": {"countryCodes": ["GB"]}, "arrivalDestinationSet": {"airportCodes": ["FRA"]}, "departureMonth": "2025-05-29T00:00:00Z", "orderBy": "StandardScore", "showDebugInfo": true}}, "Grouped": {"value": {"partnerCode": "ESKY", "currencyCode": "EUR", "departureDestinationSet": {"countryCodes": ["GB"]}, "arrivalDestinationSet": {"airportCodes": ["FRA"]}, "departureMonth": "2025-05-29T00:00:00Z", "groupBy": [{"by": "ARRIVAL.CITY", "limit": 5}, {"by": "DEPARTURE.COUNTRY", "limit": 5}, {"by": "DEPARTURE.CITY", "limit": 5}, {"limit": 10}], "orderBy": "StandardScore", "showDebugInfo": true}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQuery"}, "examples": {"Simple": {"value": {"partnerCode": "ESKY", "currencyCode": "EUR", "departureDestinationSet": {"countryCodes": ["GB"]}, "arrivalDestinationSet": {"airportCodes": ["FRA"]}, "departureMonth": "2025-05-29T00:00:00Z", "orderBy": "StandardScore", "showDebugInfo": true}}, "Grouped": {"value": {"partnerCode": "ESKY", "currencyCode": "EUR", "departureDestinationSet": {"countryCodes": ["GB"]}, "arrivalDestinationSet": {"airportCodes": ["FRA"]}, "departureMonth": "2025-05-29T00:00:00Z", "groupBy": [{"by": "ARRIVAL.CITY", "limit": 5}, {"by": "DEPARTURE.COUNTRY", "limit": 5}, {"by": "DEPARTURE.CITY", "limit": 5}, {"limit": 10}], "orderBy": "StandardScore", "showDebugInfo": true}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult"}}}}}}}, "/api/v1.0/flights/route/{departureCode}-{arrivalCode}/ow": {"get": {"tags": ["Flights"], "parameters": [{"name": "Partner-Code", "in": "header", "schema": {"type": "string", "nullable": true}}, {"name": "departureCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "arrivalCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1.0/flights/route/{departureCode}-{arrivalCode}/rt/stay/{stayLength}": {"get": {"tags": ["Flights"], "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Partner-Code", "in": "header", "schema": {"type": "string", "nullable": true}}, {"name": "departureCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "arrivalCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1.0/flights/route/{departureCode}-{arrivalCode}/rt/stay/{minStayLength}-{maxStayLength}": {"get": {"tags": ["Flights"], "parameters": [{"name": "min<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "fields", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Flights.CalendarResultFields"}, "nullable": true}}, {"name": "orderBy", "in": "query", "description": "Optional ordering field. If set, selected value has to be present in fields list. Default: order by date", "schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Flights.CalendarResultFields"}}, {"name": "limit", "in": "query", "description": "Limits result set to specified number of items. Value has to be greater than 0. Default: no limit", "schema": {"type": "integer", "description": "Limits result set to specified number of items. Value has to be greater than 0. Default: no limit", "format": "int32", "nullable": true}}, {"name": "Partner-Code", "in": "header", "schema": {"type": "string", "nullable": true}}, {"name": "departureCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "arrivalCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1.0/flights/route/{departureCode}-{arrivalCode}/rt/departure/{departureDate}": {"get": {"tags": ["Flights"], "parameters": [{"name": "departureDate", "in": "path", "required": true, "schema": {"type": "string", "format": "date-time", "nullable": true}}, {"name": "Partner-Code", "in": "header", "schema": {"type": "string", "nullable": true}}, {"name": "departureCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "arrivalCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1.0/flights/route/{departureCode}-{arrivalCode}/ow/departure/{departureDate}/range/{flex}": {"get": {"tags": ["Flights"], "parameters": [{"name": "departureDate", "in": "path", "required": true, "schema": {"type": "string", "format": "date-time"}}, {"name": "flex", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "adt", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "yth", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "chd", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "inf", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "useNearbyAirports", "in": "query", "schema": {"type": "boolean"}}, {"name": "Partner-Code", "in": "header", "schema": {"type": "string", "nullable": true}}, {"name": "departureCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "arrivalCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1.0/flights/route/{departureCode}-{arrivalCode}/rt/departure/{departureDate}/return/{returnDate}/range/{flex}": {"get": {"tags": ["Flights"], "parameters": [{"name": "departureDate", "in": "path", "required": true, "schema": {"type": "string", "format": "date-time"}}, {"name": "returnDate", "in": "path", "required": true, "schema": {"type": "string", "format": "date-time"}}, {"name": "flex", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "adt", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "yth", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "chd", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "inf", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "useNearbyAirports", "in": "query", "schema": {"type": "boolean"}}, {"name": "Partner-Code", "in": "header", "schema": {"type": "string", "nullable": true}}, {"name": "departureCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}, {"name": "arrivalCode", "in": "path", "required": true, "schema": {"type": "string", "nullable": true}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1.0/LandingPages/Deals/Find": {"post": {"tags": ["LandingPages"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.LandingPages.Deals.Request"}, "examples": {"Default": {"value": {"partnerCode": "ESKY", "departureDestinations": {"airportCodes": [], "multiportCodes": [], "cityCodes": [], "countryCodes": ["PL"], "continentCodes": [], "regionCodes": []}, "arrivalDestinations": {"anywhere": true, "airportCodes": [], "multiportCodes": [], "cityCodes": [], "countryCodes": [], "continentCodes": [], "regionCodes": []}, "departureDateFrom": "2025-06-28", "departureDateTo": "2025-07-28", "stayDurationFrom": 3, "stayDurationTo": 7, "limit": 6, "sessionId": "DBG000000000123"}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.LandingPages.Deals.Request"}, "examples": {"Default": {"value": {"partnerCode": "ESKY", "departureDestinations": {"airportCodes": [], "multiportCodes": [], "cityCodes": [], "countryCodes": ["PL"], "continentCodes": [], "regionCodes": []}, "arrivalDestinations": {"anywhere": true, "airportCodes": [], "multiportCodes": [], "cityCodes": [], "countryCodes": [], "continentCodes": [], "regionCodes": []}, "departureDateFrom": "2025-06-28", "departureDateTo": "2025-07-28", "stayDurationFrom": 3, "stayDurationTo": 7, "limit": 6, "sessionId": "DBG000000000123"}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.LandingPages.Deals.Request"}, "examples": {"Default": {"value": {"partnerCode": "ESKY", "departureDestinations": {"airportCodes": [], "multiportCodes": [], "cityCodes": [], "countryCodes": ["PL"], "continentCodes": [], "regionCodes": []}, "arrivalDestinations": {"anywhere": true, "airportCodes": [], "multiportCodes": [], "cityCodes": [], "countryCodes": [], "continentCodes": [], "regionCodes": []}, "departureDateFrom": "2025-06-28", "departureDateTo": "2025-07-28", "stayDurationFrom": 3, "stayDurationTo": 7, "limit": 6, "sessionId": "DBG000000000123"}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.LandingPages.Deals.Request"}, "examples": {"Default": {"value": {"partnerCode": "ESKY", "departureDestinations": {"airportCodes": [], "multiportCodes": [], "cityCodes": [], "countryCodes": ["PL"], "continentCodes": [], "regionCodes": []}, "arrivalDestinations": {"anywhere": true, "airportCodes": [], "multiportCodes": [], "cityCodes": [], "countryCodes": [], "continentCodes": [], "regionCodes": []}, "departureDateFrom": "2025-06-28", "departureDateTo": "2025-07-28", "stayDurationFrom": 3, "stayDurationTo": 7, "limit": 6, "sessionId": "DBG000000000123"}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.LandingPages.Deals.Response"}}}}}}}, "/api/v1.0/Legacy/GetSpecialOfferCalendarFromCache": {"post": {"tags": ["Legacy"], "summary": "Retrieves best prices calendar.", "requestBody": {"description": "Request object", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetSpecialOfferCalendarFromCacheQuery"}, "example": {"isRoundTrip": true, "selectedDepartureDay": "2025-06-05T00:00:00Z", "lengthOfStay": "Range4To7Days", "departureCode": "KTW", "arrivalCode": "LON", "passengers": [{"code": "Adult", "count": 2}], "partnerCode": "FLY4FREEDEALS", "currencyCode": "EUR"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetSpecialOfferCalendarFromCacheQuery"}, "example": {"isRoundTrip": true, "selectedDepartureDay": "2025-06-05T00:00:00Z", "lengthOfStay": "Range4To7Days", "departureCode": "KTW", "arrivalCode": "LON", "passengers": [{"code": "Adult", "count": 2}], "partnerCode": "FLY4FREEDEALS", "currencyCode": "EUR"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetSpecialOfferCalendarFromCacheQuery"}, "example": {"isRoundTrip": true, "selectedDepartureDay": "2025-06-05T00:00:00Z", "lengthOfStay": "Range4To7Days", "departureCode": "KTW", "arrivalCode": "LON", "passengers": [{"code": "Adult", "count": 2}], "partnerCode": "FLY4FREEDEALS", "currencyCode": "EUR"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetSpecialOfferCalendarFromCacheQuery"}, "example": {"isRoundTrip": true, "selectedDepartureDay": "2025-06-05T00:00:00Z", "lengthOfStay": "Range4To7Days", "departureCode": "KTW", "arrivalCode": "LON", "passengers": [{"code": "Adult", "count": 2}], "partnerCode": "FLY4FREEDEALS", "currencyCode": "EUR"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Calendar.InboundSpecialOfferCalendarFromCacheView"}}}}}}}, "/api/v1.0/Legacy/GetSpecialOfferFlightsFromCache": {"post": {"tags": ["Legacy"], "summary": "Retrieves list of flights for specified search conditions", "requestBody": {"description": "Request object", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetSpecialOfferFlightsFromCacheQuery"}, "example": {"departureDate": "2025-06-05T00:00:00Z", "returnDepartureDate": "2025-06-08T00:00:00Z", "languageCode": "PL", "departureCode": "KTW", "arrivalCode": "LON", "passengers": [{"code": "Adult", "count": 2}], "partnerCode": "FLY4FREEDEALS", "currencyCode": "EUR"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetSpecialOfferFlightsFromCacheQuery"}, "example": {"departureDate": "2025-06-05T00:00:00Z", "returnDepartureDate": "2025-06-08T00:00:00Z", "languageCode": "PL", "departureCode": "KTW", "arrivalCode": "LON", "passengers": [{"code": "Adult", "count": 2}], "partnerCode": "FLY4FREEDEALS", "currencyCode": "EUR"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetSpecialOfferFlightsFromCacheQuery"}, "example": {"departureDate": "2025-06-05T00:00:00Z", "returnDepartureDate": "2025-06-08T00:00:00Z", "languageCode": "PL", "departureCode": "KTW", "arrivalCode": "LON", "passengers": [{"code": "Adult", "count": 2}], "partnerCode": "FLY4FREEDEALS", "currencyCode": "EUR"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetSpecialOfferFlightsFromCacheQuery"}, "example": {"departureDate": "2025-06-05T00:00:00Z", "returnDepartureDate": "2025-06-08T00:00:00Z", "languageCode": "PL", "departureCode": "KTW", "arrivalCode": "LON", "passengers": [{"code": "Adult", "count": 2}], "partnerCode": "FLY4FREEDEALS", "currencyCode": "EUR"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SpecialOfferFlightsFromCacheView"}}}}}}}, "/api/v1.0/Legacy/SearchFlightsFromCache": {"post": {"tags": ["Legacy"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.SearchFlightsFromCacheQuery"}, "example": {"priceType": 14, "sessionId": "DBG70cc4f66-b", "legs": [{"departureCode": "WAW", "departureDate": "2025-07-25T00:00:00Z", "arrivalCode": "LON"}, {"departureCode": "LON", "departureDate": "2025-08-01T00:00:00Z", "arrivalCode": "WAW"}], "languageCode": "PL", "flex": {"enabled": true, "range": 3, "limit": 5}, "passengers": [{"code": "Adult", "count": 1}], "partnerCode": "ESKY", "currencyCode": "PLN"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.SearchFlightsFromCacheQuery"}, "example": {"priceType": 14, "sessionId": "DBG70cc4f66-b", "legs": [{"departureCode": "WAW", "departureDate": "2025-07-25T00:00:00Z", "arrivalCode": "LON"}, {"departureCode": "LON", "departureDate": "2025-08-01T00:00:00Z", "arrivalCode": "WAW"}], "languageCode": "PL", "flex": {"enabled": true, "range": 3, "limit": 5}, "passengers": [{"code": "Adult", "count": 1}], "partnerCode": "ESKY", "currencyCode": "PLN"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.SearchFlightsFromCacheQuery"}, "example": {"priceType": 14, "sessionId": "DBG70cc4f66-b", "legs": [{"departureCode": "WAW", "departureDate": "2025-07-25T00:00:00Z", "arrivalCode": "LON"}, {"departureCode": "LON", "departureDate": "2025-08-01T00:00:00Z", "arrivalCode": "WAW"}], "languageCode": "PL", "flex": {"enabled": true, "range": 3, "limit": 5}, "passengers": [{"code": "Adult", "count": 1}], "partnerCode": "ESKY", "currencyCode": "PLN"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.SearchFlightsFromCacheQuery"}, "example": {"priceType": 14, "sessionId": "DBG70cc4f66-b", "legs": [{"departureCode": "WAW", "departureDate": "2025-07-25T00:00:00Z", "arrivalCode": "LON"}, {"departureCode": "LON", "departureDate": "2025-08-01T00:00:00Z", "arrivalCode": "WAW"}], "languageCode": "PL", "flex": {"enabled": true, "range": 3, "limit": 5}, "passengers": [{"code": "Adult", "count": 1}], "partnerCode": "ESKY", "currencyCode": "PLN"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1.0/Legacy/GetAggregatedDestinationPrices": {"post": {"tags": ["Legacy"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.AggregatedDestinationPrices.GetAggregatedDestinationPricesQuery"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.AggregatedDestinationPrices.GetAggregatedDestinationPricesQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.AggregatedDestinationPrices.GetAggregatedDestinationPricesQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.AggregatedDestinationPrices.GetAggregatedDestinationPricesQuery"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1.0/Legacy/GetPriceAlertCalendar": {"post": {"tags": ["Legacy"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetPriceAlertCalendarQuery"}, "examples": {"Simple": {"value": {"isRoundTrip": true, "departureCode": "WAW", "arrivalCode": "LON", "partnerCode": "ESKY", "currencyCode": "PLN"}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetPriceAlertCalendarQuery"}, "examples": {"Simple": {"value": {"isRoundTrip": true, "departureCode": "WAW", "arrivalCode": "LON", "partnerCode": "ESKY", "currencyCode": "PLN"}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetPriceAlertCalendarQuery"}, "examples": {"Simple": {"value": {"isRoundTrip": true, "departureCode": "WAW", "arrivalCode": "LON", "partnerCode": "ESKY", "currencyCode": "PLN"}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetPriceAlertCalendarQuery"}, "examples": {"Simple": {"value": {"isRoundTrip": true, "departureCode": "WAW", "arrivalCode": "LON", "partnerCode": "ESKY", "currencyCode": "PLN"}}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1.0/Legacy/GetAtractiveAlternativeFlightsFromCache": {"post": {"tags": ["Legacy"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetAtractiveAlternativeFlightsFromCacheQuery"}, "example": {"departureDate": "2025-06-29T00:00:00Z", "returnDepartureDate": "2025-07-06T00:00:00Z", "departureCode": "WAS", "arrivalCode": "SEL", "languageCode": "EN", "price": 1499.99, "currencyCode": "USD", "partnerCode": "YOUR_PARTNER_CODE"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetAtractiveAlternativeFlightsFromCacheQuery"}, "example": {"departureDate": "2025-06-29T00:00:00Z", "returnDepartureDate": "2025-07-06T00:00:00Z", "departureCode": "WAS", "arrivalCode": "SEL", "languageCode": "EN", "price": 1499.99, "currencyCode": "USD", "partnerCode": "YOUR_PARTNER_CODE"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetAtractiveAlternativeFlightsFromCacheQuery"}, "example": {"departureDate": "2025-06-29T00:00:00Z", "returnDepartureDate": "2025-07-06T00:00:00Z", "departureCode": "WAS", "arrivalCode": "SEL", "languageCode": "EN", "price": 1499.99, "currencyCode": "USD", "partnerCode": "YOUR_PARTNER_CODE"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.GetAtractiveAlternativeFlightsFromCacheQuery"}, "example": {"departureDate": "2025-06-29T00:00:00Z", "returnDepartureDate": "2025-07-06T00:00:00Z", "departureCode": "WAS", "arrivalCode": "SEL", "languageCode": "EN", "price": 1499.99, "currencyCode": "USD", "partnerCode": "YOUR_PARTNER_CODE"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1.0/Packages/GetFlightsById": {"post": {"tags": ["Packages"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsById+Query"}, "example": {"partnerCode": "ESKY", "flightIds": ["LHRYYC24021735||WS19", "AALAGP240221149.32||D83079|D83672"], "includeNqs": true, "includeLegLocators": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsById+Query"}, "example": {"partnerCode": "ESKY", "flightIds": ["LHRYYC24021735||WS19", "AALAGP240221149.32||D83079|D83672"], "includeNqs": true, "includeLegLocators": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsById+Query"}, "example": {"partnerCode": "ESKY", "flightIds": ["LHRYYC24021735||WS19", "AALAGP240221149.32||D83079|D83672"], "includeNqs": true, "includeLegLocators": true}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsById+Query"}, "example": {"partnerCode": "ESKY", "flightIds": ["LHRYYC24021735||WS19", "AALAGP240221149.32||D83079|D83672"], "includeNqs": true, "includeLegLocators": true}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsById+Response"}}}}}}}, "/api/v1.0/Packages/GetFlightsByFlightOfferKey": {"post": {"tags": ["Packages"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsByFlightOfferKeyQuery"}, "example": {"partnerCode": "ESKY", "flightOfferKeys": ["KTWSTN24092978||FR2472:0:0,STNKTW24100578I||FR2471:0:1", "WAWLTN240725234.23||W61301:0:0#LTNWAW240801234.23||W61302:1:0", "WMISTN24072578||FR1022:0:0#LTNWAW240801234.23||W61302:1:1", "WAWLHR240725240801147||LO285||LO286:0:0"], "includeNqs": true, "includeLegLocators": true, "passengers": [{"code": "Adult", "count": 2}]}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsByFlightOfferKeyQuery"}, "example": {"partnerCode": "ESKY", "flightOfferKeys": ["KTWSTN24092978||FR2472:0:0,STNKTW24100578I||FR2471:0:1", "WAWLTN240725234.23||W61301:0:0#LTNWAW240801234.23||W61302:1:0", "WMISTN24072578||FR1022:0:0#LTNWAW240801234.23||W61302:1:1", "WAWLHR240725240801147||LO285||LO286:0:0"], "includeNqs": true, "includeLegLocators": true, "passengers": [{"code": "Adult", "count": 2}]}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsByFlightOfferKeyQuery"}, "example": {"partnerCode": "ESKY", "flightOfferKeys": ["KTWSTN24092978||FR2472:0:0,STNKTW24100578I||FR2471:0:1", "WAWLTN240725234.23||W61301:0:0#LTNWAW240801234.23||W61302:1:0", "WMISTN24072578||FR1022:0:0#LTNWAW240801234.23||W61302:1:1", "WAWLHR240725240801147||LO285||LO286:0:0"], "includeNqs": true, "includeLegLocators": true, "passengers": [{"code": "Adult", "count": 2}]}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsByFlightOfferKeyQuery"}, "example": {"partnerCode": "ESKY", "flightOfferKeys": ["KTWSTN24092978||FR2472:0:0,STNKTW24100578I||FR2471:0:1", "WAWLTN240725234.23||W61301:0:0#LTNWAW240801234.23||W61302:1:0", "WMISTN24072578||FR1022:0:0#LTNWAW240801234.23||W61302:1:1", "WAWLHR240725240801147||LO285||LO286:0:0"], "includeNqs": true, "includeLegLocators": true, "passengers": [{"code": "Adult", "count": 2}]}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightOffersForPackagesView"}}}}}}}, "/api/v1.0/Packages/GetFlightsFromCache": {"post": {"tags": ["Packages"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForPackagesQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "passengers": [{"code": "Adult", "count": 1}], "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LON"}], "minDepartureDate": "2025-06-29T00:00:00", "maxReturnDate": "2025-07-29T00:00:00", "minStayLength": 7, "maxStayLength": 7, "departureWeekDays": [6, 0], "returnWeekDays": [6, 0], "groupBy": ["Route", "DepartureDate", "ReturnDate"], "maxStops": 1}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForPackagesQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "passengers": [{"code": "Adult", "count": 1}], "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LON"}], "minDepartureDate": "2025-06-29T00:00:00", "maxReturnDate": "2025-07-29T00:00:00", "minStayLength": 7, "maxStayLength": 7, "departureWeekDays": [6, 0], "returnWeekDays": [6, 0], "groupBy": ["Route", "DepartureDate", "ReturnDate"], "maxStops": 1}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForPackagesQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "passengers": [{"code": "Adult", "count": 1}], "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LON"}], "minDepartureDate": "2025-06-29T00:00:00", "maxReturnDate": "2025-07-29T00:00:00", "minStayLength": 7, "maxStayLength": 7, "departureWeekDays": [6, 0], "returnWeekDays": [6, 0], "groupBy": ["Route", "DepartureDate", "ReturnDate"], "maxStops": 1}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForPackagesQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "passengers": [{"code": "Adult", "count": 1}], "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LON"}], "minDepartureDate": "2025-06-29T00:00:00", "maxReturnDate": "2025-07-29T00:00:00", "minStayLength": 7, "maxStayLength": 7, "departureWeekDays": [6, 0], "returnWeekDays": [6, 0], "groupBy": ["Route", "DepartureDate", "ReturnDate"], "maxStops": 1}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForPackagesView"}}}}}}}, "/api/v1.0/Packages/GetFlightOffers": {"post": {"tags": ["Packages"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.FlightOffersForPackagesQuery"}, "examples": {"by trip length": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "maxStops": 1, "limitPerDate": 2, "maxLegDurationInMinutes": 480, "tripLengthDuration": {"minDepartureDate": "2025-06-29", "maxDepartureDate": "2025-07-29", "minReturnArrivalDate": "2025-06-29", "maxReturnArrivalDate": "2025-07-29", "tripLengthInDays": [3, 4, 5], "earlyDepartureInHours": 2, "lateReturnArrivalInHours": 2}, "departureWeekDays": [6, 0], "includeWeekDays": [0, 1, 2, 3, 6], "includeNqs": true, "includeLegLocators": true}}, "by days at destination": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "maxStops": 1, "limitPerDate": 2, "maxLegDurationInMinutes": 480, "daysAtDestinationDuration": {"minArrivalDate": "2025-06-29", "maxArrivalDate": "2025-07-06", "minReturnDepartureDate": "2025-06-29", "maxReturnDepartureDate": "2025-07-06", "daysAtDestination": [4, 5, 6]}, "departureWeekDays": [6, 0], "includeWeekDays": [0, 1, 2, 3, 6], "includeNqs": true, "includeLegLocators": true}}, "by days at destination for LH airline": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "maxStops": 1, "limitPerDate": 2, "maxLegDurationInMinutes": 480, "daysAtDestinationDuration": {"minArrivalDate": "2025-06-29", "maxArrivalDate": "2025-07-06", "minReturnDepartureDate": "2025-06-29", "maxReturnDepartureDate": "2025-07-06", "daysAtDestination": [4, 5, 6]}, "departureWeekDays": [6, 0], "includeWeekDays": [0, 1, 2, 3, 6], "airlineCodes": ["LH"], "includeNqs": true, "includeLegLocators": true}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.FlightOffersForPackagesQuery"}, "examples": {"by trip length": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "maxStops": 1, "limitPerDate": 2, "maxLegDurationInMinutes": 480, "tripLengthDuration": {"minDepartureDate": "2025-06-29", "maxDepartureDate": "2025-07-29", "minReturnArrivalDate": "2025-06-29", "maxReturnArrivalDate": "2025-07-29", "tripLengthInDays": [3, 4, 5], "earlyDepartureInHours": 2, "lateReturnArrivalInHours": 2}, "departureWeekDays": [6, 0], "includeWeekDays": [0, 1, 2, 3, 6], "includeNqs": true, "includeLegLocators": true}}, "by days at destination": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "maxStops": 1, "limitPerDate": 2, "maxLegDurationInMinutes": 480, "daysAtDestinationDuration": {"minArrivalDate": "2025-06-29", "maxArrivalDate": "2025-07-06", "minReturnDepartureDate": "2025-06-29", "maxReturnDepartureDate": "2025-07-06", "daysAtDestination": [4, 5, 6]}, "departureWeekDays": [6, 0], "includeWeekDays": [0, 1, 2, 3, 6], "includeNqs": true, "includeLegLocators": true}}, "by days at destination for LH airline": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "maxStops": 1, "limitPerDate": 2, "maxLegDurationInMinutes": 480, "daysAtDestinationDuration": {"minArrivalDate": "2025-06-29", "maxArrivalDate": "2025-07-06", "minReturnDepartureDate": "2025-06-29", "maxReturnDepartureDate": "2025-07-06", "daysAtDestination": [4, 5, 6]}, "departureWeekDays": [6, 0], "includeWeekDays": [0, 1, 2, 3, 6], "airlineCodes": ["LH"], "includeNqs": true, "includeLegLocators": true}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.FlightOffersForPackagesQuery"}, "examples": {"by trip length": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "maxStops": 1, "limitPerDate": 2, "maxLegDurationInMinutes": 480, "tripLengthDuration": {"minDepartureDate": "2025-06-29", "maxDepartureDate": "2025-07-29", "minReturnArrivalDate": "2025-06-29", "maxReturnArrivalDate": "2025-07-29", "tripLengthInDays": [3, 4, 5], "earlyDepartureInHours": 2, "lateReturnArrivalInHours": 2}, "departureWeekDays": [6, 0], "includeWeekDays": [0, 1, 2, 3, 6], "includeNqs": true, "includeLegLocators": true}}, "by days at destination": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "maxStops": 1, "limitPerDate": 2, "maxLegDurationInMinutes": 480, "daysAtDestinationDuration": {"minArrivalDate": "2025-06-29", "maxArrivalDate": "2025-07-06", "minReturnDepartureDate": "2025-06-29", "maxReturnDepartureDate": "2025-07-06", "daysAtDestination": [4, 5, 6]}, "departureWeekDays": [6, 0], "includeWeekDays": [0, 1, 2, 3, 6], "includeNqs": true, "includeLegLocators": true}}, "by days at destination for LH airline": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "maxStops": 1, "limitPerDate": 2, "maxLegDurationInMinutes": 480, "daysAtDestinationDuration": {"minArrivalDate": "2025-06-29", "maxArrivalDate": "2025-07-06", "minReturnDepartureDate": "2025-06-29", "maxReturnDepartureDate": "2025-07-06", "daysAtDestination": [4, 5, 6]}, "departureWeekDays": [6, 0], "includeWeekDays": [0, 1, 2, 3, 6], "airlineCodes": ["LH"], "includeNqs": true, "includeLegLocators": true}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.FlightOffersForPackagesQuery"}, "examples": {"by trip length": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "maxStops": 1, "limitPerDate": 2, "maxLegDurationInMinutes": 480, "tripLengthDuration": {"minDepartureDate": "2025-06-29", "maxDepartureDate": "2025-07-29", "minReturnArrivalDate": "2025-06-29", "maxReturnArrivalDate": "2025-07-29", "tripLengthInDays": [3, 4, 5], "earlyDepartureInHours": 2, "lateReturnArrivalInHours": 2}, "departureWeekDays": [6, 0], "includeWeekDays": [0, 1, 2, 3, 6], "includeNqs": true, "includeLegLocators": true}}, "by days at destination": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "maxStops": 1, "limitPerDate": 2, "maxLegDurationInMinutes": 480, "daysAtDestinationDuration": {"minArrivalDate": "2025-06-29", "maxArrivalDate": "2025-07-06", "minReturnDepartureDate": "2025-06-29", "maxReturnDepartureDate": "2025-07-06", "daysAtDestination": [4, 5, 6]}, "departureWeekDays": [6, 0], "includeWeekDays": [0, 1, 2, 3, 6], "includeNqs": true, "includeLegLocators": true}}, "by days at destination for LH airline": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "maxStops": 1, "limitPerDate": 2, "maxLegDurationInMinutes": 480, "daysAtDestinationDuration": {"minArrivalDate": "2025-06-29", "maxArrivalDate": "2025-07-06", "minReturnDepartureDate": "2025-06-29", "maxReturnDepartureDate": "2025-07-06", "daysAtDestination": [4, 5, 6]}, "departureWeekDays": [6, 0], "includeWeekDays": [0, 1, 2, 3, 6], "airlineCodes": ["LH"], "includeNqs": true, "includeLegLocators": true}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightOffersForPackagesView"}}}}}, "deprecated": true}}, "/api/v2.0/Packages/GetFlightOffers": {"post": {"tags": ["Packages"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.FlightOffersQuery"}, "examples": {"example": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "minCheckIn": "2025-06-29", "maxCheckIn": "2025-07-06", "stayLengths": [4, 5, 6], "maxStops": 1, "limitPerGroup": 2, "maxLegDurationInMinutes": 480, "departureWeekDays": [6, 0], "includeNqs": true, "includeLegLocators": true, "currencyCode": "PLN"}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.FlightOffersQuery"}, "examples": {"example": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "minCheckIn": "2025-06-29", "maxCheckIn": "2025-07-06", "stayLengths": [4, 5, 6], "maxStops": 1, "limitPerGroup": 2, "maxLegDurationInMinutes": 480, "departureWeekDays": [6, 0], "includeNqs": true, "includeLegLocators": true, "currencyCode": "PLN"}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.FlightOffersQuery"}, "examples": {"example": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "minCheckIn": "2025-06-29", "maxCheckIn": "2025-07-06", "stayLengths": [4, 5, 6], "maxStops": 1, "limitPerGroup": 2, "maxLegDurationInMinutes": 480, "departureWeekDays": [6, 0], "includeNqs": true, "includeLegLocators": true, "currencyCode": "PLN"}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.FlightOffersQuery"}, "examples": {"example": {"value": {"partnerCode": "ESKYPLPACKAGES", "destinations": [{"departureAirport": "KTW", "arrivalAirport": "LTN"}], "minCheckIn": "2025-06-29", "maxCheckIn": "2025-07-06", "stayLengths": [4, 5, 6], "maxStops": 1, "limitPerGroup": 2, "maxLegDurationInMinutes": 480, "departureWeekDays": [6, 0], "includeNqs": true, "includeLegLocators": true, "currencyCode": "PLN"}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightOffersForPackagesView"}}}}}}}, "/api/v1.0/Packages/GetAlternativeFlights": {"post": {"tags": ["Packages"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.AlternativeFlights+Query"}, "examples": {"example": {"value": {"departureAirports": ["KTW", "KRK"], "arrivalAirports": ["LTN", "LHR", "STN", "LGW", "LCY"], "arrivalDate": "2025-06-29T00:00:00", "returnDate": "2025-07-06T00:00:00", "maxLegDurationInMinutes": 180, "maxStops": 1, "partnerCode": "ESKYPLPACKAGES", "passengers": [{"code": "Adult", "count": 2}]}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.AlternativeFlights+Query"}, "examples": {"example": {"value": {"departureAirports": ["KTW", "KRK"], "arrivalAirports": ["LTN", "LHR", "STN", "LGW", "LCY"], "arrivalDate": "2025-06-29T00:00:00", "returnDate": "2025-07-06T00:00:00", "maxLegDurationInMinutes": 180, "maxStops": 1, "partnerCode": "ESKYPLPACKAGES", "passengers": [{"code": "Adult", "count": 2}]}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.AlternativeFlights+Query"}, "examples": {"example": {"value": {"departureAirports": ["KTW", "KRK"], "arrivalAirports": ["LTN", "LHR", "STN", "LGW", "LCY"], "arrivalDate": "2025-06-29T00:00:00", "returnDate": "2025-07-06T00:00:00", "maxLegDurationInMinutes": 180, "maxStops": 1, "partnerCode": "ESKYPLPACKAGES", "passengers": [{"code": "Adult", "count": 2}]}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.AlternativeFlights+Query"}, "examples": {"example": {"value": {"departureAirports": ["KTW", "KRK"], "arrivalAirports": ["LTN", "LHR", "STN", "LGW", "LCY"], "arrivalDate": "2025-06-29T00:00:00", "returnDate": "2025-07-06T00:00:00", "maxLegDurationInMinutes": 180, "maxStops": 1, "partnerCode": "ESKYPLPACKAGES", "passengers": [{"code": "Adult", "count": 2}]}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightOffersForPackagesView"}}}}}, "deprecated": true}}, "/api/v2.0/Packages/GetAlternativeFlights": {"post": {"tags": ["Packages"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.AlternativeOffersQuery"}, "examples": {"example": {"value": {"departureAirports": ["KTW", "KRK"], "arrivalAirports": ["LTN", "LHR", "STN", "LGW", "LCY"], "checkIn": "2025-06-29", "stayLength": 7, "maxLegDurationInMinutes": 180, "maxStops": 1, "partnerCode": "ESKYPLPACKAGES", "passengers": [{"code": "Adult", "count": 2}], "includeLegLocators": true, "limit": 10}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.AlternativeOffersQuery"}, "examples": {"example": {"value": {"departureAirports": ["KTW", "KRK"], "arrivalAirports": ["LTN", "LHR", "STN", "LGW", "LCY"], "checkIn": "2025-06-29", "stayLength": 7, "maxLegDurationInMinutes": 180, "maxStops": 1, "partnerCode": "ESKYPLPACKAGES", "passengers": [{"code": "Adult", "count": 2}], "includeLegLocators": true, "limit": 10}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.AlternativeOffersQuery"}, "examples": {"example": {"value": {"departureAirports": ["KTW", "KRK"], "arrivalAirports": ["LTN", "LHR", "STN", "LGW", "LCY"], "checkIn": "2025-06-29", "stayLength": 7, "maxLegDurationInMinutes": 180, "maxStops": 1, "partnerCode": "ESKYPLPACKAGES", "passengers": [{"code": "Adult", "count": 2}], "includeLegLocators": true, "limit": 10}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.AlternativeOffersQuery"}, "examples": {"example": {"value": {"departureAirports": ["KTW", "KRK"], "arrivalAirports": ["LTN", "LHR", "STN", "LGW", "LCY"], "checkIn": "2025-06-29", "stayLength": 7, "maxLegDurationInMinutes": 180, "maxStops": 1, "partnerCode": "ESKYPLPACKAGES", "passengers": [{"code": "Adult", "count": 2}], "includeLegLocators": true, "limit": 10}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightOffersForPackagesView"}}}}}}}, "/api/v2.0/Packages/GetFlightOffersForDynamicPackages": {"post": {"tags": ["Packages"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.DynamicPackages+Request"}, "examples": {"example": {"value": {"partnerCode": "ESKYPLPACKAGES", "minCheckIn": "2025-06-08", "maxCheckIn": "2025-06-18", "routes": [{"departure": "WAW", "arrival": "LTN"}, {"departure": "WAW", "arrival": "LHR"}], "stayLengths": [5, 6], "limitPerArrivalAirport": 3, "passengers": [{"code": "Adult", "count": 2}]}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.DynamicPackages+Request"}, "examples": {"example": {"value": {"partnerCode": "ESKYPLPACKAGES", "minCheckIn": "2025-06-08", "maxCheckIn": "2025-06-18", "routes": [{"departure": "WAW", "arrival": "LTN"}, {"departure": "WAW", "arrival": "LHR"}], "stayLengths": [5, 6], "limitPerArrivalAirport": 3, "passengers": [{"code": "Adult", "count": 2}]}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.DynamicPackages+Request"}, "examples": {"example": {"value": {"partnerCode": "ESKYPLPACKAGES", "minCheckIn": "2025-06-08", "maxCheckIn": "2025-06-18", "routes": [{"departure": "WAW", "arrival": "LTN"}, {"departure": "WAW", "arrival": "LHR"}], "stayLengths": [5, 6], "limitPerArrivalAirport": 3, "passengers": [{"code": "Adult", "count": 2}]}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.DynamicPackages+Request"}, "examples": {"example": {"value": {"partnerCode": "ESKYPLPACKAGES", "minCheckIn": "2025-06-08", "maxCheckIn": "2025-06-18", "routes": [{"departure": "WAW", "arrival": "LTN"}, {"departure": "WAW", "arrival": "LHR"}], "stayLengths": [5, 6], "limitPerArrivalAirport": 3, "passengers": [{"code": "Adult", "count": 2}]}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.DynamicPackages+Response"}}}}}}}, "/api/v1.0/TravelExplorer/GetFlightsFromCache": {"post": {"tags": ["TravelExplorer"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForTravelExplorerQuery"}, "examples": {"default limit": {"value": {"keys": ["5c1e7de9-772d-445d-8379-98e460838cee"], "partnerCode": "ESKY", "currencyCode": "PLN", "languageCode": "PL", "limit": 5}}, "with default partner currency": {"value": {"keys": ["235747f3-c0c6-4715-919c-86bc8d8efb15"], "partnerCode": "ESKY", "languageCode": "PL", "limit": 5}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForTravelExplorerQuery"}, "examples": {"default limit": {"value": {"keys": ["5c1e7de9-772d-445d-8379-98e460838cee"], "partnerCode": "ESKY", "currencyCode": "PLN", "languageCode": "PL", "limit": 5}}, "with default partner currency": {"value": {"keys": ["235747f3-c0c6-4715-919c-86bc8d8efb15"], "partnerCode": "ESKY", "languageCode": "PL", "limit": 5}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForTravelExplorerQuery"}, "examples": {"default limit": {"value": {"keys": ["5c1e7de9-772d-445d-8379-98e460838cee"], "partnerCode": "ESKY", "currencyCode": "PLN", "languageCode": "PL", "limit": 5}}, "with default partner currency": {"value": {"keys": ["235747f3-c0c6-4715-919c-86bc8d8efb15"], "partnerCode": "ESKY", "languageCode": "PL", "limit": 5}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForTravelExplorerQuery"}, "examples": {"default limit": {"value": {"keys": ["5c1e7de9-772d-445d-8379-98e460838cee"], "partnerCode": "ESKY", "currencyCode": "PLN", "languageCode": "PL", "limit": 5}}, "with default partner currency": {"value": {"keys": ["235747f3-c0c6-4715-919c-86bc8d8efb15"], "partnerCode": "ESKY", "languageCode": "PL", "limit": 5}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForTravelExplorerView"}}}}}}}, "/api/v1.0/TravelExplorer/GetFlightsByOfferKey": {"post": {"tags": ["TravelExplorer"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForTravelExplorer.GetByFlightOfferKey+Query"}, "examples": {"singleticket": {"value": {"flightOfferKey": "GDNPMI240831240907183||SK1758|SK2811||SK2812|SK753:0:0", "partnerCode": "ESKY", "paxConfiguration": "*******"}}, "singleticket from OB & IB": {"value": {"flightOfferKey": "KTWSTN24110178||FR8267:0:0,STNKTW24111378I||FR2471:0:1", "partnerCode": "ESKY", "paxConfiguration": "*******"}}, "multiticket": {"value": {"flightOfferKey": "GDNPMI240826149.32||DY1055|DY1734:0:0#PMIGDN240903190||SK586|SK759:1:0", "partnerCode": "ESKY", "paxConfiguration": "*******"}}}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForTravelExplorer.GetByFlightOfferKey+Query"}, "examples": {"singleticket": {"value": {"flightOfferKey": "GDNPMI240831240907183||SK1758|SK2811||SK2812|SK753:0:0", "partnerCode": "ESKY", "paxConfiguration": "*******"}}, "singleticket from OB & IB": {"value": {"flightOfferKey": "KTWSTN24110178||FR8267:0:0,STNKTW24111378I||FR2471:0:1", "partnerCode": "ESKY", "paxConfiguration": "*******"}}, "multiticket": {"value": {"flightOfferKey": "GDNPMI240826149.32||DY1055|DY1734:0:0#PMIGDN240903190||SK586|SK759:1:0", "partnerCode": "ESKY", "paxConfiguration": "*******"}}}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForTravelExplorer.GetByFlightOfferKey+Query"}, "examples": {"singleticket": {"value": {"flightOfferKey": "GDNPMI240831240907183||SK1758|SK2811||SK2812|SK753:0:0", "partnerCode": "ESKY", "paxConfiguration": "*******"}}, "singleticket from OB & IB": {"value": {"flightOfferKey": "KTWSTN24110178||FR8267:0:0,STNKTW24111378I||FR2471:0:1", "partnerCode": "ESKY", "paxConfiguration": "*******"}}, "multiticket": {"value": {"flightOfferKey": "GDNPMI240826149.32||DY1055|DY1734:0:0#PMIGDN240903190||SK586|SK759:1:0", "partnerCode": "ESKY", "paxConfiguration": "*******"}}}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForTravelExplorer.GetByFlightOfferKey+Query"}, "examples": {"singleticket": {"value": {"flightOfferKey": "GDNPMI240831240907183||SK1758|SK2811||SK2812|SK753:0:0", "partnerCode": "ESKY", "paxConfiguration": "*******"}}, "singleticket from OB & IB": {"value": {"flightOfferKey": "KTWSTN24110178||FR8267:0:0,STNKTW24111378I||FR2471:0:1", "partnerCode": "ESKY", "paxConfiguration": "*******"}}, "multiticket": {"value": {"flightOfferKey": "GDNPMI240826149.32||DY1055|DY1734:0:0#PMIGDN240903190||SK586|SK759:1:0", "partnerCode": "ESKY", "paxConfiguration": "*******"}}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForTravelExplorerView"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForTravelExplorerView"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForTravelExplorerView"}}}}, "400": {"description": "Bad Request"}}}}}, "components": {"schemas": {"Esky.FlightSearch.Contracts.DomainEnums.PersonTypeEnum": {"enum": ["None", "Adult", "Child", "Youth", "Infant", "Senior"], "type": "string"}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SearchFlightsRequestPassenger": {"required": ["count"], "type": "object", "properties": {"code": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.DomainEnums.PersonTypeEnum"}, "count": {"type": "integer", "description": "Number of passengers of a specified type", "format": "int32"}, "ages": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}}, "Esky.FlightSearch.Contracts.Queries.FlightCache.SpecialOfferFlightsQuery": {"required": ["arrivalCode", "currencyCode", "departureCode", "departureDate", "partnerCode"], "type": "object", "properties": {"partnerCode": {"type": "string", "description": "Requested partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Requested currency code", "nullable": true}, "departureDate": {"type": "string", "description": "Departure date", "format": "date-time"}, "returnDepartureDate": {"type": "string", "description": "Departure date of a return flight (for roundtrip queries)", "format": "date-time", "nullable": true}, "departureCode": {"type": "string", "description": "Code of departure location\r\nCan be a 3-letter IATA airport code, city code, or an eSky multiport code", "nullable": true}, "arrivalCode": {"type": "string", "description": "Code of arrival location\r\nCan be a 3-letter IATA airport code, city code, or an eSky multiport code", "nullable": true}, "passengers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SearchFlightsRequestPassenger"}, "description": "Passenger configuration\r\nDefault is a single adult configuration", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.SpecialOfferSegment": {"required": ["aac", "ac", "ad", "dac", "dd", "fn", "ft"], "type": "object", "properties": {"ac": {"type": "string", "description": "Airline code", "nullable": true}, "dac": {"type": "string", "description": "Departure location code\r\n3-letter IATA airport code", "nullable": true}, "dd": {"type": "string", "description": "Departure date", "format": "date-time"}, "aac": {"type": "string", "description": "Arrival location code\r\n3-letter IATA airport code", "nullable": true}, "ad": {"type": "string", "description": "Arrival date", "format": "date-time"}, "ft": {"type": "string", "description": "Flight time in hh:mm format", "nullable": true}, "fn": {"type": "string", "description": "Flight number", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.SpecialOfferLeg": {"required": ["ft", "segs"], "type": "object", "properties": {"ft": {"type": "string", "description": "Flight time in hh:mm format", "nullable": true}, "segs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.SpecialOfferSegment"}, "description": "Leg segments", "nullable": true}, "deepLink": {"type": "string", "description": "Deeplink for pricing", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.SpecialOfferFlight": {"required": ["c", "legs", "p"], "type": "object", "properties": {"c": {"type": "string", "description": "Currency code", "nullable": true}, "p": {"type": "number", "description": "Flight price", "format": "decimal"}, "legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.SpecialOfferLeg"}, "description": "Flight legs", "nullable": true}, "legthOfStay": {"type": "integer", "format": "int32"}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.SpecialOfferFlightsView": {"required": ["flights"], "type": "object", "properties": {"flights": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.SpecialOfferFlight"}, "description": "List of grouped flight results", "nullable": true}}}, "Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQuery+RuntimeModeEnum": {"enum": ["Live", "Test"], "type": "string"}, "Esky.FlightSearch.Contracts.CommonObjects.DestinationSet": {"type": "object", "properties": {"airportCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "multiportCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "cityCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "countryCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "continentCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "regionCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "anywhere": {"type": "boolean"}}}, "Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQuery+GroupByDefinition": {"type": "object", "properties": {"by": {"type": "string", "nullable": true}, "limit": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}}}, "Esky.FlightSearch.Contracts.Enums.OrderTypeEnum": {"enum": ["None", "Price", "StandardScore", "PricePerDistance"], "type": "string"}, "Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQuery": {"type": "object", "properties": {"runtimeMode": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQuery+RuntimeModeEnum"}, "partnerCode": {"type": "string", "nullable": true}, "currencyCode": {"type": "string", "nullable": true}, "airlineCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "departureDestinationSet": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DestinationSet"}, "arrivalDestinationSet": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DestinationSet"}, "departureMonth": {"type": "string", "format": "date-time", "nullable": true}, "groupBy": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQuery+GroupByDefinition"}, "nullable": true}, "orderBy": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Enums.OrderTypeEnum"}, "maxStandardScore": {"type": "number", "format": "double", "nullable": true}, "maxCountryToCountryStandardScore": {"type": "number", "format": "double", "nullable": true}, "showDebugInfo": {"type": "boolean"}}}, "Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult+Price": {"type": "object", "properties": {"amount": {"type": "number", "format": "decimal"}, "currency": {"type": "string", "nullable": true}}}, "Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult+GroupSummary": {"type": "object", "properties": {"byCode": {"type": "string", "nullable": true}, "minimalPrice": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult+Price"}}}, "Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult+Group": {"type": "object", "properties": {"groupSummary": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult+GroupSummary"}}}, "Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult+LocationWithDate": {"type": "object", "properties": {"airportCode": {"type": "string", "nullable": true}, "cityCode": {"type": "string", "nullable": true}, "countryCode": {"type": "string", "nullable": true}, "date": {"type": "string", "format": "date-time"}}}, "Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult+Offer": {"type": "object", "properties": {"airlineCode": {"type": "string", "nullable": true}, "providerCode": {"type": "integer", "format": "int32"}, "price": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult+Price"}, "stopovers": {"type": "integer", "format": "int32"}, "stopoversTime": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "departure": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult+LocationWithDate"}, "arrival": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult+LocationWithDate"}, "refreshDate": {"type": "string", "format": "date-time", "nullable": true}}}, "Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult": {"type": "object", "properties": {"groups": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult+Group"}, "nullable": true}, "totalGroupsCount": {"type": "integer", "format": "int32", "nullable": true}, "offers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.BestOffers.BestOffersQueryResult+Offer"}, "nullable": true}, "totalOffersCount": {"type": "integer", "format": "int32", "nullable": true}}}, "Esky.FlightSearch.Contracts.Queries.Flights.CalendarResultFields": {"enum": ["Amount", "<PERSON><PERSON><PERSON><PERSON>", "QualityScore", "TransferCount"], "type": "string"}, "Esky.FlightSearch.LandingPages.Deals.Request+ConcreteDestinations": {"type": "object", "properties": {"airportCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "multiportCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "cityCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "countryCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "continentCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "regionCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}}}, "Esky.FlightSearch.LandingPages.Deals.Request+AnyDestinations": {"type": "object", "properties": {"anywhere": {"type": "boolean"}, "airportCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "multiportCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "cityCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "countryCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "continentCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "regionCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}}}, "Esky.FlightSearch.LandingPages.Deals.Request+SortingOption": {"enum": ["Price", "Score"], "type": "string"}, "Esky.FlightSearch.LandingPages.Deals.Request": {"required": ["arrivalDestinations", "departureDateFrom", "departureDateTo", "departureDestinations", "limit", "onlyDirectFlights", "partnerCode", "sorting", "stayDurationFrom", "stayDurationTo"], "type": "object", "properties": {"partnerCode": {"type": "string", "nullable": true}, "departureDestinations": {"$ref": "#/components/schemas/Esky.FlightSearch.LandingPages.Deals.Request+ConcreteDestinations"}, "arrivalDestinations": {"$ref": "#/components/schemas/Esky.FlightSearch.LandingPages.Deals.Request+AnyDestinations"}, "departureDateFrom": {"type": "string", "format": "date"}, "departureDateTo": {"type": "string", "format": "date"}, "stayDurationFrom": {"type": "integer", "format": "int32"}, "stayDurationTo": {"type": "integer", "format": "int32"}, "onlyDirectFlights": {"type": "boolean"}, "sorting": {"$ref": "#/components/schemas/Esky.FlightSearch.LandingPages.Deals.Request+SortingOption"}, "limit": {"type": "integer", "format": "int32"}, "sessionId": {"type": "string", "nullable": true}}}, "Esky.FlightSearch.LandingPages.Deals.Segment": {"required": ["airlineCode", "arrivalCode", "arrivalDate", "departureCode", "departureDate"], "type": "object", "properties": {"airlineCode": {"type": "string", "nullable": true}, "departureCode": {"type": "string", "nullable": true}, "departureDate": {"type": "string", "format": "date-time"}, "arrivalCode": {"type": "string", "nullable": true}, "arrivalDate": {"type": "string", "format": "date-time"}}}, "Esky.FlightSearch.LandingPages.Deals.Leg": {"required": ["segments"], "type": "object", "properties": {"segments": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.LandingPages.Deals.Segment"}, "nullable": true}}}, "Esky.FlightSearch.LandingPages.Deals.PriceModel": {"required": ["amount", "transactionFee"], "type": "object", "properties": {"amount": {"type": "number", "format": "decimal"}, "transactionFee": {"type": "number", "format": "decimal"}}}, "Esky.FlightSearch.LandingPages.Deals.Offer": {"required": ["legs", "nqs", "price", "stayDuration"], "type": "object", "properties": {"legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.LandingPages.Deals.Leg"}, "nullable": true}, "price": {"$ref": "#/components/schemas/Esky.FlightSearch.LandingPages.Deals.PriceModel"}, "stayDuration": {"type": "integer", "format": "int32"}, "nqs": {"type": "number", "format": "decimal"}}}, "Esky.FlightSearch.LandingPages.Deals.Response": {"required": ["currencyCode", "offers"], "type": "object", "properties": {"currencyCode": {"type": "string", "nullable": true}, "offers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.LandingPages.Deals.Offer"}, "nullable": true}}}, "Esky.FlightSearch.Contracts.DomainEnums.LengthOfStayEnum": {"enum": ["Any", "Weekends", "Range0To3Days", "Range4To7Days", "Range8To14Days", "Range15To21Days", "Custom"], "type": "string", "description": "Trip duration"}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.ISearchFlightsRequestPassenger": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.DomainEnums.PersonTypeEnum"}, "count": {"type": "integer", "description": "Number of passengers of a specified type", "format": "int32", "readOnly": true}}}, "Esky.FlightSearch.Contracts.Queries.Legacy.GetSpecialOfferCalendarFromCacheQuery": {"required": ["arrivalCode", "currencyCode", "departureCode", "isRoundTrip", "partnerCode"], "type": "object", "properties": {"isRoundTrip": {"type": "boolean", "description": "True for roundtrip flights, false for one way flights"}, "selectedDepartureDay": {"type": "string", "description": "Departure day selected on calendar.\r\nWhen specified, service returns best outbound price only for a selected day (paired with a collection of return flights)\r\nWhen not specified, a full 12-month calendar of outbound best prices is returned - without any return flight information", "format": "date-time", "nullable": true}, "lengthOfStay": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.DomainEnums.LengthOfStayEnum"}, "specialOfferId": {"type": "integer", "description": "Id of eSky special offer to retrieve", "format": "int32", "nullable": true}, "departureCode": {"type": "string", "description": "Code of departure location\r\nCan be a 3-letter IATA airport code, city code, or an eSky multiport code", "nullable": true}, "arrivalCode": {"type": "string", "description": "Code of arrival location\r\nCan be a 3-letter IATA airport code, city code, or an eSky multiport code", "nullable": true}, "passengers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.ISearchFlightsRequestPassenger"}, "description": "Passenger configuration\r\nDefault is a single adult configuration", "nullable": true}, "airlineCodes": {"type": "array", "items": {"type": "string"}, "description": "filter by airlines codes", "nullable": true}, "partnerCode": {"type": "string", "description": "Requested partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Requested currency code", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Calendar.CalendarItem": {"required": ["departureDate", "minPriceAmount"], "type": "object", "properties": {"departureDate": {"type": "string", "description": "Date of flight", "format": "date-time"}, "minPriceAmount": {"type": "number", "description": "Best price in requested currency", "format": "decimal"}}, "description": "Single calendar cell"}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Calendar.InboundSpecialOfferCalendarFromCacheView+CalendarItem": {"required": ["outboundFlight"], "type": "object", "properties": {"outboundFlight": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Calendar.CalendarItem"}, "inboundFlights": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Calendar.CalendarItem"}, "description": "Collection of cheapest inbound flight for each day", "nullable": true}}, "description": "Single calendar item (cell)"}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Calendar.SpecialOfferCalendarFromCacheView+MonthPrice": {"required": ["amount", "month"], "type": "object", "properties": {"amount": {"type": "number", "description": "Value in requested currency", "format": "decimal"}, "month": {"type": "string", "description": "Date representing corresponding month", "format": "date-time"}}, "description": "Describes month price"}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Calendar.InboundSpecialOfferCalendarFromCacheView": {"required": ["calendarItems"], "type": "object", "properties": {"calendarItems": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Calendar.InboundSpecialOfferCalendarFromCacheView+CalendarItem"}, "description": "Collection of calendar items (cells) representing price of cheapest outbound flight for a single day\r\npaired with a collection of cheapest return (inbound) flights for each day", "nullable": true}, "airTrafficRuleId": {"type": "integer", "description": "Obsolete - for backward compatibility only", "format": "int32", "deprecated": true}, "airTrafficRuleVersion": {"type": "string", "description": "Obsolete - for backward compatibility only", "nullable": true, "deprecated": true}, "bestOutboundMonthPrices": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Calendar.SpecialOfferCalendarFromCacheView+MonthPrice"}, "description": "Best month prices for outbound flights", "nullable": true}, "bestInboundMonthPrices": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Calendar.SpecialOfferCalendarFromCacheView+MonthPrice"}, "description": "Best month prices for inbound flights", "nullable": true}}, "description": "Inbound calendar data container"}, "Esky.FlightSearch.Contracts.Queries.Legacy.GetSpecialOfferFlightsFromCacheQuery": {"required": ["arrivalCode", "currencyCode", "departureCode", "departureDate", "languageCode", "partnerCode"], "type": "object", "properties": {"departureDate": {"type": "string", "description": "Departure date", "format": "date-time"}, "returnDepartureDate": {"type": "string", "description": "Departure date of a return flight (for roundtrip queries)", "format": "date-time", "nullable": true}, "languageCode": {"type": "string", "description": "Language code used for descriptions translation", "nullable": true}, "specialOfferId": {"type": "integer", "description": "Id of eSky special offer to retrieve", "format": "int32", "nullable": true}, "departureCode": {"type": "string", "description": "Code of departure location\r\nCan be a 3-letter IATA airport code, city code, or an eSky multiport code", "nullable": true}, "arrivalCode": {"type": "string", "description": "Code of arrival location\r\nCan be a 3-letter IATA airport code, city code, or an eSky multiport code", "nullable": true}, "passengers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.ISearchFlightsRequestPassenger"}, "description": "Passenger configuration\r\nDefault is a single adult configuration", "nullable": true}, "airlineCodes": {"type": "array", "items": {"type": "string"}, "description": "filter by airlines codes", "nullable": true}, "partnerCode": {"type": "string", "description": "Requested partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Requested currency code", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.StopoverItem": {"type": "object", "properties": {"airportCode": {"type": "string", "nullable": true}, "arrivalDate": {"type": "string", "format": "date-time", "nullable": true}, "departureDate": {"type": "string", "format": "date-time", "nullable": true}, "arrivalTerminal": {"type": "string", "nullable": true}, "departureTerminal": {"type": "string", "nullable": true}, "cityCode": {"type": "string", "nullable": true}, "countryCode": {"type": "string", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FareItem": {"type": "object", "properties": {"pc": {"type": "integer", "description": "Passenger code", "format": "int32"}, "fc": {"type": "string", "description": "Fare code", "nullable": true}, "addData": {"type": "string", "description": "Additional data", "nullable": true}}}, "Esky.Common.Enums.FlightFacilityType": {"enum": ["None", "RegisteredBaggage", "Wifi", "<PERSON><PERSON>", "Refreshments", "PowerPlug", "Entertainment", "LegRoom", "SeatConfiguration"], "type": "string"}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightFacility": {"type": "object", "properties": {"t": {"$ref": "#/components/schemas/Esky.Common.Enums.FlightFacilityType"}, "d": {"type": "string", "nullable": true}, "o": {"type": "boolean"}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightAttribute": {"type": "object", "properties": {"t": {"type": "string", "description": "Flight attribute type", "nullable": true}, "d": {"type": "string", "description": "Flight attribute description", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightProperty": {"type": "object", "properties": {"t": {"type": "string", "description": "Flight property type", "nullable": true}, "k": {"type": "string", "description": "Flight property key", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightAmenityPrice": {"type": "object", "properties": {"c": {"type": "string", "description": "Flight amenity price currency", "nullable": true}, "a": {"type": "number", "description": "Flight amenity price amount", "format": "decimal"}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightAmenity": {"type": "object", "properties": {"t": {"type": "string", "description": "Flight amenity type", "nullable": true}, "k": {"type": "string", "description": "Flight amenity key", "nullable": true}, "ia": {"type": "boolean", "description": "Flight amenity is available"}, "ip": {"type": "boolean", "description": "Flight amenity is paid"}, "p": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightAmenityPrice"}, "w": {"type": "integer", "description": "Flight amenity weight in kgs", "format": "int32"}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SegmentItem": {"required": ["aac", "ac", "acc", "ad", "bc", "dac", "dcc", "dd", "fares", "fn", "ft", "mid", "pc", "sac", "sc"], "type": "object", "properties": {"ac": {"type": "string", "description": "Airline code, example values: LO, W6, FR,", "nullable": true}, "dac": {"type": "string", "description": "Departure location code,\r\n3-letter IATA airport code, example values: WAW, LTN", "nullable": true}, "dcc": {"type": "string", "description": "Departure city code, example values: WAW, LON", "nullable": true}, "dd": {"type": "string", "description": "Departure date, example values: Date(1709099700000), Date(1713870900000)", "format": "date-time"}, "aac": {"type": "string", "description": "Arrival location code,\r\n3-letter IATA airport code, example values: WAW, LTN", "nullable": true}, "acc": {"type": "string", "description": "Arrival city code, example values: WAW, LON", "nullable": true}, "ad": {"type": "string", "description": "Arrival date, example values: Date(1709099700000), Date(1713962400000)", "format": "date-time"}, "ft": {"type": "string", "description": "Flight time in hh:mm format, example values: 2:35", "nullable": true}, "fn": {"type": "string", "description": "Flight number, example values: 286, 3883", "nullable": true}, "pc": {"type": "integer", "description": "Provider code", "format": "int32"}, "sac": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.StopoverItem"}, "description": "Stopovers", "nullable": true}, "fares": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FareItem"}, "description": "<PERSON>es", "nullable": true}, "bc": {"type": "string", "description": "Booking class code", "nullable": true}, "sc": {"type": "integer", "description": "Service class code\r\nAny = 0, Economy = 1, First = 2, Business = 3, EconomyPremium = 4", "format": "int32"}, "mid": {"type": "integer", "description": "Mixed flight identifier", "format": "int32"}, "ff": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightFacility"}, "description": "Flight facilities", "nullable": true}, "fa": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightAttribute"}, "description": "Flight attributes", "nullable": true}, "fp": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightProperty"}, "description": "Flight properties", "nullable": true}, "fam": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightAmenity"}, "description": "Flight amenities", "nullable": true}, "a": {"type": "string", "description": "Aircraft code, example values: Boeing737, Embraer195, CanadairRegionalJet900", "nullable": true}, "iST": {"type": "boolean", "description": "Is self transfer"}, "iP": {"type": "boolean", "description": "Is protected", "nullable": true}, "ob": {"type": "string", "description": "Flight is operated by, example values: LO, W6, FR,", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.LegItem": {"required": ["asc", "fid", "ft", "oid", "segs"], "type": "object", "properties": {"fid": {"type": "string", "description": "Leg identifier", "nullable": true}, "oid": {"type": "string", "description": "Unique offer identifier in search results scope. Should return the same value across search requests.", "nullable": true}, "ft": {"type": "string", "description": "Flight time in hh:mm format, example values: 2:35", "nullable": true}, "asc": {"type": "integer", "description": "Available seats count, max to 9", "format": "int32"}, "ll": {"type": "string", "description": "Encoded leg locator", "nullable": true}, "ffll": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Encoded leg locators for bundled flights", "nullable": true}, "nqs": {"type": "number", "description": "Negative quality score", "format": "decimal"}, "pi": {"type": "string", "description": "DeepLink (pricing indicator) used by metasearch", "nullable": true}, "or": {"type": "boolean", "description": "Optional reservation (pending airlines)"}, "dn": {"type": "boolean", "description": "Is the departure airport different from requested airport (nearby airport)"}, "an": {"type": "boolean", "description": "Is the arrival airport different from requested airport (nearby airport)"}, "ddn": {"type": "boolean", "description": "Is the departure date different from requested date (flex result)"}, "segs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SegmentItem"}, "description": "Leg segments", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightItemWithLegGroups+LegGroup": {"required": ["legs"], "type": "object", "properties": {"legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.LegItem"}, "description": "Flight legs in group", "nullable": true}, "dn": {"type": "boolean", "description": "Is the departure airport different from requested airport (nearby airport)"}, "an": {"type": "boolean", "description": "Is the arrival airport different from requested airport (nearby airport)"}, "ddn": {"type": "boolean", "description": "Is the departure date different from requested date (flex result)"}}, "description": "Leg group"}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightItemWithLegGroups": {"required": ["c", "cs", "fid", "lg", "p", "pc", "ppx", "tbf", "tf", "tp", "ttf"], "type": "object", "properties": {"lg": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightItemWithLegGroups+LegGroup"}, "description": "Legs grouped by price", "nullable": true}, "cacheOfferKeys": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "nqsoid": {"type": "array", "items": {"type": "string"}, "description": "Offer ids of best legs, according to NQS", "nullable": true}, "__type": {"type": "string", "description": "Obsolete - for backward compatibility only", "nullable": true, "deprecated": true}, "c": {"type": "string", "description": "Currency code", "nullable": true}, "cs": {"type": "string", "description": "Currency symbol", "nullable": true}, "du": {"type": "number", "description": "DU fee", "format": "decimal"}, "pc": {"type": "integer", "description": "Provider code", "format": "int32"}, "p": {"type": "number", "description": "Flight price", "format": "decimal"}, "tp": {"type": "number", "format": "decimal"}, "ppx": {"type": "number", "description": "Price per passenger", "format": "decimal"}, "tbf": {"type": "number", "format": "decimal"}, "ttf": {"type": "number", "format": "decimal"}, "tf": {"type": "number", "description": "Transaction fee amount", "format": "decimal"}, "d": {"type": "number", "description": "Discount", "format": "decimal"}, "fid": {"type": "string", "description": "Flight identifier", "nullable": true}, "fmt": {"type": "string", "nullable": true}, "nqs": {"type": "number", "description": "Negative quality score", "format": "decimal"}}}, "Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"required": ["key", "value"], "type": "object", "properties": {"key": {"type": "string", "description": "Item key", "nullable": true}, "type": {"type": "string", "description": "Item type", "nullable": true}, "value": {"type": "string", "description": "Item value", "nullable": true}}, "description": "Dictionary item"}, "System.Collections.Generic.KeyValuePair`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"key": {"type": "string", "nullable": true, "readOnly": true}, "value": {"type": "string", "nullable": true, "readOnly": true}}}, "Esky.FlightSearch.Contracts.CommonObjects.Price": {"type": "object", "properties": {"currencyCode": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "nullable": true}, "amount": {"type": "number", "description": "Amount", "format": "decimal"}}, "description": "Price object"}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SearchFiltersView": {"required": ["airlineCodes", "airlineNames", "airlineTypes", "airportsCityCodes", "airportsCountryCodes", "airportsNames", "arrivalCodes", "cityNames", "countryNames", "departureCodes", "providerCodes", "transferCodes"], "type": "object", "properties": {"departureCodes": {"type": "array", "items": {"type": "string"}, "description": "List of departure location codes", "nullable": true}, "arrivalCodes": {"type": "array", "items": {"type": "string"}, "description": "List of arrival location codes", "nullable": true}, "transferCodes": {"type": "array", "items": {"type": "string"}, "description": "List of transfer codes", "nullable": true}, "airportsNames": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of airport names", "nullable": true}, "airportsCityCodes": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of airport city mappings", "nullable": true}, "airportsCountryCodes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Collections.Generic.KeyValuePair`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of airport city mappings", "nullable": true}, "cityNames": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of city names", "nullable": true}, "countryNames": {"type": "array", "items": {"$ref": "#/components/schemas/System.Collections.Generic.KeyValuePair`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of country names", "nullable": true}, "airlineCodes": {"type": "array", "items": {"type": "string"}, "description": "List of airline codes", "nullable": true}, "airlineNames": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of airline names", "nullable": true}, "airlineTypes": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of airline types", "nullable": true}, "providerCodes": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of provider codes", "nullable": true}, "airlineAlliances": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "Obsolete - for backward compatibility only", "nullable": true}, "stopovers": {"type": "array", "items": {"type": "integer", "format": "int32"}, "description": "Stopovers", "nullable": true}, "airlinePopularity": {"type": "array", "items": {"type": "string"}, "description": "Obsolete - for backward compatibility only", "nullable": true, "deprecated": true}, "airportsWebNames": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "Obsolete - for backward compatibility only", "nullable": true, "deprecated": true}, "maxPromotedFlightsForHighlightCategory": {"type": "integer", "description": "Obsolete - for backward compatibility only", "format": "int32", "deprecated": true}, "maxPromotedFlightsForRaiseCategory": {"type": "integer", "description": "Obsolete - for backward compatibility only", "format": "int32", "deprecated": true}, "maxPromotedFlightsForTooltipCategory": {"type": "integer", "description": "Shortest flight time returned", "format": "int32", "deprecated": true}, "maximalFlightTime": {"type": "string", "description": "Longest flight time returned", "nullable": true, "deprecated": true}, "maximalPrice": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.Price"}, "minimalFlightTime": {"type": "string", "description": "Shortest flight time returned", "nullable": true, "deprecated": true}, "minimalPrice": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.Price"}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SpecialOfferFlightsFromCacheView+FlightsView": {"required": ["items", "searchFilters"], "type": "object", "properties": {"airTrafficRuleId": {"type": "integer", "description": "Obsolete - for backward compatibility only", "format": "int32", "deprecated": true}, "airTrafficRuleVersion": {"type": "string", "description": "Obsolete - for backward compatibility only", "nullable": true, "deprecated": true}, "moreResultsAvailable": {"type": "boolean", "description": "Obsolete - for backward compatibility only", "deprecated": true}, "pagingLocator": {"type": "string", "description": "Obsolete - for backward compatibility only", "nullable": true, "deprecated": true}, "flexDateCalendarPercentCoverage": {"type": "integer", "description": "Obsolete - for backward compatibility only", "format": "int32", "deprecated": true}, "sessionId": {"type": "string", "description": "Search session identifier", "nullable": true, "deprecated": true}, "alternativeItems": {"type": "array", "items": {"type": "string"}, "description": "Obsolete - for backward compatibility only", "nullable": true, "deprecated": true}, "isPricePerPax": {"type": "boolean", "description": "Obsolete - for backward compatibility only", "deprecated": true}, "priceType": {"type": "integer", "description": "Obsolete - for backward compatibility only", "format": "int32", "deprecated": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightItemWithLegGroups"}, "description": "List of grouped flight results", "nullable": true}, "searchFilters": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SearchFiltersView"}}, "description": "Flights data"}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SpecialOfferFlightsFromCacheView": {"required": ["flights"], "type": "object", "properties": {"airTrafficRuleId": {"type": "integer", "description": "Obsolete - for backward compatibility only", "format": "int32", "deprecated": true}, "airTrafficRuleVersion": {"type": "string", "description": "Obsolete - for backward compatibility only", "nullable": true, "deprecated": true}, "flights": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SpecialOfferFlightsFromCacheView+FlightsView"}}, "description": "Flights data container"}, "Esky.FlightSearch.Contracts.DomainEnums.ServiceClassEnum": {"enum": ["Any", "Economy", "First", "Business", "EconomyPremium"], "type": "string"}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SearchFlightsRequestLeg": {"type": "object", "properties": {"arrivalCityCode": {"type": "string", "nullable": true}, "departureCityCode": {"type": "string", "nullable": true}, "departureCode": {"type": "string", "nullable": true}, "departureDate": {"type": "string", "format": "date-time"}, "arrivalCode": {"type": "string", "nullable": true}, "flexDate": {"type": "integer", "format": "int32", "nullable": true}}}, "Esky.FlightSearch.Contracts.Queries.Legacy.SearchConfig": {"type": "object", "properties": {"searchId": {"type": "string", "nullable": true}, "portionId": {"type": "integer", "format": "int32"}}}, "Esky.FlightSearch.Contracts.Queries.Legacy.FlexConfig": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "range": {"type": "integer", "format": "int32"}, "limit": {"type": "integer", "format": "int32"}}}, "Esky.FlightSearch.Contracts.DomainEnums.RuntimeModeEnum": {"enum": ["<PERSON><PERSON><PERSON>", "Live", "Test"], "type": "string"}, "Esky.FlightSearch.Contracts.Queries.Legacy.SearchFlightsFromCacheQuery": {"required": ["arrivalCode", "currencyCode", "departureCode", "partnerCode"], "type": "object", "properties": {"airlines": {"type": "array", "items": {"type": "string"}, "nullable": true}, "directFlights": {"type": "boolean"}, "groupByPriceOnly": {"type": "boolean"}, "packageSearch": {"type": "boolean", "nullable": true}, "serviceClass": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.DomainEnums.ServiceClassEnum"}, "priceType": {"type": "integer", "format": "int32", "nullable": true}, "flexDate": {"type": "integer", "format": "int32", "nullable": true}, "pricePerPax": {"type": "boolean", "nullable": true}, "originUrl": {"type": "string", "nullable": true}, "sessionId": {"type": "string", "nullable": true}, "legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SearchFlightsRequestLeg"}, "nullable": true}, "alternativeCurrencyCode": {"type": "string", "nullable": true}, "languageCode": {"type": "string", "nullable": true}, "deepLink": {"type": "boolean"}, "offset": {"type": "integer", "format": "int32", "nullable": true}, "searchConfig": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.SearchConfig"}, "flex": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.FlexConfig"}, "runtimeMode": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.DomainEnums.RuntimeModeEnum"}, "specialOfferId": {"type": "integer", "description": "Id of eSky special offer to retrieve", "format": "int32", "nullable": true}, "departureCode": {"type": "string", "description": "Code of departure location\r\nCan be a 3-letter IATA airport code, city code, or an eSky multiport code", "nullable": true}, "arrivalCode": {"type": "string", "description": "Code of arrival location\r\nCan be a 3-letter IATA airport code, city code, or an eSky multiport code", "nullable": true}, "passengers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.ISearchFlightsRequestPassenger"}, "description": "Passenger configuration\r\nDefault is a single adult configuration", "nullable": true}, "airlineCodes": {"type": "array", "items": {"type": "string"}, "description": "filter by airlines codes", "nullable": true}, "partnerCode": {"type": "string", "description": "Requested partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Requested currency code", "nullable": true}}}, "Esky.FlightSearch.Contracts.Queries.Legacy.AggregatedDestinationPrices.GetAggregatedDestinationPricesQuery": {"required": ["currencyCode", "partnerCode"], "type": "object", "properties": {"airlineCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "departureDestinationSet": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DestinationSet"}, "arrivalDestinationSet": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DestinationSet"}, "departureMonthFrom": {"type": "string", "format": "date-time", "nullable": true}, "departureMonthTo": {"type": "string", "format": "date-time", "nullable": true}, "languageCode": {"type": "string", "nullable": true}, "lengthOfStayCategory": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.DomainEnums.LengthOfStayEnum"}, "aggregateToMultiport": {"type": "boolean"}, "showFlightDetails": {"type": "boolean"}, "maxResults": {"type": "integer", "format": "int32", "nullable": true}, "orderType": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Enums.OrderTypeEnum"}, "showDebugInfo": {"type": "boolean"}, "partnerCode": {"type": "string", "description": "Requested partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Requested currency code", "nullable": true}}}, "Esky.FlightSearch.Contracts.Queries.Legacy.DestinationTypeEnum": {"enum": ["Airport", "City", "Country", "Continent"], "type": "string"}, "Esky.FlightSearch.Contracts.Queries.Legacy.GetPriceAlertCalendarQuery": {"required": ["currencyCode", "partnerCode"], "type": "object", "properties": {"isRoundTrip": {"type": "boolean"}, "departureCode": {"type": "string", "nullable": true}, "departureDestinationType": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.DestinationTypeEnum"}, "arrivalCode": {"type": "string", "nullable": true}, "arrivalDestinationType": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.Legacy.DestinationTypeEnum"}, "departureYear": {"type": "integer", "format": "int32", "nullable": true}, "departureMonth": {"type": "integer", "format": "int32", "nullable": true}, "packageSearch": {"type": "boolean"}, "languageCode": {"type": "string", "nullable": true}, "deepLink": {"type": "boolean", "nullable": true}, "priceAmountLimit": {"type": "number", "format": "double", "nullable": true}, "selectedDepartureDay": {"type": "string", "format": "date-time", "nullable": true}, "selectedReturnDepartureMonth": {"type": "string", "format": "date-time", "nullable": true}, "maxFlightsCount": {"type": "integer", "format": "int32", "nullable": true}, "maxConnectionsCount": {"type": "integer", "format": "int32", "nullable": true}, "lengthOfStay": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.DomainEnums.LengthOfStayEnum"}, "showDebugInfo": {"type": "boolean"}, "partnerCode": {"type": "string", "description": "Requested partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Requested currency code", "nullable": true}}}, "Esky.FlightSearch.Contracts.Queries.Legacy.GetAtractiveAlternativeFlightsFromCacheQuery": {"required": ["arrivalCode", "currencyCode", "departureCode", "departureDate", "languageCode", "partnerCode", "price"], "type": "object", "properties": {"departureDate": {"type": "string", "description": "Departure date", "format": "date-time"}, "returnDepartureDate": {"type": "string", "description": "Departure date of a return flight (for roundtrip queries)", "format": "date-time", "nullable": true}, "departureCode": {"type": "string", "description": "Departure airport code", "nullable": true}, "arrivalCode": {"type": "string", "description": "Arrival airport code", "nullable": true}, "languageCode": {"type": "string", "description": "Two-letter ISO language code used for description translation", "nullable": true}, "price": {"type": "number", "description": "Highest allowed price amount", "format": "decimal"}, "currencyCode": {"type": "string", "description": "Currency code related to price amount", "nullable": true}, "partnerCode": {"type": "string", "description": "Partner code", "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsById+Query": {"type": "object", "properties": {"partnerCode": {"type": "string", "nullable": true}, "flightIds": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "includeNqs": {"type": "boolean"}, "includeLegLocators": {"type": "boolean"}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.PaxData": {"type": "object", "properties": {"totalPrice": {"type": "number", "format": "decimal"}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.FlightPrice": {"type": "object", "properties": {"currency": {"type": "string", "nullable": true}, "paxConfigurations": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.PaxData"}, "nullable": true}, "refreshDate": {"type": "string", "format": "date-time", "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsById+Response+Flight": {"type": "object", "properties": {"flightId": {"type": "string", "nullable": true}, "flightPrices": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightPrice"}, "legLocators": {"type": "array", "items": {"type": "string"}, "nullable": true}, "nqs": {"type": "number", "format": "double", "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsById+Response": {"type": "object", "properties": {"flights": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsById+Response+Flight"}, "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.GetFlightsByFlightOfferKeyQuery": {"type": "object", "properties": {"partnerCode": {"type": "string", "nullable": true}, "flightOfferKeys": {"type": "array", "items": {"type": "string"}, "nullable": true}, "includeNqs": {"type": "boolean"}, "includeLegLocators": {"type": "boolean"}, "includeFlightDetails": {"type": "boolean"}, "passengers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SearchFlightsRequestPassenger"}, "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.FlightOffersForPackagesView+Flight": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "flightPrices": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightPrice"}, "nullable": true}, "departureAirport": {"type": "string", "nullable": true}, "arrivalAirport": {"type": "string", "nullable": true}, "departureDate": {"type": "string", "format": "date-time"}, "arrivalDate": {"type": "string", "format": "date-time"}, "flightDuration": {"type": "string", "nullable": true}, "returnDepartureDate": {"type": "string", "format": "date-time"}, "returnArrivalDate": {"type": "string", "format": "date-time"}, "returnFlightDuration": {"type": "string", "nullable": true}, "legLocators": {"type": "array", "items": {"type": "string"}, "nullable": true}, "flightOfferKey": {"type": "string", "nullable": true}, "providerCode": {"type": "integer", "format": "int32"}, "stops": {"type": "integer", "format": "int32"}, "baggageIncluded": {"type": "boolean"}, "nqs": {"type": "number", "format": "decimal"}, "airlineCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.FlightOffersForPackagesView": {"type": "object", "properties": {"flights": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightOffersForPackagesView+Flight"}, "nullable": true}}}, "Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForPackagesQuery+Route": {"type": "object", "properties": {"departureAirport": {"type": "string", "nullable": true}, "arrivalAirport": {"type": "string", "nullable": true}}}, "Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForPackagesQuery+GroupingMode": {"enum": ["Route", "DepartureDate", "ReturnDate"], "type": "string"}, "Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForPackagesQuery": {"type": "object", "properties": {"partnerCode": {"type": "string", "nullable": true}, "currencyCode": {"type": "string", "nullable": true}, "passengers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.ISearchFlightsRequestPassenger"}, "description": "Passenger configuration\r\nDefault is a single adult configuration", "nullable": true}, "destinations": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForPackagesQuery+Route"}, "nullable": true}, "minDepartureDate": {"type": "string", "format": "date-time"}, "maxDepartureDate": {"type": "string", "description": "Either MaxReturnDate or MaxDepartureDate should be given", "format": "date-time", "nullable": true}, "maxReturnDate": {"type": "string", "description": "Either MaxReturnDate or MaxDepartureDate should be given", "format": "date-time", "nullable": true}, "minStayLength": {"type": "integer", "format": "int32"}, "maxStayLength": {"type": "integer", "format": "int32"}, "limit": {"type": "integer", "format": "int32", "nullable": true}, "departureWeekDays": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "returnWeekDays": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "airlineCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "groupBy": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForPackagesQuery+GroupingMode"}, "nullable": true}, "maxStops": {"type": "integer", "format": "int32", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.OfferType": {"enum": ["Regular", "TourOperator"], "type": "string"}, "Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForPackagesView+Flight": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "flightIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "flightOfferKey": {"type": "string", "nullable": true}, "flightPrices": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightPrice"}, "nullable": true}, "departureAirport": {"type": "string", "nullable": true}, "arrivalAirport": {"type": "string", "nullable": true}, "departureDate": {"type": "string", "format": "date-time"}, "arrivalDate": {"type": "string", "format": "date-time"}, "flightDuration": {"type": "string", "nullable": true}, "returnDepartureDate": {"type": "string", "format": "date-time"}, "returnArrivalDate": {"type": "string", "format": "date-time"}, "returnFlightDuration": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "decimal"}, "transactionFee": {"type": "number", "format": "decimal"}, "currencyCode": {"type": "string", "nullable": true}, "nqs": {"type": "number", "format": "decimal"}, "legLocators": {"type": "array", "items": {"type": "string"}, "nullable": true}, "providerCode": {"type": "integer", "format": "int32"}, "stops": {"type": "integer", "format": "int32"}, "baggageIncluded": {"type": "boolean"}, "offerType": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.OfferType"}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForPackagesView": {"type": "object", "properties": {"flights": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForPackagesView+Flight"}, "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.FlightOffersForPackagesQuery+Route": {"type": "object", "properties": {"departureAirport": {"type": "string", "nullable": true}, "arrivalAirport": {"type": "string", "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.SearchStrategies.TripLengthDuration": {"type": "object", "properties": {"minDepartureDate": {"type": "string", "format": "date"}, "maxDepartureDate": {"type": "string", "format": "date", "nullable": true}, "minReturnArrivalDate": {"type": "string", "format": "date", "nullable": true}, "maxReturnArrivalDate": {"type": "string", "format": "date"}, "tripLengthInDays": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "earlyDepartureInHours": {"type": "integer", "format": "int32"}, "lateReturnArrivalInHours": {"type": "integer", "format": "int32"}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.SearchStrategies.DaysAtDestinationDuration": {"type": "object", "properties": {"minArrivalDate": {"type": "string", "format": "date"}, "maxArrivalDate": {"type": "string", "format": "date", "nullable": true}, "minReturnDepartureDate": {"type": "string", "format": "date", "nullable": true}, "maxReturnDepartureDate": {"type": "string", "format": "date"}, "daysAtDestination": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.FlightOffersForPackagesQuery": {"type": "object", "properties": {"partnerCode": {"type": "string", "nullable": true}, "destinations": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.FlightOffersForPackagesQuery+Route"}, "nullable": true}, "maxStops": {"type": "integer", "format": "int32", "nullable": true}, "limitPerDate": {"type": "integer", "format": "int32", "nullable": true}, "maxLegDurationInMinutes": {"type": "integer", "format": "int32", "nullable": true}, "tripLengthDuration": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.SearchStrategies.TripLengthDuration"}, "daysAtDestinationDuration": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.SearchStrategies.DaysAtDestinationDuration"}, "departureWeekDays": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "includeWeekDays": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "airlineCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "includeNqs": {"type": "boolean"}, "includeLegLocators": {"type": "boolean"}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.FlightOffersQuery+Route": {"type": "object", "properties": {"departureAirport": {"type": "string", "nullable": true}, "arrivalAirport": {"type": "string", "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.FlightOffersQuery": {"type": "object", "properties": {"partnerCode": {"type": "string", "nullable": true}, "destinations": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.FlightOffersQuery+Route"}, "nullable": true}, "minCheckIn": {"type": "string", "format": "date"}, "maxCheckIn": {"type": "string", "format": "date"}, "stayLengths": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "maxStops": {"type": "integer", "format": "int32", "nullable": true}, "limitPerGroup": {"type": "integer", "format": "int32"}, "maxLegDurationInMinutes": {"type": "integer", "format": "int32", "nullable": true}, "departureWeekDays": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "airlineCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "includeNqs": {"type": "boolean"}, "includeLegLocators": {"type": "boolean"}, "currencyCode": {"type": "string", "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.AlternativeFlights+Query": {"type": "object", "properties": {"departureAirports": {"type": "array", "items": {"type": "string"}, "nullable": true}, "arrivalAirports": {"type": "array", "items": {"type": "string"}, "nullable": true}, "arrivalDate": {"type": "string", "format": "date-time"}, "returnDate": {"type": "string", "format": "date-time"}, "maxLegDurationInMinutes": {"type": "integer", "format": "int32", "nullable": true}, "maxStops": {"type": "integer", "format": "int32", "nullable": true}, "partnerCode": {"type": "string", "nullable": true}, "passengers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.ISearchFlightsRequestPassenger"}, "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.AlternativeOffersQuery": {"type": "object", "properties": {"departureAirports": {"type": "array", "items": {"type": "string"}, "nullable": true}, "arrivalAirports": {"type": "array", "items": {"type": "string"}, "nullable": true}, "checkIn": {"type": "string", "format": "date"}, "stayLength": {"type": "integer", "format": "int32"}, "maxLegDurationInMinutes": {"type": "integer", "format": "int32", "nullable": true}, "maxStops": {"type": "integer", "format": "int32", "nullable": true}, "partnerCode": {"type": "string", "nullable": true}, "passengers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SearchFlightsRequestPassenger"}, "nullable": true}, "includeLegLocators": {"type": "boolean", "nullable": true}, "limit": {"type": "integer", "format": "int32", "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.DynamicPackages+Request+Route": {"type": "object", "properties": {"departure": {"type": "string", "nullable": true}, "arrival": {"type": "string", "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.DynamicPackages+Request": {"type": "object", "properties": {"partnerCode": {"type": "string", "nullable": true}, "minCheckIn": {"type": "string", "format": "date"}, "maxCheckIn": {"type": "string", "format": "date"}, "routes": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.DynamicPackages+Request+Route"}, "nullable": true}, "stayLengths": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "maxStops": {"type": "integer", "format": "int32"}, "limitPerArrivalAirport": {"type": "integer", "format": "int32"}, "passengers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SearchFlightsRequestPassenger"}, "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.DynamicPackages+Response+Price": {"type": "object", "properties": {"totalPrice": {"type": "number", "format": "decimal"}, "currency": {"type": "string", "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.DynamicPackages+Response+Flight": {"type": "object", "properties": {"flightOfferKey": {"type": "string", "nullable": true}, "departureAirport": {"type": "string", "nullable": true}, "arrivalAirport": {"type": "string", "nullable": true}, "departureDate": {"type": "string", "format": "date-time"}, "arrivalDate": {"type": "string", "format": "date-time"}, "returnDepartureDate": {"type": "string", "format": "date-time"}, "returnArrivalDate": {"type": "string", "format": "date-time"}, "providerCode": {"type": "integer", "format": "int32"}, "stops": {"type": "integer", "format": "int32"}, "baggageIncluded": {"type": "boolean"}, "flightPrices": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.DynamicPackages+Response+Price"}, "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.DynamicPackages+Response": {"type": "object", "properties": {"flights": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.FlightsCache.FlightsForPackages.v2.DynamicPackages+Response+Flight"}, "nullable": true}}}, "Esky.FlightSearch.Contracts.Queries.FlightCache.FlightsForTravelExplorerQuery": {"type": "object", "properties": {"keys": {"type": "array", "items": {"type": "string"}, "nullable": true}, "partnerCode": {"type": "string", "nullable": true}, "currencyCode": {"type": "string", "nullable": true}, "languageCode": {"type": "string", "nullable": true}, "limit": {"type": "integer", "format": "int32"}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForTravelExplorerView+Segment": {"type": "object", "properties": {"airlineCode": {"type": "string", "nullable": true}, "departureAirport": {"type": "string", "nullable": true}, "departureDate": {"type": "string", "format": "date-time"}, "arrivalAirport": {"type": "string", "nullable": true}, "arrivalDate": {"type": "string", "format": "date-time"}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForTravelExplorerView+Leg": {"type": "object", "properties": {"segments": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForTravelExplorerView+Segment"}, "nullable": true}, "flightDuration": {"type": "string", "nullable": true}, "legLocator": {"type": "string", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForTravelExplorerView+Flight": {"type": "object", "properties": {"flightOfferKey": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "decimal"}, "transactionFee": {"type": "number", "format": "decimal"}, "currencyCode": {"type": "string", "nullable": true}, "legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForTravelExplorerView+Leg"}, "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForTravelExplorerView": {"type": "object", "properties": {"flights": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Flights.FlightsForTravelExplorerView+Flight"}, "nullable": true}, "airlineNames": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}}}, "Esky.FlightSearch.FlightsCache.FlightsForTravelExplorer.GetByFlightOfferKey+Query": {"type": "object", "properties": {"flightOfferKey": {"type": "string", "nullable": true}, "partnerCode": {"type": "string", "nullable": true}, "paxConfiguration": {"type": "string", "nullable": true}}}}}}