using Esky.Packages.Application.Abstractions.Gateways.HotelsApiGateway;
using Esky.Packages.Application.Dtos.HotelOffers;
using Esky.Packages.Domain.Types.Extensions;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Infrastructure.Gateways.HotelsApiGateway.HotelsApi;

namespace Esky.Packages.Infrastructure.Gateways.HotelApiGateway;

public class HotelApiGateway : IHotelOfferLiveGateway
{
    private readonly HotelsApi _client;

    public HotelApiGateway(IHttpClientFactory httpClientFactory)
    {
        _client = new HotelsApi(httpClientFactory.CreateClient(ApiHttpClientConsts.HotelApiClient));
    }

    public async Task<IEnumerable<HotelOfferDto>> Search(HotelSearchCriteria criteria, CancellationToken cancellationToken)
    {
        var apiParameters = new HotelSearchParameters
        {
            HotelMetaCodes = criteria.MetaCodes,
            SearchContext = new SearchContext
            {
                StayInformation = new StayInformation
                {
                    CheckInDate = criteria.CheckIn.ToDateTime(TimeOnly.MinValue),
                    CheckOutDate = criteria.CheckOut.ToDateTime(TimeOnly.MinValue),
                    Nights = (criteria.CheckOut.ToDateTime(TimeOnly.MinValue) - criteria.CheckIn.ToDateTime(TimeOnly.MinValue)).Days
                },
                SettingsKey = new SettingsKey
                {
                    PartnerCode = criteria.PartnerCode
                },
                RoomsConfiguration = criteria.Occupancies.Select(rc => new RoomConfiguration
                {
                    Adults = rc.Adults,
                    ChildrenAges = rc.ChildrenAges
                }).ToList()
            },
            Currency = "EUR",
            Language = "en",
        };

        var result = await _client.SearchAsync(apiParameters, cancellationToken);

        // TODO: Handle multiple room configurations
        var occupancy = criteria.Occupancies.Merge();

        return result.Hotels
            .SelectMany(h => h.RoomPackages
                .Select(r =>
                {
                    // offerId example 148007:Breakfast:210825:280825:{a4c}:SUI.B2|NRF|BB|0|4|:ESKYPLPACKAGES:36:epa:{MiBCRURST09NIFdJVEggT1VURE9PUiBKQUNVWlpJ}:AtCheckout:
                    var offerIdParts = r.OfferId.Split(':');

                    var providerCode = int.Parse(offerIdParts[7]);
                    var configurationId = offerIdParts[8];
                    var providerConfigurationId = $"{providerCode}|{configurationId}";

                    return new RoomOffer(
                        MetaCode: h.MetaCode,
                        CheckIn: criteria.CheckIn,
                        StayLength: criteria.CheckOut.DayNumber - criteria.CheckIn.DayNumber,
                        MealPlan: Map(r.MealPlan),
                        Occupancy: new HotelOfferOccupancy(occupancy.Adults, occupancy.ChildrenAges),
                        Price: new HotelOfferDto.HotelOfferMoneyDto
                        {
                            Value = r.PriceDetails.ProviderNetPrice.Amount,
                            Currency = r.PriceDetails.ProviderNetPrice.Currency
                        },
                        Refundability: r.CancellationInfo.CancellationDetails.Any(d => d.Price.Amount == 0)
                            ? HotelOfferDto.HotelOfferRefundabilityDto.Refundable
                            : HotelOfferDto.HotelOfferRefundabilityDto.NonRefundable,
                        ProviderConfigurationId: providerConfigurationId);
                }))
            .GroupBy(r => r.MetaCode)
            .Select(g => new HotelOfferDto
            {
                // example: "id": "250822:7:472189:123|packages:A2",
                Id = $"{g.First().CheckIn.ToString("yyMMdd")}:{g.First().StayLength}:{g.Key}:{g.First().ProviderConfigurationId}:{g.First().Occupancy}",
                CheckIn = criteria.CheckIn,
                StayLength = criteria.CheckOut.DayNumber - criteria.CheckIn.DayNumber,
                MetaCode = g.Key,
                ProviderConfigurationId = g.First().ProviderConfigurationId,
                Occupancy = g.First().Occupancy,
                Prices = g
                    .GroupBy(g1 => g1.MealPlan)
                    .ToDictionary(g1 => g1.Key, g1 =>
                        g1
                            .GroupBy(g2 => g2.Refundability)
                            .ToDictionary(g2 => g2.Key, g2 =>
                                new HotelOfferDto.HotelOfferMoneyDto
                                {
                                    Value = g2.MinBy(g3 => g3.Price.Value)!.Price.Value,
                                    Currency = g2.MinBy(g3 => g3.Price.Value)!.Price.Currency
                                }))
            });
    }
    
    private static HotelOfferDto.HotelOfferMealPlanDto Map(MealPlan mealPlanDto)
    {
        return mealPlanDto switch
        {
            MealPlan.None => HotelOfferDto.HotelOfferMealPlanDto.None,
            MealPlan.Breakfast => HotelOfferDto.HotelOfferMealPlanDto.Breakfast,
            MealPlan.HalfBoard => HotelOfferDto.HotelOfferMealPlanDto.HalfBoard,
            MealPlan.FullBoard => HotelOfferDto.HotelOfferMealPlanDto.FullBoard,
            MealPlan.AllInclusive => HotelOfferDto.HotelOfferMealPlanDto.AllInclusive,
            _ => HotelOfferDto.HotelOfferMealPlanDto.None
        };
    }

    private record RoomOffer(
        int MetaCode,
        DateOnly CheckIn,
        int StayLength,
        string ProviderConfigurationId,
        HotelOfferOccupancy Occupancy,
        HotelOfferDto.HotelOfferMealPlanDto MealPlan,
        HotelOfferDto.HotelOfferRefundabilityDto Refundability,
        HotelOfferDto.HotelOfferMoneyDto Price);
}
