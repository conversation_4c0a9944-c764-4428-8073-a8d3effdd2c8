{"openapi": "3.0.4", "info": {"title": "HApi", "description": "API fot Hotels", "version": "1.0"}, "paths": {"/hapi/Search": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.HotelSearchParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.HotelSearchParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.HotelSearchParameters"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.HotelsSearch.HotelSearchResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.HotelsSearch.HotelSearchResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.HotelsSearch.HotelSearchResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}}}}}}, "/hapi/Provider/{providerCode}/Search": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "providerCode", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Contract.Common.ProviderCode"}}, {"name": "includeResponseDetails", "in": "query", "schema": {"type": "boolean", "default": true}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.ProviderSearchParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.ProviderSearchParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.ProviderSearchParameters"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.HotelsSearch.HotelSearchResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.HotelsSearch.HotelSearchResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.HotelsSearch.HotelSearchResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}}}}}}, "/hapi/OccupancyPricing": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.OccupancyPricingAvailabilityParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.OccupancyPricingAvailabilityParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.OccupancyPricingAvailabilityParameters"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.OccupancyPricingAvailabilityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.OccupancyPricingAvailabilityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.OccupancyPricingAvailabilityResult"}}}}}}}, "/hapi/Variants": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.HotelVariantsParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.HotelVariantsParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.HotelVariantsParameters"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.HotelVariants.HotelVariantsResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.HotelVariants.HotelVariantsResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.HotelVariants.HotelVariantsResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}}}}}}, "/hapi/Offers": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.OffersParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.OffersParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.OffersParameters"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.Offers.OffersResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.Offers.OffersResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.Offers.OffersResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}}}}}}, "/hapi/hotels/details": {"post": {"tags": ["Hotels"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.HotelDetailsParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.HotelDetailsParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApiContract.Request.HotelDetailsParameters"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.HotelDetails.HotelDetailsResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.HotelDetails.HotelDetailsResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiContract.Response.HotelDetails.HotelDetailsResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/pNetCore.Mvc.ProblemDetails"}}}}}}}}, "components": {"schemas": {"ApiContract.Request.HotelDetailsParameters": {"type": "object", "properties": {"partnerCode": {"type": "string", "nullable": true}, "hotelMetaCode": {"type": "integer", "format": "int32"}, "language": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiContract.Request.HotelSearchParameters": {"type": "object", "properties": {"hotelMetaCodes": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "searchContext": {"$ref": "#/components/schemas/ApiContract.SearchContext"}, "currency": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiContract.Request.HotelVariantsParameters": {"type": "object", "properties": {"hotelMetaCode": {"type": "integer", "format": "int32"}, "searchContext": {"$ref": "#/components/schemas/ApiContract.SearchContext"}, "currency": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiContract.Request.OccupancyPricingAvailabilityParameters": {"type": "object", "properties": {"partnerCode": {"type": "string", "nullable": true}, "hotelMetaCodes": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "stayInformation": {"$ref": "#/components/schemas/Contract.Common.StayInformation"}, "occupancies": {"type": "array", "items": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.RoomConfiguration"}}, "nullable": true}}, "additionalProperties": false}, "ApiContract.Request.OffersParameters": {"type": "object", "properties": {"offerIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "language": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiContract.Request.ProviderSearchParameters": {"type": "object", "properties": {"hotelMetaCodes": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "partnerCode": {"type": "string", "nullable": true}, "stayInformation": {"$ref": "#/components/schemas/Contract.Common.StayInformation"}, "occupancy": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.RoomConfiguration"}, "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.BedConfiguration": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "bedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.BedType"}, "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.BedType": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.CancellationInfo": {"type": "object", "properties": {"cancellationDetails": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.CancellationInfoDetails"}, "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.CancellationInfoDetails": {"type": "object", "properties": {"until": {"type": "string", "format": "date"}, "price": {"$ref": "#/components/schemas/Contract.Common.Price"}}, "additionalProperties": false}, "ApiContract.Response.Context": {"type": "object", "properties": {"partnerCode": {"type": "string", "nullable": true}, "contextsApplied": {"type": "array", "items": {"$ref": "#/components/schemas/Contract.Common.Context"}, "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.HotelDetails.HotelDetailsResult": {"type": "object", "properties": {"providerHotels": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.HotelDetails.ProviderHotel"}, "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.HotelDetails.ProviderHotel": {"type": "object", "properties": {"providerCode": {"$ref": "#/components/schemas/Contract.Common.ProviderCode"}, "hotelName": {"type": "string", "nullable": true}, "rooms": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.HotelDetails.ProviderRoom"}, "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.HotelDetails.ProviderRoom": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "giataRoomName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "roomType": {"type": "string", "nullable": true}, "roomClass": {"type": "string", "nullable": true}, "photos": {"type": "array", "items": {"type": "object", "properties": {"Thumbnail": {"$ref": "#/components/schemas/Contract.Common.Photo"}, "Medium": {"$ref": "#/components/schemas/Contract.Common.Photo"}, "Large": {"$ref": "#/components/schemas/Contract.Common.Photo"}}, "additionalProperties": false}, "nullable": true}, "facilities": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bedConfigurations": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.BedConfiguration"}, "nullable": true}, "areaInSqMeters": {"type": "integer", "format": "int32", "nullable": true}, "areaInSqFeets": {"type": "integer", "format": "int32", "nullable": true}, "maxAdults": {"type": "integer", "format": "int32", "nullable": true}, "maxChildren": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.HotelOccupancyPricing": {"type": "object", "properties": {"hotelMetaCode": {"type": "integer", "format": "int32"}, "offerPricings": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.OfferOccupancyPricing"}, "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.HotelVariants.HotelVariantsResult": {"type": "object", "properties": {"hotelMetaCode": {"type": "integer", "format": "int32"}, "offers": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.HotelVariants.Variant"}, "nullable": true}, "context": {"$ref": "#/components/schemas/ApiContract.Response.Context"}}, "additionalProperties": false}, "ApiContract.Response.HotelVariants.Room": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "bedConfigurations": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.BedConfiguration"}, "nullable": true}, "roomConfiguration": {"$ref": "#/components/schemas/ApiContract.RoomConfiguration"}, "photos": {"type": "array", "items": {"type": "object", "properties": {"Thumbnail": {"$ref": "#/components/schemas/Contract.Common.Photo"}, "Medium": {"$ref": "#/components/schemas/Contract.Common.Photo"}, "Large": {"$ref": "#/components/schemas/Contract.Common.Photo"}}, "additionalProperties": false}, "nullable": true}, "facilities": {"type": "array", "items": {"type": "string"}, "nullable": true}, "areaInSqMeters": {"type": "integer", "format": "int32", "nullable": true}, "areaInSqFeets": {"type": "integer", "format": "int32", "nullable": true}, "maxAdults": {"type": "integer", "format": "int32", "nullable": true}, "maxChildren": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.HotelVariants.Variant": {"type": "object", "properties": {"offerId": {"type": "string", "nullable": true}, "packageId": {"type": "string", "nullable": true}, "hotelPricingLocator": {"type": "string", "nullable": true}, "qualifiedHotelCode": {"type": "integer", "format": "int32"}, "offerPrice": {"$ref": "#/components/schemas/Contract.Common.Price"}, "priceDetails": {"$ref": "#/components/schemas/ApiContract.Response.PriceDetails"}, "rooms": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.HotelVariants.Room"}, "nullable": true}, "providerCode": {"type": "integer", "format": "int32"}, "mealPlan": {"$ref": "#/components/schemas/Contract.Common.MealPlan"}, "paymentType": {"$ref": "#/components/schemas/ApiContract.Response.PaymentType"}, "cancellationInfo": {"$ref": "#/components/schemas/ApiContract.Response.CancellationInfo"}, "policies": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.Information"}, "nullable": true}, "importantInformations": {"type": "string", "nullable": true}, "receivedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ApiContract.Response.HotelsSearch.Hotel": {"type": "object", "properties": {"metaCode": {"type": "integer", "format": "int32"}, "qualifiedCode": {"type": "integer", "format": "int32"}, "roomPackages": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.HotelsSearch.RoomPackage"}, "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.HotelsSearch.HotelSearchResult": {"type": "object", "properties": {"currency": {"type": "string", "nullable": true}, "hotels": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.HotelsSearch.Hotel"}, "nullable": true}, "context": {"$ref": "#/components/schemas/ApiContract.Response.Context"}}, "additionalProperties": false}, "ApiContract.Response.HotelsSearch.Room": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.HotelsSearch.RoomPackage": {"type": "object", "properties": {"offerId": {"type": "string", "nullable": true}, "providerOfferGroup": {"type": "string", "nullable": true}, "rooms": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.HotelsSearch.Room"}, "nullable": true}, "price": {"$ref": "#/components/schemas/Contract.Common.Price"}, "priceDetails": {"$ref": "#/components/schemas/ApiContract.Response.PriceDetails"}, "paymentType": {"$ref": "#/components/schemas/ApiContract.Response.PaymentType"}, "mealPlan": {"$ref": "#/components/schemas/Contract.Common.MealPlan"}, "cancellationInfo": {"$ref": "#/components/schemas/ApiContract.Response.CancellationInfo"}, "receivedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ApiContract.Response.Information": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.OccupancyPricingAvailabilityResult": {"type": "object", "properties": {"hotels": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.HotelOccupancyPricing"}, "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.OfferOccupancyPricing": {"type": "object", "properties": {"providerRoomIds": {"type": "string", "nullable": true}, "providerCode": {"$ref": "#/components/schemas/Contract.Common.ProviderCode"}, "offerOccupancyGroupId": {"type": "string", "nullable": true}, "mealPlan": {"$ref": "#/components/schemas/Contract.Common.MealPlan"}, "paymentType": {"$ref": "#/components/schemas/ApiContract.Response.PaymentType"}, "refundability": {"$ref": "#/components/schemas/ApiContract.Response.Refundability"}, "occupancyPricing": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ApiContract.Response.Pricing"}, "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.Offers.Offer": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "metaCode": {"type": "integer", "format": "int32"}, "mealPlan": {"$ref": "#/components/schemas/Contract.Common.MealPlan"}, "stayInformation": {"$ref": "#/components/schemas/Contract.Common.StayInformation"}, "rooms": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.Offers.OfferRoom"}, "nullable": true}, "providerCode": {"type": "integer", "format": "int32", "deprecated": true}, "bookingLocator": {"type": "string", "nullable": true, "deprecated": true}}, "additionalProperties": false}, "ApiContract.Response.Offers.OfferRoom": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ApiContract.Response.Offers.OffersResult": {"type": "object", "properties": {"offers": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.Offers.Offer"}, "nullable": true}, "rooms": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.Offers.Room"}, "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.Offers.Room": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "roomType": {"type": "string", "nullable": true}, "roomClass": {"type": "string", "nullable": true}, "photos": {"type": "array", "items": {"type": "object", "properties": {"Thumbnail": {"$ref": "#/components/schemas/Contract.Common.Photo"}, "Medium": {"$ref": "#/components/schemas/Contract.Common.Photo"}, "Large": {"$ref": "#/components/schemas/Contract.Common.Photo"}}, "additionalProperties": false}, "nullable": true}, "facilities": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bedConfigurations": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.Response.BedConfiguration"}, "nullable": true}, "areaInSqMeters": {"type": "integer", "format": "int32", "nullable": true}, "areaInSqFeets": {"type": "integer", "format": "int32", "nullable": true}, "maxAdults": {"type": "integer", "format": "int32", "nullable": true}, "maxChildren": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "ApiContract.Response.PaymentType": {"enum": ["Unknown", "OnSite", "AtCheckout", "Installments"], "type": "string"}, "ApiContract.Response.PriceDetails": {"type": "object", "properties": {"currency": {"type": "string", "nullable": true}, "netPrice": {"type": "number", "format": "decimal"}, "taxPrice": {"type": "number", "format": "decimal"}, "extraCharges": {"type": "number", "format": "decimal"}, "providerNetPrice": {"$ref": "#/components/schemas/Contract.Common.Price"}}, "additionalProperties": false}, "ApiContract.Response.Pricing": {"type": "object", "properties": {"offerId": {"type": "string", "nullable": true}, "providerOfferGroup": {"type": "string", "nullable": true}, "providerNetPrice": {"$ref": "#/components/schemas/Contract.Common.Price"}, "receivedAt": {"type": "string", "format": "date-time"}, "availability": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ApiContract.Response.Refundability": {"type": "object", "properties": {"isRefundable": {"type": "boolean"}, "freeRefundUntil": {"type": "string", "format": "date", "nullable": true}}, "additionalProperties": false}, "ApiContract.RoomConfiguration": {"type": "object", "properties": {"adults": {"type": "integer", "format": "int32"}, "childrenAges": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "ApiContract.SearchContext": {"type": "object", "properties": {"stayInformation": {"$ref": "#/components/schemas/Contract.Common.StayInformation"}, "settingsKey": {"$ref": "#/components/schemas/ApiContract.SettingsKey"}, "roomsConfiguration": {"type": "array", "items": {"$ref": "#/components/schemas/ApiContract.RoomConfiguration"}, "nullable": true}}, "additionalProperties": false}, "ApiContract.SettingsKey": {"type": "object", "properties": {"partnerCode": {"type": "string", "nullable": true}, "flightBookingId": {"type": "string", "nullable": true}, "mobileDevice": {"type": "boolean", "nullable": true}, "userId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Contract.Common.Context": {"enum": ["CROSSSELLING", "MOBILE", "MEMBER"], "type": "string"}, "Contract.Common.MealPlan": {"enum": ["Unknown", "None", "Breakfast", "HalfBoard", "FullBoard", "AllInclusive"], "type": "string"}, "Contract.Common.Photo": {"type": "object", "properties": {"src": {"type": "string", "nullable": true}, "width": {"type": "integer", "format": "int32"}, "height": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Contract.Common.Price": {"type": "object", "properties": {"amount": {"type": "number", "format": "decimal"}, "currency": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Contract.Common.ProviderCode": {"enum": ["BookingCom", "Expedia", "HotelBeds", "Travolutionary", "TestHouseExpress", "<PERSON><PERSON><PERSON>", "RateHawk", "WebBeds", "OTS", "Alturabeds", "Yalago", "TravelgateDodo"], "type": "string"}, "Contract.Common.StayInformation": {"type": "object", "properties": {"checkInDate": {"type": "string", "format": "date-time"}, "checkOutDate": {"type": "string", "format": "date-time"}, "nights": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "pNetCore.Mvc.ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}}}}