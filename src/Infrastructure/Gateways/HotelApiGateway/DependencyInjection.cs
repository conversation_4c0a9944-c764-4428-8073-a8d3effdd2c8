using System.Net;
using Esky.Packages.Application.Abstractions.Gateways.HotelsApiGateway;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Esky.Packages.Infrastructure.HttpClients.Extensions;
using Esky.Packages.Infrastructure.HttpClients.Options;
using Esky.Packages.Infrastructure.Observability;
using Microsoft.Extensions.DependencyInjection;
using Polly;

namespace Esky.Packages.Infrastructure.Gateways.HotelsApiGateway;

internal static class DependencyInjection
{
    public static IServiceCollection AddHotelsLiveSearchGateway(this IServiceCollection services, bool enableLoggingHandlers = false)
    {
        var builder = services.AddHttpClient(ApiHttpClientConsts.HotelApiClient,
                (sp, client) =>
                {
                    client.BaseAddress = new Uri(sp.GetRequiredService<ApiUrlsOptions>().HotelApi);
                })
            .AddCompression();
            
        if (enableLoggingHandlers)
        {
            builder.WithLoggingHandler();
        }

        builder.AddResilienceHandler("hotelsApi", (b, _) =>
            b.AddRetry(new()
            {
                ShouldHandle = static args => ValueTask.FromResult(args.Outcome.Result?.StatusCode is
                        HttpStatusCode.BadGateway
                        or HttpStatusCode.ServiceUnavailable
                        or HttpStatusCode.GatewayTimeout
                        or HttpStatusCode.InternalServerError
                ),
                BackoffType = DelayBackoffType.Exponential,
                UseJitter = true,
                MaxRetryAttempts = 3,
                MaxDelay = TimeSpan.FromSeconds(30),
                Delay = TimeSpan.FromMilliseconds(1000),
            })
        );

        services.AddSingleton<IHotelOfferLiveGateway, HotelApiGateway.HotelApiGateway>();

        return services;
    }
}
