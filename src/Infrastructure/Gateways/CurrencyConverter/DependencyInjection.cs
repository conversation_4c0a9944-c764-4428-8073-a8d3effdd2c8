using System.Net;
using Esky.Packages.Application.Abstractions.Gateways.CurrencyConverter;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Esky.Packages.Infrastructure.HttpClients.Extensions;
using Esky.Packages.Infrastructure.HttpClients.Options;
using Esky.Packages.Infrastructure.Observability;
using Microsoft.Extensions.DependencyInjection;
using Polly;

namespace Esky.Packages.Infrastructure.Gateways.CurrencyConverter;

internal static class DependencyInjection
{
    public static IServiceCollection AddCurrencyConverterGateway(this IServiceCollection services, bool enableLoggingHandlers = false)
    {
        services.AddSingleton<ICurrencyConverterGateway, CurrencyConverterGateway>();
        
        var builder = services.AddHttpClient(ApiHttpClientConsts.CurrencyConverterClient, (sp, client) =>
            {
                client.BaseAddress = new Uri(sp.GetRequiredService<ApiUrlsOptions>().CurrencyConverter);
                client.Timeout = TimeSpan.FromSeconds(30);
            })
            .AddCompression();
            
        if (enableLoggingHandlers)
        {
            builder.WithLoggingHandler();
        }
        
        builder.AddResilienceHandler("currencyConverter", (b, _) =>
            b.AddRetry(new()
            {
                ShouldHandle = static args => ValueTask.FromResult(args.Outcome.Result?.StatusCode is
                    HttpStatusCode.BadGateway
                    or HttpStatusCode.ServiceUnavailable
                    or HttpStatusCode.GatewayTimeout
                    or HttpStatusCode.InternalServerError
                ),
                BackoffType = DelayBackoffType.Exponential,
                UseJitter = true,
                MaxRetryAttempts = 5,
                MaxDelay = TimeSpan.FromSeconds(30),
                Delay = TimeSpan.FromMilliseconds(1000),
            })
        );
        
        return services;
    }
}