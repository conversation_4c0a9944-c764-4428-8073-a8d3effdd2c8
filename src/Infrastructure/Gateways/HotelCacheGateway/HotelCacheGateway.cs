using Esky.Packages.Application.Abstractions.Gateways.HotelOfferGateway;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Infrastructure.Gateways.HotelCacheGateway.Client.HotelCache;
using HotelOfferDto = Esky.Packages.Application.Dtos.HotelOffers.HotelOfferDto;

namespace Esky.Packages.Infrastructure.Gateways.HotelCacheGateway;

public class HotelCacheGateway : IHotelOfferGateway
{
    private readonly HotelCacheClient _client;

    public HotelCacheGateway(IHttpClientFactory httpClientFactory)
    {
        var httpClient = httpClientFactory.CreateClient(ApiHttpClientConsts.HotelCacheClient);
        _client = new HotelCacheClient(httpClient);
    }

    public async Task<IEnumerable<HotelOfferDto>> Search(HotelOfferSearchCriteria criteria,
        CancellationToken cancellationToken = default)
    {
        var hotelOffers = await _client.ListAsync(new ListHotelOffersQuery
        {
            CheckIn = criteria.CheckIn,
            StayLength = criteria.StayLength,
            MetaCodes = criteria.MetaCodes,
            Occupancies = criteria.Occupancies.Select(o => o.ToString()).ToArray(),
            ProviderConfigurationIds = criteria.ProviderConfigurationIds,
        }, cancellationToken);

        var results = new List<HotelOfferDto>(hotelOffers.Count);
        
        foreach (var offer in hotelOffers)
        {
            var dto = new HotelOfferDto
            {
                Id = offer.Id,
                CheckIn = offer.CheckIn,
                StayLength = offer.StayLength,
                MetaCode = offer.MetaCode,
                ProviderConfigurationId = offer.ProviderConfigurationId,
                Occupancy = offer.Occupancy,
                Prices = CreatePricesDictionary(offer.Prices)
            };
            
            results.Add(dto);
        }
        
        return results;
    }
    
    private static Dictionary<HotelOfferDto.HotelOfferMealPlanDto, Dictionary<HotelOfferDto.HotelOfferRefundabilityDto, HotelOfferDto.HotelOfferMoneyDto>> 
        CreatePricesDictionary(IDictionary<string, IDictionary<string, Money>> prices)
    {
        var result = new Dictionary<HotelOfferDto.HotelOfferMealPlanDto, 
            Dictionary<HotelOfferDto.HotelOfferRefundabilityDto, HotelOfferDto.HotelOfferMoneyDto>>(prices.Count);
            
        foreach (var mealPlanEntry in prices)
        {
            var mealPlan = Enum.Parse<HotelOfferDto.HotelOfferMealPlanDto>(mealPlanEntry.Key);
            var refundabilityDict = new Dictionary<HotelOfferDto.HotelOfferRefundabilityDto, 
                HotelOfferDto.HotelOfferMoneyDto>(mealPlanEntry.Value.Count);
                
            foreach (var refundabilityEntry in mealPlanEntry.Value)
            {
                var refundability = Enum.Parse<HotelOfferDto.HotelOfferRefundabilityDto>(refundabilityEntry.Key);
                var price = new HotelOfferDto.HotelOfferMoneyDto
                {
                    Value = refundabilityEntry.Value.Value,
                    Currency = refundabilityEntry.Value.Currency
                };
                
                refundabilityDict.Add(refundability, price);
            }
            
            result.Add(mealPlan, refundabilityDict);
        }
        
        return result;
    }
}