namespace Esky.Packages.Repricer.Events.HotelOffers;

public class HotelOfferQuoteEvent
{
    public required int MetaCode { get; init; }
    public required DateOnly CheckIn { get; init; }
    public required int StayLength { get; init; }
    public required string Currency { get; init; }
    public required int ProviderCode { get; init; }
    // occupancy x mealPlan x refundable x price
    public required Dictionary<string, Dictionary<string, Dictionary<bool, decimal>>> Prices { get; init; }
}