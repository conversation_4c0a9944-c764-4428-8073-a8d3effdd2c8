{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Esky": "Debug"}}, "Mongo": {"ConnectionString": "mongodb://localhost:27017/esky-packages?retryWrites=true&replicaSet=packages&compressors=snappy,zlib,zstd&w=1&journal=true&readPreference=secondaryPreferred"}, "MongoSearch": {"ConnectionString": "mongodb://localhost:27017/esky-packages-search?retryWrites=true&replicaSet=packages&compressors=snappy,zlib,zstd&w=1&journal=true&readPreference=secondaryPreferred"}, "FlightQuoteConsumer": {"ConsumerGroup": "packageRepricerNew-dev", "BootstrapServers": "localhost:9092", "Topic": "repartitionedFlightQuotes", "AutoOffsetReset": "Latest"}, "HotelOfferQuoteConsumer": {"ConsumerGroup": "packageRepricerNew-dev", "BootstrapServers": "localhost:9092", "Topic": "repartitionedHotelOfferQuotes", "AutoOffsetReset": "Latest"}, "BloomFilterNotificationProducer": {"BootstrapServers": "localhost:9092", "Topic": "bloomFilterNotifications", "CompressionType": "Lz4"}}