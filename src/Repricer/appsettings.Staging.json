{"Mongo": {"ConnectionString": "SECRET"}, "FlightQuoteConsumer": {"ConsumerGroup": "packageRepricerNew-staging", "BootstrapServers": "esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-0.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-1.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-2.service.gcp-staging.consul:9092", "Topic": "repartitionedFlightQuotesNew", "AutoOffsetReset": "Latest"}, "HotelOfferQuoteConsumer": {"ConsumerGroup": "packageRepricerNew-staging", "BootstrapServers": "esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-0.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-1.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-2.service.gcp-staging.consul:9092", "Topic": "repartitionedHotelOfferQuotesNew", "AutoOffsetReset": "Latest"}, "BloomFilterNotificationProducer": {"BootstrapServers": "esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-0.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-1.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-2.service.gcp-staging.consul:9092", "Topic": "bloomFilterNotificationsNew", "CompressionType": "Lz4"}}