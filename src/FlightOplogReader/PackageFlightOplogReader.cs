using System.Text.Json;
using Confluent.Kafka;
using Esky.Hotels.Infrastructure.Kafka.ConfirmationGroup;
using Esky.Hotels.Infrastructure.Kafka.Producer;
using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.Domain.Events.PackageQuotes;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;
using Esky.Packages.FlightOplogReader.Observability;
using Esky.Packages.FlightOplogReader.Options;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.OplogReaders;
using Esky.Packages.Infrastructure.Partitioners;
using Esky.Packages.Infrastructure.Serialization.Contexts;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Esky.Packages.FlightOplogReader;

public class PackageFlightOplogReader(
    PackageDatabase database,
    ILogger<OplogReader<PackageFlight>> logger,
    KafkaProducerFactory producerFactory,
    ConfirmationQueue confirmationQueue,
    OplogReaderOptions options,
    PackageFlightProducerOptions producerOptions,
    string collectionName,
    string resumeTokenCollectionName)
    : OplogReader<PackageFlight>(database.Database, logger, collectionName, resumeTokenCollectionName)
{
    private KafkaProducer<string, byte[]> _producer = null!;
    private readonly HotelMetaCodePartitioner _packageQuotePartitioner = new();
    private readonly AirportPartitioner _packageFlightVariantPartitioner = new();

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _packageQuotePartitioner.Initialize(producerOptions.PackageQuoteTopic, producerOptions.BootstrapServers);
        _packageFlightVariantPartitioner.Initialize(producerOptions.PackageFlightVariantTopic,
            producerOptions.BootstrapServers);

        _producer = producerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(producerOptions.BootstrapServers)
            .WithAcks(Acks.All) // TODO: Check if this is the correct setting for the producer
            .WithDeliveryReportFields(ProducerDeliveryReportFields.Status)
            .WithClientId(nameof(PackageFlightOplogReader))
            .WithCompression(producerOptions.Compression)
            .WithQueueBufferingMaxMessages(1_000_000));

        if (options.PartitionCount < 1 || options.PartitionIndex < 0 ||
            options.PartitionIndex > options.PartitionCount - 1)
            throw new InvalidOperationException("Invalid configuration - PartitionCount/PartitionIndex");

        ReaderName = $"{nameof(PackageFlightOplogReader)}-{options.PartitionIndex}";

        return base.ExecuteAsync(stoppingToken);
    }

    protected override void ConfigureOptions(ChangeStreamOptions changeStreamOptions)
    {
        changeStreamOptions.FullDocument = ChangeStreamFullDocumentOption.Required;
        changeStreamOptions.FullDocumentBeforeChange = ChangeStreamFullDocumentBeforeChangeOption.Required;
    }

    protected override PipelineDefinition<ChangeStreamDocument<PackageFlight>, ChangeStreamDocument<PackageFlight>>
        ConfigurePipeline(EmptyPipelineDefinition<ChangeStreamDocument<PackageFlight>> pipeline)
    {
        logger.LogInformation("Partition count: {count}", options.PartitionCount);
        logger.LogInformation("Partition index: {index}", options.PartitionIndex);

        return pipeline.Match(
            BsonDocument.Parse(
                // language=json
                $$"""
                  {
                    "$expr": {
                      "$and": [
                        {
                          "$eq": [
                            {
                              "$mod": [{ "$dayOfYear": "$documentKey._id.c" }, {{options.PartitionCount}}]
                            },
                            {{options.PartitionIndex}}
                          ]
                        },
                        {
                          "$in": ["$operationType", ["insert", "delete", "replace"]]
                        }
                      ]
                    }
                  }
                  """
            )
        );
    }

    protected override void ProcessBatch(IReadOnlyCollection<ChangeStreamDocument<PackageFlight>> batch,
        Action acknowledge)
    {
        Metrics.RegisterLag(DateTimeOffset.FromUnixTimeSeconds(batch.First().ClusterTime.Timestamp).UtcDateTime);

        var quoteEvents = new List<PackageFlightQuoteEvent>();

        foreach (var change in batch)
        {
            if (MapToQuoteEvents(change, logger) is { } e)
            {
                quoteEvents.AddRange(e);
            }
        }

        var variantEvents = new List<PackageFlightVariantEvent>();

        foreach (var change in batch)
        {
            if (MapToVariantEvents(change, logger) is { } e)
            {
                variantEvents.AddRange(e);
            }
        }

        var confirmationCount = quoteEvents.Count + variantEvents.Count;
        var confirmationGroup = confirmationQueue.Queue.CreateAndEnqueue(confirmationCount, _ => acknowledge());

        foreach (var e in quoteEvents)
        {
            SendQuoteEvent(e, confirmationGroup);
        }

        foreach (var e in variantEvents)
        {
            SendVariantEvent(e, confirmationGroup);
        }

        logger.LogDebug("Sent {count} events", confirmationCount);
    }

    protected override void ProcessEmptyBatch()
    {
        Metrics.RegisterLag(null);
    }

    private static List<PackageFlightQuoteEvent> MapToQuoteEvents(
        ChangeStreamDocument<PackageFlight> change,
        ILogger logger)
    {
        var before = change.FullDocumentBeforeChange;
        var after = change.FullDocument;

        var events = new List<PackageFlightQuoteEvent>();

        var beforePrices = before?.GetLowestPricesByOccupancy();
        var afterPrices = after?.GetLowestPricesByOccupancy();

        if (before == null && after == null)
        {
            logger.LogWarning("FullDocument and FullDocumentBeforeChange of the change stream document are null");
        }
        else if (before == null) // added flight
        {
            events.AddRange(after!.MetaCodes.Select(metaCode => CreateQuoteEvent(after.Id, metaCode, afterPrices!)));
        }
        else if (after == null) // removed flight
        {
            events.AddRange(before.MetaCodes.Select(metaCode =>
                CreateQuoteEvent(before.Id, metaCode, new Dictionary<PackageOccupancy, FlightPriceEntry>())));
        }
        else
        {
            var beforeMetaCodes = before.MetaCodes.ToHashSet();
            var afterMetaCodes = after.MetaCodes.ToHashSet();

            var addedMetaCodes = afterMetaCodes.Except(beforeMetaCodes);
            var removedMetaCodes = beforeMetaCodes.Except(afterMetaCodes);

            if (!beforePrices!.DeepEquals(afterPrices!))
            {
                // change price
                events.AddRange(after.MetaCodes.Select(metaCode =>
                    CreateQuoteEvent(after.Id, metaCode, afterPrices!)));
            }

            // new meta codes
            events.AddRange(addedMetaCodes.Select(addedMetaCode =>
                CreateQuoteEvent(after.Id, addedMetaCode, afterPrices!)));

            // removed meta codes
            events.AddRange(removedMetaCodes.Select(removedMetaCode =>
                CreateQuoteEvent(before.Id, removedMetaCode, new Dictionary<PackageOccupancy, FlightPriceEntry>())));
        }

        return events;
    }

    private static PackageFlightQuoteEvent CreateQuoteEvent(
        PackageFlightId id,
        int metaCode,
        Dictionary<PackageOccupancy, FlightPriceEntry> prices) =>
        new()
        {
            Id = id,
            MetaCode = metaCode,
            CheckIn = id.CheckIn,
            StayLength = id.StayLength,
            DepartureAirport = id.DepartureAirport,
            ArrivalAirport = id.ArrivalAirport,
            MarketId = id.MarketId,
            Prices = prices
        };


    private static List<PackageFlightVariantEvent> MapToVariantEvents(
        ChangeStreamDocument<PackageFlight> change,
        ILogger logger)
    {
        var before = change.FullDocumentBeforeChange;
        var after = change.FullDocument;

        var events = new List<PackageFlightVariantEvent>();

        var beforeVariants = before?.GetVariants();
        var afterVariants = after?.GetVariants();

        if (before == null && after == null)
        {
            logger.LogWarning("FullDocument and FullDocumentBeforeChange of the change stream document are null");
        }
        else if (before == null) // added flight
        {
            events.AddRange(afterVariants!.Select(CreateVariantUpdatedEvent));
        }
        else if (after == null) // removed flight
        {
            events.AddRange(beforeVariants!.Select(CreateVariantDeletedEvent));
        }
        else // updated flight
        {
            var deletedVariants = beforeVariants!.Where(b => afterVariants!.All(a => a.Id != b.Id));
            var addedVariants = afterVariants!.Where(a => beforeVariants!.All(b => a.Id != b.Id));
            var updatedVariants = afterVariants!.Where(a => beforeVariants!.Any(b =>
                a.Id == b.Id && (a.Price != b.Price || a.DepartureDate != b.DepartureDate ||
                                 a.ReturnArrivalDate != b.ReturnArrivalDate)));

            events.AddRange(deletedVariants.Select(CreateVariantDeletedEvent));
            events.AddRange(addedVariants.Select(CreateVariantUpdatedEvent));
            events.AddRange(updatedVariants.Select(CreateVariantUpdatedEvent));
        }

        return events;
    }

    private static PackageFlightVariantUpdatedEvent CreateVariantUpdatedEvent(PackageFlightVariant variant) =>
        new()
        {
            Id = variant.Id,
            CheckIn = variant.Id.CheckIn,
            StayLength = variant.Id.StayLength,
            MarketId = variant.Id.MarketId,
            ArrivalAirport = variant.Id.ArrivalAirport,
            DepartureAirport = variant.Id.DepartureAirport,
            Occupancy = variant.Id.Occupancy,
            InboundDeparture = variant.Id.InboundDeparture,
            OutboundDeparture = variant.Id.OutboundDeparture,
            DepartureDate = variant.DepartureDate,
            ReturnArrivalDate = variant.ReturnArrivalDate,
            Price = variant.Price
        };

    private static PackageFlightVariantDeletedEvent CreateVariantDeletedEvent(PackageFlightVariant variant) =>
        new()
        {
            Id = variant.Id,
            CheckIn = variant.Id.CheckIn,
            StayLength = variant.Id.StayLength,
            MarketId = variant.Id.MarketId,
            ArrivalAirport = variant.Id.ArrivalAirport,
            DepartureAirport = variant.Id.DepartureAirport,
            Occupancy = variant.Id.Occupancy,
            InboundDeparture = variant.Id.InboundDeparture,
            OutboundDeparture = variant.Id.OutboundDeparture,
        };

    private void SendQuoteEvent(PackageFlightQuoteEvent e, ConfirmationGroup confirmationGroup)
    {
        var partition = _packageQuotePartitioner.GetTopicPartition(e.MetaCode);

        var value = JsonSerializer.SerializeToUtf8Bytes(e, PackageQuoteEventJsonContext.Default.PackageQuoteEvent);

        _producer.Produce(partition, new Message<string, byte[]>
            {
                Key = e.Id,
                Value = value
            },
            report => MarkConfirmationGroupBasedOnDeliveryReport(report, confirmationGroup));
    }

    private void SendVariantEvent(PackageFlightVariantEvent e, ConfirmationGroup confirmationGroup)
    {
        var partition = _packageFlightVariantPartitioner.GetTopicPartition(e.ArrivalAirport);

        var value = JsonSerializer.SerializeToUtf8Bytes(e,
            PackageFlightVariantEventJsonContext.Default.PackageFlightVariantEvent);

        _producer.Produce(partition, new Message<string, byte[]>
            {
                Key = e.Id,
                Value = value
            },
            report => MarkConfirmationGroupBasedOnDeliveryReport(report, confirmationGroup));
    }

    private void MarkConfirmationGroupBasedOnDeliveryReport<TKey, TValue>(
        DeliveryReport<TKey, TValue> deliveryReport, ConfirmationGroup confirmationGroup)
    {
        if (deliveryReport.Status == PersistenceStatus.Persisted)
        {
            confirmationGroup.ConfirmOne();
        }
        else
        {
            logger.LogError("Cannot deliver message to kafka: {error}", deliveryReport.Error);
            confirmationGroup.MarkAsFailed();
        }
    }
}