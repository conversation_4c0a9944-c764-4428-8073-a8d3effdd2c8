using Esky.Packages.Contract.Common;

namespace Esky.Packages.Contract.Calendars;

public class GetCalendarQueryDto
{
    public required int MetaCode { get; set; }
    public required OccupancyDto Occupancy { get; set; }
    public required string MarketId { get; set; }
    public List<MealPlanDto>? MealPlans { get; set; }
    public List<string>? DepartureAirports { get; set; } 
    public List<TimeOfDayDto>? InboundDepartures { get; set; } 
    public List<TimeOfDayDto>? OutboundDepartures { get; set; } 
}