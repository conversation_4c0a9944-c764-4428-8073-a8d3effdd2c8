using Esky.Packages.Contract.Common;

namespace Esky.Packages.Contract.Calendars;

public class CalendarEntryDto
{
    public required string PackageId { get; set; }
    public required string DepartureAirport { get; set; }
    public required DateOnly CheckIn { get; set; }
    public required DateOnly ReturnArriveDate { get; set; }
    public required MealPlanDto MealPlan { get; set; }
    public required int Price { get; set; }
}