using Esky.Packages.Contract.Common;

namespace Esky.Packages.Contract.Search;

public record GetSearchQueryDto(
    string MarketId,
    List<int> MetaCodes,
    List<int> StayLengths,
    DateOnly DepartureDateFrom,
    DateOnly DepartureDateTo,
    List<string>? DepartureAirports,
    List<MealPlanDto>? MealPlans,
    OccupancyDto? Occupancy,
    List<OccupancyDto>? Occupancies,
    List<TimeOfDayDto>? InboundDepartures,
    List<TimeOfDayDto>? OutboundDepartures,
    int? MaxPrice,
    bool? UseDynamicSearchFallback);