using Esky.Packages.Contract.Common;

namespace Esky.Packages.Contract.PackageVariants;

public record PackageLiveVariantsDto(
    string Id,
    OccupancyDto Occupancy,
    OccupancyDto[] Occupancies,
    DateOnly CheckIn,
    int StayLength,
    int MetaCode,
    string DefinitionId,
    string MarketId,
    string PartnerCode,
    string Currency,
    PackageFlightOfferDto[] FlightOffers,
    PackageHotelOfferDto[] HotelOffers);
