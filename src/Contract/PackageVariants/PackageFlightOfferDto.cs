namespace Esky.Packages.Contract.PackageVariants;

public record PackageFlightOfferDto(
    string Id,
    decimal Price,
    decimal PriceCompensated,
    string DepartureAirport,
    string ArrivalAirport,
    DateOnly DepartureDate,
    DateOnly ArrivalDate,
    DateOnly ReturnDepartureDate,
    DateOnly ReturnArrivalDate,
    int Stops,
    string[] FlightIds,
    string[] LegLocators,
    bool RegisteredBaggageIncluded,
    bool IsSelected);
