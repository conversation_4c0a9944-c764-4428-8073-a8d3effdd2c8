using Esky.Packages.Contract.Common;

namespace Esky.Packages.Contract.PackageVariants;

public record GetPackageLiveVariantsQueryDto(
    OccupancyDto? Occupancy,
    OccupancyDto[]? Occupancies,
    string[]? DepartureAirports,
    string? FlightOptionId,
    string? PreferredDepartureAirport,
    TimeOfDayDto[]? InboundDepartures,
    TimeOfDayDto[]? OutboundDepartures,
    bool PreferSelectedFlight = false);
