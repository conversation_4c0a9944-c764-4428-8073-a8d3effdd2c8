using Esky.Packages.Contract.Common;

namespace Esky.Packages.Contract.PackageAvailabilities;

public record PackageAvailabilityDto(
    int MetaCode,
    OccupancyDto[] Occupancies,
    MealPlanDto[] MealPlans,
    string[] DepartureAirports,
    int[] StayLengths,
    decimal LowestTotalPrice,
    Dictionary<int, decimal> LowestPricesPerStayLength,
    DateO<PERSON><PERSON>n,
    DateOnly MaxCheckIn);
