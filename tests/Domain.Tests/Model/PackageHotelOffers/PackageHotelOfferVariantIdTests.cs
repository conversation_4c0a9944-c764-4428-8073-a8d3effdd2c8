using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelOfferVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageHotelOffers;

public class PackageHotelOfferVariantIdTests
{
    [Fact]
    public void Constructor_WithValidStringId_ParsesCorrectly()
    {
        // Arrange
        var id = "250101:7:pl:1234:A2:B";

        // Act
        var packageHotelOfferVariantId = new PackageHotelOfferVariantId(id);

        // Assert
        packageHotelOfferVariantId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageHotelOfferVariantId.StayLength.ShouldBe(7);
        packageHotelOfferVariantId.MarketId.ShouldBe("pl");
        packageHotelOfferVariantId.MetaCode.ShouldBe(1234);
        packageHotelOfferVariantId.Occupancy.ShouldBe(new PackageOccupancy(2, 0, 0, 0));
        packageHotelOfferVariantId.MealPlan.ShouldBe(MealPlan.Breakfast);
    }

    [Fact]
    public void Constructor_WithInvalidStringId_ThrowsFormatException()
    {
        // Arrange
        var id = "invalid:id";

        // Act & Assert
        Should.Throw<FormatException>(() => new PackageHotelOfferVariantId(id));
    }

    [Fact]
    public void Equals_WithSameValues_ReturnsTrue()
    {
        // Arrange
        var id1 = new PackageHotelOfferVariantId("250101:7:pl:1234:A2:B");
        var id2 = new PackageHotelOfferVariantId("250101:7:pl:1234:A2:B");

        // Act & Assert
        id1.Equals(id2).ShouldBeTrue();
    }

    [Fact]
    public void Equals_WithDifferentValues_ReturnsFalse()
    {
        // Arrange
        var id1 = new PackageHotelOfferVariantId("250101:7:pl:1234:A2:B");
        var id2 = new PackageHotelOfferVariantId("250101:7:pl:5678:A2:B");

        // Act & Assert
        id1.Equals(id2).ShouldBeFalse();
    }

    [Fact]
    public void ToString_ReturnsExpectedFormat()
    {
        // Arrange
        var packageHotelOfferVariantId = new PackageHotelOfferVariantId(
            DateOnly.ParseExact("250101", "yyMMdd"), 7, "pl", 1234,
            new PackageOccupancy(2, 0, 0, 0), MealPlan.Breakfast);

        // Act
        var result = packageHotelOfferVariantId.ToString();

        // Assert
        result.ShouldBe("250101:7:pl:1234:A2:B");
    }

    [Fact]
    public void TryParse_WithValidString_ReturnsTrue()
    {
        // Arrange
        var id = "250101:7:pl:1234:A2:B";

        // Act
        var success = PackageHotelOfferVariantId.TryParse(id, null, out var packageHotelOfferVariantId);

        // Assert
        success.ShouldBeTrue();
        packageHotelOfferVariantId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageHotelOfferVariantId.StayLength.ShouldBe(7);
        packageHotelOfferVariantId.MarketId.ShouldBe("pl");
        packageHotelOfferVariantId.MetaCode.ShouldBe(1234);
        packageHotelOfferVariantId.Occupancy.ShouldBe(new PackageOccupancy(2, 0, 0, 0));
        packageHotelOfferVariantId.MealPlan.ShouldBe(MealPlan.Breakfast);
    }

    [Fact]
    public void TryParse_WithInvalidString_ReturnsFalse()
    {
        // Arrange
        var id = "invalid:id";

        // Act
        var success = PackageHotelOfferVariantId.TryParse(id, null, out var packageHotelOfferVariantId);

        // Assert
        success.ShouldBeFalse();
        packageHotelOfferVariantId.ShouldBe(default);
    }
}