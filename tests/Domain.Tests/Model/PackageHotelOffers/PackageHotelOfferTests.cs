using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Common.Tests.Fixtures.Domain;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageHotelOffers;

public class PackageHotelOfferTests
{
    [Fact]
    public void Create_WithDifferentHotelOfferQuotesCurrency_ConvertsToPackageHotelOfferCurrency()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithCurrencyConversion(Currency.PLN, 0.2M);
        var occupancy = new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0);
        var prices = new Dictionary<MealPlan, Dictionary<Refundability, Money>>
        {
            { MealPlan.Breakfast, new Dictionary<Refundability, Money> { { Refundability.Refundable, new Money(100M, Currency.PLN) } } }
        };
        var hotelOfferQuote = fixture.CreateHotelOfferQuote(prices: prices, occupancy: occupancy);

        // Act
        var packageHotelOffer = fixture.CreatePackageHotelOffer(hotelOfferQuotes: [hotelOfferQuote]);

        // Assert
        packageHotelOffer.Prices["OTS|123"][occupancy][MealPlan.Breakfast].ShouldBe(20M);
        packageHotelOffer.Currency.ShouldBe(Currency.GBP);
    }

    [Fact]
    public void Compare_WithSamePackageHotelOffers_ReturnsTrue()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var packageHotelOffer1 = fixture.CreatePackageHotelOffer();
        var packageHotelOffer2 = fixture.CreatePackageHotelOffer();

        // Act
        var result = packageHotelOffer1.Compare(packageHotelOffer2);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void Compare_WithDifferentPricesWithinCompensation_ReturnsTrue()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var prices1 = new Dictionary<MealPlan, Dictionary<Refundability, Money>>
        {
            { MealPlan.Breakfast, new Dictionary<Refundability, Money> { { Refundability.Refundable, new Money(100M, Currency.GBP) } } }
        };
        var prices2 = new Dictionary<MealPlan, Dictionary<Refundability, Money>>
        {
            { MealPlan.Breakfast, new Dictionary<Refundability, Money> { { Refundability.Refundable, new Money(101M, Currency.GBP) } } }
        };

        var hotelOfferQuote1 = fixture.CreateHotelOfferQuote(prices: prices1);
        var hotelOfferQuote2 = fixture.CreateHotelOfferQuote(prices: prices2);

        var packageHotelOffer1 = fixture.CreatePackageHotelOffer(hotelOfferQuotes: [hotelOfferQuote1]);
        var packageHotelOffer2 = fixture.CreatePackageHotelOffer(hotelOfferQuotes: [hotelOfferQuote2]);

        // Act
        var result = packageHotelOffer1.Compare(packageHotelOffer2);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void Compare_WithDifferentPricesExceedingCompensation_ReturnsFalse()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var prices1 = new Dictionary<MealPlan, Dictionary<Refundability, Money>>
        {
            { MealPlan.Breakfast, new Dictionary<Refundability, Money> { { Refundability.Refundable, new Money(100M, Currency.GBP) } } }
        };
        var prices2 = new Dictionary<MealPlan, Dictionary<Refundability, Money>>
        {
            { MealPlan.Breakfast, new Dictionary<Refundability, Money> { { Refundability.Refundable, new Money(200M, Currency.GBP) } } }
        };

        var hotelOfferQuote1 = fixture.CreateHotelOfferQuote(prices: prices1);
        var hotelOfferQuote2 = fixture.CreateHotelOfferQuote(prices: prices2);

        var packageHotelOffer1 = fixture.CreatePackageHotelOffer(hotelOfferQuotes: [hotelOfferQuote1]);
        var packageHotelOffer2 = fixture.CreatePackageHotelOffer(hotelOfferQuotes: [hotelOfferQuote2]);

        // Act
        var result = packageHotelOffer1.Compare(packageHotelOffer2);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public void ApplyQuotes_WithEmptyQuotes_DoesNotUpdatePrices()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var packageHotelOffer = fixture.CreatePackageHotelOffer();
        var originalUpdatedAt = packageHotelOffer.UpdatedAt;

        // Act
        var isUpdated = packageHotelOffer.ApplyQuotes([]);

        // Assert
        isUpdated.ShouldBeFalse();
        packageHotelOffer.UpdatedAt.ShouldBe(originalUpdatedAt);
    }

    [Fact]
    public void ApplyQuotes_WithNonMatchingStayKey_DoesNotUpdatePrices()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var notExistingMetaCode = 999;
        var packageHotelOffer = fixture.CreatePackageHotelOffer();
        var originalUpdatedAt = packageHotelOffer.UpdatedAt;

        var nonMatchingStayKey = fixture.CreatePackageHotelOfferStayKey(metaCode: notExistingMetaCode);
        var quotes = new Dictionary<PackageHotelOfferStayKey, List<HotelOfferQuote>>
        {
            { nonMatchingStayKey, [fixture.CreateHotelOfferQuote()] }
        };

        // Act
        var isUpdated = packageHotelOffer.ApplyQuotes(quotes);

        // Assert
        isUpdated.ShouldBeFalse();
        packageHotelOffer.UpdatedAt.ShouldBe(originalUpdatedAt);
    }

    [Fact]
    public void ApplyQuotes_WithOldHotelOfferQuote_DoesNotUpdatePrices()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var hotelOfferQuote = fixture.CreateHotelOfferQuote(updatedAt: new DateTime(2020, 1, 2));
        var packageHotelOffer = fixture.CreatePackageHotelOffer(hotelOfferQuotes: [hotelOfferQuote]);
        var originalUpdatedAt = packageHotelOffer.UpdatedAt;

        var stayKey = fixture.CreatePackageHotelOfferStayKey();
        var quotes = new Dictionary<PackageHotelOfferStayKey, List<HotelOfferQuote>>
        {
            { stayKey, [fixture.CreateHotelOfferQuote(updatedAt: new DateTime(2025, 1, 1))] }
        };

        // Act
        var isUpdated = packageHotelOffer.ApplyQuotes(quotes);

        // Assert
        isUpdated.ShouldBeFalse();
        packageHotelOffer.UpdatedAt.ShouldBe(originalUpdatedAt);
    }

    [Fact]
    public void ApplyQuotes_WithValidHotelOfferQuotes_UpdatePrices()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var packageHotelOffer = fixture.CreatePackageHotelOffer();
        var originalUpdatedAt = packageHotelOffer.UpdatedAt;

        var stayKey = fixture.CreatePackageHotelOfferStayKey();
        var newPrices = new Dictionary<MealPlan, Dictionary<Refundability, Money>>
        {
            { MealPlan.Breakfast, new Dictionary<Refundability, Money> { { Refundability.Refundable, new Money(50M, Currency.GBP) } } }
        };
        var quotes = new Dictionary<PackageHotelOfferStayKey, List<HotelOfferQuote>>
        {
            { stayKey, [fixture.CreateHotelOfferQuote(prices: newPrices, updatedAt: new DateTime(2025, 1, 3))] }
        };

        // Act
        var isUpdated = packageHotelOffer.ApplyQuotes(quotes);

        // Assert
        isUpdated.ShouldBeTrue();
        packageHotelOffer.UpdatedAt.ShouldBeGreaterThan(originalUpdatedAt);
    }

    [Fact]
    public void ApplyQuotes_WithDiffentHotelOfferQuotesCurrency_ConvertsToPackageHotelOfferCurrency()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion()
            .WithCurrencyConversion(Currency.USD, 0.77M);
        var occupancy = new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0);
        var packageHotelOffer = fixture.CreatePackageHotelOffer();
        var originalUpdatedAt = packageHotelOffer.UpdatedAt;

        var newPrices = new Dictionary<MealPlan, Dictionary<Refundability, Money>>
        {
            { MealPlan.Breakfast, new Dictionary<Refundability, Money> { { Refundability.Refundable, new Money(50M, Currency.USD) } } }
        };
        var quotes = new Dictionary<PackageHotelOfferStayKey, List<HotelOfferQuote>>
        {
            { fixture.CreatePackageHotelOfferStayKey(), [fixture.CreateHotelOfferQuote(prices: newPrices, occupancy: occupancy, updatedAt: new DateTime(2025, 1, 3))] }
        };

        // Act
        var isUpdated = packageHotelOffer.ApplyQuotes(quotes);

        // Assert
        isUpdated.ShouldBeTrue();
        packageHotelOffer.UpdatedAt.ShouldBeGreaterThan(originalUpdatedAt);
        packageHotelOffer.Prices["OTS|123"][occupancy][MealPlan.Breakfast].ShouldBe(39M);
        packageHotelOffer.Currency.ShouldBe(Currency.GBP);
    }
}