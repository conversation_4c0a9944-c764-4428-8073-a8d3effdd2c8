using Esky.Packages.Common.Tests;
using Esky.Packages.Common.Tests.Fixtures.Domain;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.Packages;

public class PackageTests
{
    [Fact]
    public void GetVariants_ReturnsExpectedPackageVariants()
    {
        // Arrange
        var package = PackageTestFixture.CreatePackage();

        // Act
        var variants = package.GetVariants();

        // Assert
        variants.Count.ShouldBe(2);

        var expectedDepartureDate = DateOnly.FromDateTime(new DateTime(2025, 3, 24));
        var expectedReturnArrivalDate = DateOnly.FromDateTime(new DateTime(2025, 3, 31));
        var expectedOccupancy = DefaultTestValues.PackageOccupancy;
        var expectedStayLength = DefaultTestValues.StayLength;
        var expectedMarketId = "pl";
        var expectedDepartureAirport = DefaultTestValues.DepartureAirport;

        variants.ShouldContain(variant =>
            variant.Id.Occupancy.Equals(expectedOccupancy) &&
            variant.Id.MealPlan == MealPlan.Breakfast &&
            variant.Id.StayLength == expectedStayLength &&
            variant.Id.MarketId == expectedMarketId &&
            variant.Id.DepartureAirport.Equals(expectedDepartureAirport) &&
            variant.Price == 800M &&
            variant.DepartureDate == expectedDepartureDate &&
            variant.ReturnArrivalDate == expectedReturnArrivalDate);

        variants.ShouldContain(variant =>
            variant.Id.Occupancy.Equals(expectedOccupancy) &&
            variant.Id.MealPlan == MealPlan.HalfBoard &&
            variant.Id.StayLength == expectedStayLength &&
            variant.Id.MarketId == expectedMarketId &&
            variant.Id.DepartureAirport.Equals(expectedDepartureAirport) &&
            variant.Price == 1000M &&
            variant.DepartureDate == expectedDepartureDate &&
            variant.ReturnArrivalDate == expectedReturnArrivalDate);
    }

    [Fact]
    public void GetLowestPricesPerDepartureAirportAndOccupancy_WithMultipleFlightPrices_ReturnsLowestPrices()
    {
        // Arrange
        var flightPrices = new Dictionary<Airport, Dictionary<Airport, Dictionary<PackageOccupancy, FlightPriceEntry>>>
        {
            {
                new Airport("JFK"),
                new Dictionary<Airport, Dictionary<PackageOccupancy, FlightPriceEntry>>
                {
                    {
                        new Airport("LHR"),
                        new Dictionary<PackageOccupancy, FlightPriceEntry>
                        {
                            {
                                new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0),
                                new FlightPriceEntry
                                {
                                    Price = 500M,
                                    DepartureDate = DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
                                    ReturnArrivalDate = DateOnly.FromDateTime(DateTime.Today.AddDays(8))
                                }
                            }
                        }
                    }
                }
            },
            {
                new Airport("LAX"),
                new Dictionary<Airport, Dictionary<PackageOccupancy, FlightPriceEntry>>
                {
                    {
                        new Airport("LHR"),
                        new Dictionary<PackageOccupancy, FlightPriceEntry>
                        {
                            {
                                new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0),
                                new FlightPriceEntry
                                {
                                    Price = 300M,
                                    DepartureDate = DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
                                    ReturnArrivalDate = DateOnly.FromDateTime(DateTime.Today.AddDays(8))
                                }
                            }
                        }
                    }
                }
            }
        };
        var package = PackageTestFixture.CreatePackage(flightPrices: flightPrices);

        // Act
        var lowestPrices = package.GetLowestPricesPerDepartureAirportAndOccupancy().ToList();

        // Assert
        lowestPrices.Count.ShouldBe(1);
        lowestPrices[0].PriceEntry.Price.ShouldBe(300M);
    }

    [Fact]
    public void GetLowestPricesPerOccupancyAndMealPlan_WithMultipleHotelOfferPrices_ReturnsLowestPrices()
    {
        // Arrange
        var hotelOfferPrices = new Dictionary<PackageOccupancy, Dictionary<MealPlan, decimal>>
        {
            {
                new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0),
                new Dictionary<MealPlan, decimal>
                {
                    { MealPlan.Breakfast, 200M },
                    { MealPlan.HalfBoard, 250M }
                }
            },
            {
                new PackageOccupancy(adults: 2, youths: 1, children: 0, infants: 0),
                new Dictionary<MealPlan, decimal>
                {
                    { MealPlan.FullBoard, 350M },
                    { MealPlan.Breakfast, 170M }
                }
            }
        };

        var package = PackageTestFixture.CreatePackage(hotelOfferPrices: hotelOfferPrices);

        // Act
        var lowestPrices = package.GetLowestPricesPerOccupancyAndMealPlan().ToList();

        // Assert
        lowestPrices.Count.ShouldBe(4);

        lowestPrices.ShouldContain(lp =>
            lp.Occupancy.Equals(new PackageOccupancy(2, 0, 0, 0)) &&
            lp.MealPlan == MealPlan.Breakfast &&
            lp.Price == 200M);

        lowestPrices.ShouldContain(lp =>
            lp.Occupancy.Equals(new PackageOccupancy(2, 0, 0, 0)) &&
            lp.MealPlan == MealPlan.HalfBoard &&
            lp.Price == 250M);

        lowestPrices.ShouldContain(lp =>
            lp.Occupancy.Equals(new PackageOccupancy(2, 1, 0, 0)) &&
            lp.MealPlan == MealPlan.Breakfast &&
            lp.Price == 170M);

        lowestPrices.ShouldContain(lp =>
            lp.Occupancy.Equals(new PackageOccupancy(2, 1, 0, 0)) &&
            lp.MealPlan == MealPlan.FullBoard &&
            lp.Price == 350M);
    }
}
