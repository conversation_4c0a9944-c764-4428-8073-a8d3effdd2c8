using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.Packages;

public class PackageVariantIdTests
{
    [Fact]
    public void Constructor_WithValidStringId_ParsesCorrectly()
    {
        // Arrange
        var id = "250101:7:pl:1234:A2:LAX:B";

        // Act
        var packageVariantId = new PackageVariantId(id);

        // Assert
        packageVariantId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageVariantId.StayLength.ShouldBe(7);
        packageVariantId.MarketId.ShouldBe("pl");
        packageVariantId.MetaCode.ShouldBe(1234);
        packageVariantId.Occupancy.ShouldBe(new PackageOccupancy(2, 0, 0, 0));
        packageVariantId.DepartureAirport.ShouldBe(new Airport("LAX"));
        packageVariantId.MealPlan.ShouldBe(MealPlan.Breakfast);
    }

    [Fact]
    public void Constructor_WithInvalidStringId_ThrowsFormatException()
    {
        // Arrange
        var id = "invalid:id";

        // Act & Assert
        Should.Throw<FormatException>(() => new PackageVariantId(id));
    }

    [Fact]
    public void Equals_WithSameValues_ReturnsTrue()
    {
        // Arrange
        var id1 = new PackageVariantId("250101:7:pl:1234:A2:LAX:B");
        var id2 = new PackageVariantId("250101:7:pl:1234:A2:LAX:B");

        // Act & Assert
        id1.Equals(id2).ShouldBeTrue();
    }

    [Fact]
    public void Equals_WithDifferentValues_ReturnsFalse()
    {
        // Arrange
        var id1 = new PackageVariantId("250101:7:pl:1234:A2:LAX:B");
        var id2 = new PackageVariantId("250101:7:pl:5678:A2:LAX:B");

        // Act & Assert
        id1.Equals(id2).ShouldBeFalse();
    }

    [Fact]
    public void ToString_ReturnsExpectedFormat()
    {
        // Arrange
        var packageVariantId = new PackageVariantId(
            DateOnly.ParseExact("250101", "yyMMdd"), 7, "pl", 1234,
            new PackageOccupancy(2, 0, 0, 0), new Airport("LAX"), MealPlan.Breakfast);

        // Act
        var result = packageVariantId.ToString();

        // Assert
        result.ShouldBe("250101:7:pl:1234:A2:LAX:B");
    }

    [Fact]
    public void TryParse_WithValidString_ReturnsTrue()
    {
        // Arrange
        var id = "250101:7:pl:1234:A2:LAX:B";

        // Act
        var success = PackageVariantId.TryParse(id, null, out var packageVariantId);

        // Assert
        success.ShouldBeTrue();
        packageVariantId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageVariantId.StayLength.ShouldBe(7);
        packageVariantId.MarketId.ShouldBe("pl");
        packageVariantId.MetaCode.ShouldBe(1234);
        packageVariantId.Occupancy.ShouldBe(new PackageOccupancy(2, 0, 0, 0));
        packageVariantId.DepartureAirport.ShouldBe(new Airport("LAX"));
        packageVariantId.MealPlan.ShouldBe(MealPlan.Breakfast);
    }

    [Fact]
    public void TryParse_WithInvalidString_ReturnsFalse()
    {
        // Arrange
        var id = "invalid:id";

        // Act
        var success = PackageVariantId.TryParse(id, null, out var packageVariantId);

        // Assert
        success.ShouldBeFalse();
        packageVariantId.ShouldBe(default);
    }
}
