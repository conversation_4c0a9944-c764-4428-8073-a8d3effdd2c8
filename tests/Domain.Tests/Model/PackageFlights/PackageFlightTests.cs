using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Common.Tests.Fixtures.Domain;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageFlights;

public class PackageFlightTests
{
    [Fact]
    public void Create_WithDifferentFlightQuotesCurrency_ConvertsToPackageFlightCurrency()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithCurrencyConversion(Currency.PLN, 0.2M);

        var prices = new Dictionary<PackageOccupancy, decimal>
        {
            { new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0), 200M }
        };
        var flight = fixture.CreateFlight(prices: prices);
        var flightOffer = fixture.CreateFlightOffer(flights: [flight]);
        var flightQuotes = fixture.CreateFlightQuote(prices: prices, currency: Currency.PLN);

        // Act
        var packageFlight = fixture.CreatePackageFlight(flightOffers: [flightOffer], quotes: [flightQuotes], currency: Currency.GBP);

        // Assert
        packageFlight.FlightOffers[0].Flights[0].Prices.ElementAt(0).Value.ShouldBe(40M);
        packageFlight.Currency.ShouldBe(Currency.GBP);
    }

    [Fact]
    public void Compare_WithSamePackageFlights_ReturnsTrue()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithAnyCurrencyConversion();
        var packageFlight1 = fixture.CreatePackageFlight();
        var packageFlight2 = fixture.CreatePackageFlight();

        // Act
        var result = packageFlight1.Compare(packageFlight2);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void Compare_WithDifferentPricesExceedingCompensation_ReturnsFalse()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithAnyCurrencyConversion();
        var prices1 = new Dictionary<PackageOccupancy, decimal>
        {
            { new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0), 100M }
        };
        var prices2 = new Dictionary<PackageOccupancy, decimal>
        {
            { new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0), 200M }
        };

        var flight1 = fixture.CreateFlight(prices: prices1);
        var flight2 = fixture.CreateFlight(prices: prices2);

        var flightOffer1 = fixture.CreateFlightOffer(flights: [flight1]);
        var flightOffer2 = fixture.CreateFlightOffer(flights: [flight2]);

        var flightQuotes1 = fixture.CreateFlightQuote(prices: prices1);
        var flightQuotes2 = fixture.CreateFlightQuote(prices: prices2);

        var packageFlight1 = fixture.CreatePackageFlight(flightOffers: [flightOffer1], quotes: [flightQuotes1]);
        var packageFlight2 = fixture.CreatePackageFlight(flightOffers: [flightOffer2], quotes: [flightQuotes2]);

        // Act
        var result = packageFlight1.Compare(packageFlight2);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public void Compare_WithDifferentFlightOffers_ReturnsFalse()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithAnyCurrencyConversion();
        var packageFlight1 = fixture.CreatePackageFlight(
            flightOffers:
            [
                fixture.CreateFlightOffer(flights:
                [
                    fixture.CreateFlight(flightId: "flightId1")
                ])
            ]);

        var packageFlight2 = fixture.CreatePackageFlight(
            flightOffers:
            [
                fixture.CreateFlightOffer(flights:
                [
                    fixture.CreateFlight(flightId: "flightId2")
                ])
            ]);

        // Act
        var result = packageFlight1.Compare(packageFlight2);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public void GetFlightIds_WithDuplicateFlightIds_ReturnsUniqueFlightIds()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithAnyCurrencyConversion();
        var flightOffer1 = fixture.CreateFlightOffer(
            flights: [fixture.CreateFlight(flightId: "flightId1")]);
        var flightOffer2 = fixture.CreateFlightOffer(
            flights: [fixture.CreateFlight(flightId: "flightId2")]);
        var flightOffer3 = fixture.CreateFlightOffer(
            flights: [fixture.CreateFlight(flightId: "flightId1")]); // Duplicate Flight ID

        var packageFlight = fixture.CreatePackageFlight(flightOffers: [flightOffer1, flightOffer2, flightOffer3]);

        // Act
        var flightIds = packageFlight.GetFlightIds();

        // Assert
        flightIds.ShouldBe(["flightId1", "flightId2"]);
    }

    [Fact]
    public void ApplyQuotes_WithEmptyQuotes_DoesNotUpdateFlights()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithAnyCurrencyConversion();
        var packageFlight = fixture.CreatePackageFlight();
        var originalUpdatedAt = packageFlight.UpdatedAt;

        // Act
        var isUpdated = packageFlight.ApplyQuotes([]);

        // Assert
        isUpdated.ShouldBeFalse();
        packageFlight.UpdatedAt.ShouldBe(originalUpdatedAt);
    }

    [Fact]
    public void ApplyQuotes_WithInvalidFlightId_DoesNotUpdateFlights()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithAnyCurrencyConversion();
        var notExistingFlightId = "NotExistingFlightId";
        var packageFlight = fixture.CreatePackageFlight();
        var originalUpdatedAt = packageFlight.UpdatedAt;

        // Act
        var isUpdated = packageFlight.ApplyQuotes(new Dictionary<string, FlightQuote>
        {
            { notExistingFlightId, fixture.CreateFlightQuote(flightId: notExistingFlightId) }
        });

        // Assert
        isUpdated.ShouldBeFalse();
        packageFlight.UpdatedAt.ShouldBe(originalUpdatedAt);
    }

    [Fact]
    public void ApplyQuotes_WithOldHotelOfferQuote_DoesNotUpdateFlights()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithAnyCurrencyConversion();
        var packageFlight = fixture.CreatePackageFlight();
        var originalUpdatedAt = packageFlight.UpdatedAt;

        // Act
        var isUpdated = packageFlight.ApplyQuotes(new Dictionary<string, FlightQuote>
        {
            { "flightId1", fixture.CreateFlightQuote(updateTime: new DateTime(2025, 1, 1)) }
        });

        // Assert
        isUpdated.ShouldBeFalse();
        packageFlight.UpdatedAt.ShouldBe(originalUpdatedAt);
    }

    [Fact]
    public void ApplyQuotes_WithValidFlightQuotes_UpdatePrices()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithAnyCurrencyConversion();
        var packageFlight = fixture.CreatePackageFlight(currency: Currency.EUR);
        var originalUpdatedAt = packageFlight.UpdatedAt;
        var newPrices = new Dictionary<PackageOccupancy, decimal>
        {
            { new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0), 50M }
        };
        var quotes = new Dictionary<string, FlightQuote>
        {
            { "flightId1", fixture.CreateFlightQuote(prices: newPrices, currency: Currency.EUR) }
        };

        // Act
        var isUpdated = packageFlight.ApplyQuotes(quotes);

        // Assert
        isUpdated.ShouldBeTrue();
        packageFlight.UpdatedAt.ShouldBeGreaterThan(originalUpdatedAt);
        packageFlight.FlightOffers[0].Flights[0].Prices.ElementAt(0).Value.ShouldBe(50M);
        packageFlight.Currency.ShouldBe(Currency.EUR);
    }

    [Fact]
    public void ApplyQuotes_WithDifferentFlightQuotesCurrency_ConvertsToPackageFlightCurrency()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithAnyCurrencyConversion()
            .WithCurrencyConversion(Currency.USD, 0.77M);
        var packageFlight = fixture.CreatePackageFlight();
        var originalUpdatedAt = packageFlight.UpdatedAt;

        var newPrices = new Dictionary<PackageOccupancy, decimal>
        {
            { new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0), 50M }
        };
        var newFlightQuotes = new Dictionary<string, FlightQuote>
        {
            { "flightId1", fixture.CreateFlightQuote(prices: newPrices, currency: Currency.USD) }
        };

        // Act
        var isUpdated = packageFlight.ApplyQuotes(newFlightQuotes);

        // Assert
        isUpdated.ShouldBeTrue();
        packageFlight.UpdatedAt.ShouldBeGreaterThan(originalUpdatedAt);
        packageFlight.FlightOffers[0].Flights[0].Prices.ElementAt(0).Value.ShouldBe(39M);
        packageFlight.Currency.ShouldBe(Currency.GBP);
    }

    [Fact]
    public void Create_WithThreeOrLessFlightOffers_DoesNotLimitFlightOffers()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithAnyCurrencyConversion();
            
        var flightOffers = new List<FlightOffer>
        {
            fixture.CreateFlightOffer(id: "offer1", flights: [fixture.CreateFlight(flightId: "flight1")]),
            fixture.CreateFlightOffer(id: "offer2", flights: [fixture.CreateFlight(flightId: "flight2")]),
        };
        
        // Act
        var packageFlight = fixture.CreatePackageFlight(flightOffers: flightOffers.ToArray());
        
        // Assert
        packageFlight.FlightOffers.Length.ShouldBe(2);
        packageFlight.FlightOffers.Select(o => o.Id).ShouldContain("offer1");
        packageFlight.FlightOffers.Select(o => o.Id).ShouldContain("offer2");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldContain("flight1");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldContain("flight2");
    }
    
    [Fact]
    public void Create_WithMultipleFlightOffersAndSamePricesForAllOccupancies_LimitsToThreeFlightOffers()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithAnyCurrencyConversion();

        var occupancy1 = new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0);
        var occupancy2 = new PackageOccupancy(adults: 2, youths: 0, children: 1, infants: 0);
        var occupancies = new[] { occupancy1, occupancy2 };
        
        // All flights have the same price for all occupancies
        var prices1 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 100M },
            { occupancy2, 150M }
        };
        
        var prices2 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 200M },
            { occupancy2, 250M }
        };
        
        var prices3 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 300M },
            { occupancy2, 350M }
        };
        
        var prices4 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 400M },
            { occupancy2, 450M }
        };
        
        var prices5 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 500M },
            { occupancy2, 550M }
        };
        
        var flightOffers = new List<FlightOffer>
        {
            fixture.CreateFlightOffer(id: "offer1", flights: [fixture.CreateFlight(flightId: "flight1", prices: prices1)]),
            fixture.CreateFlightOffer(id: "offer2", flights: [fixture.CreateFlight(flightId: "flight2", prices: prices2)]),
            fixture.CreateFlightOffer(id: "offer3", flights: [fixture.CreateFlight(flightId: "flight3", prices: prices3)]),
            fixture.CreateFlightOffer(id: "offer4", flights: [fixture.CreateFlight(flightId: "flight4", prices: prices4)]),
            fixture.CreateFlightOffer(id: "offer5", flights: [fixture.CreateFlight(flightId: "flight5", prices: prices5)])
        };
        
        // Create quotes for each flight
        var quotes = new List<FlightQuote>
        {
            fixture.CreateFlightQuote(flightId: "flight1", prices: prices1),
            fixture.CreateFlightQuote(flightId: "flight2", prices: prices2),
            fixture.CreateFlightQuote(flightId: "flight3", prices: prices3),
            fixture.CreateFlightQuote(flightId: "flight4", prices: prices4),
            fixture.CreateFlightQuote(flightId: "flight5", prices: prices5)
        };
        
        // Act
        var packageFlight = fixture.CreatePackageFlight(
            flightOffers: flightOffers.ToArray(),
            quotes: quotes.ToArray(),
            occupancies: occupancies);
        
        // Assert
        packageFlight.FlightOffers.Length.ShouldBe(3);
        
        // Should keep the 3 cheapest flight offers
        packageFlight.FlightOffers.Select(o => o.Id).ShouldContain("offer1");
        packageFlight.FlightOffers.Select(o => o.Id).ShouldContain("offer2");
        packageFlight.FlightOffers.Select(o => o.Id).ShouldContain("offer3");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldContain("flight1");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldContain("flight2");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldContain("flight3");
        
        // Should not include the more expensive flight offers
        packageFlight.FlightOffers.Select(o => o.Id).ShouldNotContain("offer4");
        packageFlight.FlightOffers.Select(o => o.Id).ShouldNotContain("offer5");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldNotContain("flight4");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldNotContain("flight5");
    }
    
    [Fact]
    public void Create_WithMultipleFlightOffersAndDifferentPricesByOccupancy_SelectsTopThreeForEachOccupancy()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithAnyCurrencyConversion();
            
        var occupancy1 = new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0);
        var occupancy2 = new PackageOccupancy(adults: 2, youths: 0, children: 1, infants: 0);
        var occupancies = new[] { occupancy1, occupancy2 };
        
        // Create prices with different rankings for each PackageOccupancy
        // Flight1: Cheapest for occupancy1, not in top 3 for occupancy2
        var prices1 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 100M },
            { occupancy2, 600M }
        };
        
        // Flight2: Cheapest for occupancy2, 2nd cheapest for occupancy1
        var prices2 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 200M },
            { occupancy2, 150M }
        };
        
        // Flight3: 3rd cheapest for occupancy1, 3rd cheapest for occupancy2
        var prices3 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 300M },
            { occupancy2, 350M }
        };
        
        // Flight4: Not in top 3 for occupancy1, 2nd cheapest for occupancy2
        var prices4 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 500M },
            { occupancy2, 250M }
        };
        
        // Flight5: Not in top 3 for either occupancy
        var prices5 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 600M },
            { occupancy2, 700M }
        };
        
        var flightOffers = new List<FlightOffer>
        {
            fixture.CreateFlightOffer(id: "offer1", flights: [fixture.CreateFlight(flightId: "flight1", prices: prices1)]),
            fixture.CreateFlightOffer(id: "offer2", flights: [fixture.CreateFlight(flightId: "flight2", prices: prices2)]),
            fixture.CreateFlightOffer(id: "offer3", flights: [fixture.CreateFlight(flightId: "flight3", prices: prices3)]),
            fixture.CreateFlightOffer(id: "offer4", flights: [fixture.CreateFlight(flightId: "flight4", prices: prices4)]),
            fixture.CreateFlightOffer(id: "offer5", flights: [fixture.CreateFlight(flightId: "flight5", prices: prices5)])
        };
        
        // Create quotes for each flight
        var quotes = new List<FlightQuote>
        {
            fixture.CreateFlightQuote(flightId: "flight1", prices: prices1),
            fixture.CreateFlightQuote(flightId: "flight2", prices: prices2),
            fixture.CreateFlightQuote(flightId: "flight3", prices: prices3),
            fixture.CreateFlightQuote(flightId: "flight4", prices: prices4),
            fixture.CreateFlightQuote(flightId: "flight5", prices: prices5)
        };
        
        // Act
        var packageFlight = fixture.CreatePackageFlight(
            flightOffers: flightOffers.ToArray(),
            quotes: quotes.ToArray(),
            occupancies: occupancies);
        
        // Assert
        // Should have 4 flight offers (top 3 for each occupancy with 1 overlap)
        packageFlight.FlightOffers.Length.ShouldBe(4);
        
        // For occupancy1: offer1, offer2, offer3
        packageFlight.FlightOffers.Select(o => o.Id).ShouldContain("offer1");
        packageFlight.FlightOffers.Select(o => o.Id).ShouldContain("offer2");
        packageFlight.FlightOffers.Select(o => o.Id).ShouldContain("offer3");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldContain("flight1");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldContain("flight2");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldContain("flight3");
        
        // For occupancy2: offer2, offer4, offer3
        packageFlight.FlightOffers.Select(o => o.Id).ShouldContain("offer4");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldContain("flight4");
        
        // Should not include offer5 (not in top 3 for either occupancy)
        packageFlight.FlightOffers.Select(o => o.Id).ShouldNotContain("offer5");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldNotContain("flight5");
    }
    
    [Fact]
    public void Create_WithMultipleFlightsAndSomeWithMissingPrices_OnlyConsidersFlightsWithPrices()
    {
        // Arrange
        var fixture = new PackageFlightTestFixture()
            .WithAnyCurrencyConversion();
            
        var occupancy1 = new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0);
        var occupancy2 = new PackageOccupancy(adults: 2, youths: 0, children: 1, infants: 0);
        var occupancies = new[] { occupancy1, occupancy2 };
        
        // Flight1: Has prices for both occupancies
        var prices1 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 100M },
            { occupancy2, 150M }
        };
        
        // Flight2: Has prices for both occupancies
        var prices2 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 200M },
            { occupancy2, 250M }
        };
        
        // Flight3: Has prices for both occupancies but expensive
        var prices3 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 300M },
            { occupancy2, 350M }
        };
        
        // Flight4: Only has price for occupancy1, cheaper than offer2
        var prices4 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy1, 150M }
        };
        
        // Flight5: Only has price for occupancy2, cheaper than offer2
        var prices5 = new Dictionary<PackageOccupancy, decimal>
        {
            { occupancy2, 200M }
        };
        
        var flightOffers = new List<FlightOffer>
        {
            fixture.CreateFlightOffer(id: "offer1", flights: [fixture.CreateFlight(flightId: "flight1", prices: prices1)]),
            fixture.CreateFlightOffer(id: "offer2", flights: [fixture.CreateFlight(flightId: "flight2", prices: prices2)]),
            fixture.CreateFlightOffer(id: "offer3", flights: [fixture.CreateFlight(flightId: "flight3", prices: prices3)]),
            fixture.CreateFlightOffer(id: "offer4", flights: [fixture.CreateFlight(flightId: "flight4", prices: prices4)]),
            fixture.CreateFlightOffer(id: "offer5", flights: [fixture.CreateFlight(flightId: "flight5", prices: prices5)])
        };
        
        // Create quotes for each flight
        var quotes = new List<FlightQuote>
        {
            fixture.CreateFlightQuote(flightId: "flight1", prices: prices1),
            fixture.CreateFlightQuote(flightId: "flight2", prices: prices2),
            fixture.CreateFlightQuote(flightId: "flight3", prices: prices3),
            fixture.CreateFlightQuote(flightId: "flight4", prices: prices4),
            fixture.CreateFlightQuote(flightId: "flight5", prices: prices5)
        };
        
        // Act
        var packageFlight = fixture.CreatePackageFlight(
            flightOffers: flightOffers.ToArray(),
            quotes: quotes.ToArray(),
            occupancies: occupancies);
        
        // Assert
        packageFlight.FlightOffers.Length.ShouldBe(4);
        
        // For occupancy1: offer1, offer4, offer2 (top 3)
        packageFlight.FlightOffers.Select(o => o.Id).ShouldContain("offer1");
        packageFlight.FlightOffers.Select(o => o.Id).ShouldContain("offer2");
        packageFlight.FlightOffers.Select(o => o.Id).ShouldContain("offer4");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldContain("flight1");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldContain("flight2");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldContain("flight4");
        
        // For occupancy2: offer1, offer5, offer2 (top 3)
        packageFlight.FlightOffers.Select(o => o.Id).ShouldContain("offer5");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldContain("flight5");
        
        // offer3 should NOT be included as it's not in the top 3 for either occupancy
        packageFlight.FlightOffers.Select(o => o.Id).ShouldNotContain("offer3");
        packageFlight.FlightOffers.Select(o => o.Flights[0].Id).ShouldNotContain("flight3");
    }
}
