using Esky.Packages.Common.Tests;
using Esky.Packages.Common.Tests.Fixtures.Domain;
using Esky.Packages.Domain.Model.LiveVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageVariants;

public class HotelOfferLiveVariantsTests
{
    private readonly HotelOfferVariantTestFixture _fixture;

    public HotelOfferLiveVariantsTests()
    {
        _fixture = new HotelOfferVariantTestFixture()
            .WithAnyCurrencyConversion();
    }

    [Fact]
    public void CreateCheapestHotelOfferVariantsByRooms_ReturnsAllOffersForCheapestRoom()
    {
        // Arrange
        var roomOffers1 = new Dictionary<string, List<HotelOfferLiveVariant>>
        {
            ["room1"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(150M, DefaultTestValues.Currency), offerId: "offer1"),
                _fixture.CreateHotelOfferVariant(price: new Money(100M, DefaultTestValues.Currency), offerId: "offer2"),
            ],
            ["room2"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(90M, DefaultTestValues.Currency), offerId: "offer3"),
                _fixture.CreateHotelOfferVariant(price: new Money(180M, DefaultTestValues.Currency), offerId: "offer4"),
            ]
        };
        var roomOffers2 = new Dictionary<string, List<HotelOfferLiveVariant>>
        {
            ["room3"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(200M, DefaultTestValues.Currency), offerId: "offer5"),
                _fixture.CreateHotelOfferVariant(price: new Money(100M, DefaultTestValues.Currency), offerId: "offer6"),
            ],
            ["room4"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(900M, DefaultTestValues.Currency), offerId: "offer7"),
                _fixture.CreateHotelOfferVariant(price: new Money(80M, DefaultTestValues.Currency), offerId: "offer8"),
            ]
        };
        var hotelOfferVariantsGroupedByRooms = new List<Dictionary<string, List<HotelOfferLiveVariant>>>
        {
            roomOffers1,
            roomOffers2
        };
        var onlyRefundable = false;

        // Act
        var hotelOfferVariants = HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms,
            onlyRefundable);

        // Assert
        hotelOfferVariants.HotelOffers.Count.ShouldBe(4);
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer3");
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer4");
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer7");
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer8");
        hotelOfferVariants.OnlyRefundable.ShouldBeFalse();
    }

    [Fact]
    public void CreateCheapestHotelOfferVariantsByRooms_WithOnlyRefundable_ReturnsCheapestRefundableOffers()
    {
        // Arrange
        var refundable = new Refundability(isRefundable: true);
        var nonRefundable = new Refundability(isRefundable: false);
        var onlyRefundable = true;
        // Arrange
        var roomOffers1 = new Dictionary<string, List<HotelOfferLiveVariant>>
        {
            ["room1"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(150M, DefaultTestValues.Currency), refundability: refundable, offerId: "offer1"),
                _fixture.CreateHotelOfferVariant(price: new Money(100M, DefaultTestValues.Currency), refundability: nonRefundable, offerId: "offer2"),
            ],
            ["room2"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(90M, DefaultTestValues.Currency), refundability: refundable, offerId: "offer3"),
                _fixture.CreateHotelOfferVariant(price: new Money(95M, DefaultTestValues.Currency), refundability: refundable, offerId: "offer4"),
                _fixture.CreateHotelOfferVariant(price: new Money(180M, DefaultTestValues.Currency), refundability: nonRefundable, offerId: "offer5"),
            ]
        };
        var roomOffers2 = new Dictionary<string, List<HotelOfferLiveVariant>>
        {
            ["room3"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(200M, DefaultTestValues.Currency), refundability: refundable, offerId: "offer6"),
                _fixture.CreateHotelOfferVariant(price: new Money(100M, DefaultTestValues.Currency), refundability: nonRefundable, offerId: "offer7"),
            ],
            ["room4"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(900M, DefaultTestValues.Currency), refundability: refundable, offerId: "offer8"),
                _fixture.CreateHotelOfferVariant(price: new Money(80M, DefaultTestValues.Currency), refundability: nonRefundable, offerId: "offer9"),
            ]
        };
        var hotelOfferVariantsGroupedByRooms = new List<Dictionary<string, List<HotelOfferLiveVariant>>>
        {
            roomOffers1,
            roomOffers2
        };

        // Act
        var hotelOfferVariants = HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms,
            onlyRefundable);

        // Assert
        hotelOfferVariants.HotelOffers.Count.ShouldBe(3);
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer3");
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer4");
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer6");
        hotelOfferVariants.OnlyRefundable.ShouldBeTrue();
    }

    [Fact]
    public void GetHotelOffersByMealPlan_GroupsOffersByMealPlanCorrectly()
    {
        // Arrange
        var roomOffers = new Dictionary<string, List<HotelOfferLiveVariant>>
        {
            ["room1"] =
            [
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.Breakfast, price: new Money(100M, DefaultTestValues.Currency), offerId: "offer1"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.AllInclusive, price: new Money(200M, DefaultTestValues.Currency), offerId: "offer2"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.Breakfast, price: new Money(150M, DefaultTestValues.Currency), offerId: "offer3")
            ]
        };
        var hotelOfferVariantsGroupedByRooms = new List<Dictionary<string, List<HotelOfferLiveVariant>>>
        {
            roomOffers
        };
        var hotelOfferVariants = HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms,
            onlyRefundable: false);

        // Act
        var groupedOffers = hotelOfferVariants.GetHotelOffersByMealPlan();

        // Assert
        groupedOffers.Count.ShouldBe(2);
        groupedOffers.ShouldContainKey(MealPlan.Breakfast);
        groupedOffers.ShouldContainKey(MealPlan.AllInclusive);

        groupedOffers[MealPlan.Breakfast].Count.ShouldBe(2);
        groupedOffers[MealPlan.AllInclusive].Count.ShouldBe(1);

        groupedOffers[MealPlan.Breakfast][0].Price.ShouldBe(100M);
        groupedOffers[MealPlan.Breakfast][1].Price.ShouldBe(150M);
    }
}
