using Esky.Packages.Common.Tests;
using Esky.Packages.Common.Tests.Fixtures.Domain;
using Esky.Packages.Domain.Model.LiveVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageVariants;

public class FlightLiveVariantsTests
{
    private readonly FlightVariantTestFixture _fixture;

    public FlightLiveVariantsTests()
    {
        _fixture = new FlightVariantTestFixture()
            .WithAnyCurrencyConversion();
    }

    [Fact]
    public void Create_WithDuplicateKeys_RemovesDuplicates()
    {
        // Arrange
        var flights = new List<FlightLiveVariant>
        {
            _fixture.CreateAlternativeFlight(key: "flight1", prices: [new Money(100M, DefaultTestValues.Currency)]),
            _fixture.CreateAlternativeFlight(key: "flight1", prices: [new Money(200M, DefaultTestValues.Currency)]),
            _fixture.CreateAlternativeFlight(key: "flight2", prices: [new Money(150M, DefaultTestValues.Currency)])
        };

        // Act
        var alternativeFlights = FlightLiveVariants.Create(flights);

        // Assert
        alternativeFlights.Flights.Count.ShouldBe(2);
        alternativeFlights.Flights.ShouldContain(f => f.Key == "flight1" && f.Price == 100M);
        alternativeFlights.Flights.ShouldContain(f => f.Key == "flight2" && f.Price == 150M);
    }

    [Fact]
    public void SelectAlternativeFlight_WithFlightOptionId_ReturnsSpecifiedFlight()
    {
        // Arrange
        var flights = new List<FlightLiveVariant>
        {
            _fixture.CreateAlternativeFlight(key: "flight1", prices: [new Money(100M, DefaultTestValues.Currency)]),
            _fixture.CreateAlternativeFlight(key: "flight2", prices: [new Money(200M, DefaultTestValues.Currency)])
        };
        var alternativeFlights = FlightLiveVariants.Create(flights);

        // Act
        var selectedFlight = alternativeFlights.SelectFlight(
            selectedDepartureAirports: null,
            selectedFlightOptionId: "flight2",
            preferredDepartureAirport: null);

        // Assert
        selectedFlight.ShouldNotBeNull();
        selectedFlight.Key.ShouldBe("flight2");
    }

    [Fact]
    public void SelectAlternativeFlight_WithPreferredDepartureAirport_ReturnsCheapestFlightFromThatAirport()
    {
        // Arrange
        var flights = new List<FlightLiveVariant>
        {
            _fixture.CreateAlternativeFlight(key: "flight1", departureAirport: "JFK", prices: [new Money(200M, DefaultTestValues.Currency)]),
            _fixture.CreateAlternativeFlight(key: "flight2", departureAirport: "LAX", prices: [new Money(150M, DefaultTestValues.Currency)]),
            _fixture.CreateAlternativeFlight(key: "flight3", departureAirport: "JFK", prices: [new Money(100M, DefaultTestValues.Currency)])
        };
        var alternativeFlights = FlightLiveVariants.Create(flights);

        // Act
        var selectedFlight = alternativeFlights.SelectFlight(
            selectedDepartureAirports: null,
            selectedFlightOptionId: null,
            preferredDepartureAirport: "JFK");

        // Assert
        selectedFlight.ShouldNotBeNull();
        selectedFlight.Key.ShouldBe("flight3");
        selectedFlight.Price.ShouldBe(100M);
    }

    [Fact]
    public void SelectAlternativeFlight_WithSelectedDepartureAirports_ReturnsCheapestFlightFromThoseAirports()
    {
        // Arrange
        var flights = new List<FlightLiveVariant>
        {
            _fixture.CreateAlternativeFlight(key: "flight1", departureAirport: "SEA", prices: [new Money(250M, DefaultTestValues.Currency)]),
            _fixture.CreateAlternativeFlight(key: "flight2", departureAirport: "LAX", prices: [new Money(150M, DefaultTestValues.Currency)]),
            _fixture.CreateAlternativeFlight(key: "flight3", departureAirport: "SFO", prices: [new Money(100M, DefaultTestValues.Currency)])
        };
        var alternativeFlights = FlightLiveVariants.Create(flights);

        // Act
        var selectedFlight = alternativeFlights.SelectFlight(
            selectedDepartureAirports: ["LAX", "SFO"],
            selectedFlightOptionId: null,
            preferredDepartureAirport: null);

        // Assert
        selectedFlight.ShouldNotBeNull();
        selectedFlight.Key.ShouldBe("flight3");
        selectedFlight.Price.ShouldBe(100M);
    }

    [Fact]
    public void SelectAlternativeFlight_NoCriteria_ReturnsCheapestFlight()
    {
        // Arrange
        var flights = new List<FlightLiveVariant>
        {
            _fixture.CreateAlternativeFlight(key: "flight1", prices: [new Money(300M, DefaultTestValues.Currency)]),
            _fixture.CreateAlternativeFlight(key: "flight2", prices: [new Money(200M, DefaultTestValues.Currency)]),
            _fixture.CreateAlternativeFlight(key: "flight3", prices: [new Money(100M, DefaultTestValues.Currency)])
        };
        var alternativeFlights = FlightLiveVariants.Create(flights);

        // Act
        var selectedFlight = alternativeFlights.SelectFlight(
            selectedDepartureAirports: null,
            selectedFlightOptionId: null,
            preferredDepartureAirport: null);

        // Assert
        selectedFlight.ShouldNotBeNull();
        selectedFlight.Key.ShouldBe("flight3");
        selectedFlight.Price.ShouldBe(100M);
    }

    [Fact]
    public void RemoveSoldOutFlight_RemovesFlightSuccessfully()
    {
        // Arrange
        var flightToRemove = _fixture.CreateAlternativeFlight(key: "flight1", prices: [new Money(100M, DefaultTestValues.Currency)]);
        var flights = new List<FlightLiveVariant>
        {
            flightToRemove,
            _fixture.CreateAlternativeFlight(key: "flight2", prices: [new Money(200M, DefaultTestValues.Currency)])
        };
        var alternativeFlights = FlightLiveVariants.Create(flights);

        // Act
        alternativeFlights.RemoveSoldOutFlight(flightToRemove);

        // Assert
        alternativeFlights.Flights.Count.ShouldBe(1);
        alternativeFlights.Flights.ShouldNotContain(f => f.Key == "flight1");
    }

    [Fact]
    public void RemoveSoldOutFlight_FlightNotInList_ThrowsException()
    {
        // Arrange
        var flights = new List<FlightLiveVariant>
        {
            _fixture.CreateAlternativeFlight(key: "flight1", prices: [new Money(100M, DefaultTestValues.Currency)])
        };
        var alternativeFlights = FlightLiveVariants.Create(flights);
        var notExistingFlight = _fixture.CreateAlternativeFlight(key: "flight2", prices: [new Money(200M, DefaultTestValues.Currency)]);

        // Act & Assert
        Should.Throw<InvalidOperationException>(() => alternativeFlights.RemoveSoldOutFlight(notExistingFlight));
    }

    [Fact]
    public void GetFlightsByDepartureAirport_GroupsFlightsCorrectly()
    {
        // Arrange
        var flights = new List<FlightLiveVariant>
        {
            _fixture.CreateAlternativeFlight(key: "flight1", departureAirport: "NYC", prices: [new Money(300M, DefaultTestValues.Currency)]),
            _fixture.CreateAlternativeFlight(key: "flight2", departureAirport: "LAX", prices: [new Money(200M, DefaultTestValues.Currency)]),
            _fixture.CreateAlternativeFlight(key: "flight3", departureAirport: "NYC", prices: [new Money(100M, DefaultTestValues.Currency)])
        };
        var alternativeFlights = FlightLiveVariants.Create(flights);

        // Act
        var groupedFlights = alternativeFlights.GetFlightsByDepartureAirport();

        // Assert
        groupedFlights.Count.ShouldBe(2);
        groupedFlights["NYC"].Count.ShouldBe(2);
        groupedFlights["LAX"].Count.ShouldBe(1);

        groupedFlights["NYC"][0].Price.ShouldBe(100M);
        groupedFlights["NYC"][1].Price.ShouldBe(300M);
    }
}
