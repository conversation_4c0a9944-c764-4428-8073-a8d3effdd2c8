using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Common.Tests;

public static class DefaultTestValues
{
    public static Airport DepartureAirport = new("LHR");
    public static Airport ArrivalAirport = new("MAD");
    public static PackageOccupancy PackageOccupancy = new(adults: 2, youths: 0, children: 0, infants: 0);
    public static Currency Currency = Currency.GBP;
    public static int StayLength = 7;
    public static MealPlan MealPlan = new("Breakfast");
    public static Refundability Refundability = new Refundability(isRefundable: true);
}
