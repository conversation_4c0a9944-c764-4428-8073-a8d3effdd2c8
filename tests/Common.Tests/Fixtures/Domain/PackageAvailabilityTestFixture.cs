using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageAvailabilities;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Common.Tests.Fixtures.Domain;

public static class PackageAvailabilityTestFixture
{
    public static PackageAvailability CreatePackageAvailability(
        int? metaCode = null,
        PackageOccupancy[]? occupancies = null,
        MealPlan[]? mealPlans = null,
        Airport[]? departureAirports = null,
        int[]? stayLengths = null,
        decimal? lowestTotalPrice = null,
        Dictionary<int, decimal>? lowestPricesPerStayLength = null,
        DateOnly? minCheckIn = null,
        DateOnly? maxCheckIn = null)
    {
        return new PackageAvailability
        {
            MetaCode = metaCode ?? 100,
            Occupancies = occupancies ?? [DefaultTestValues.PackageOccupancy],
            MealPlans = mealPlans ?? [DefaultTestValues.MealPlan],
            DepartureAirports = departureAirports ?? [DefaultTestValues.DepartureAirport],
            StayLengths = stayLengths ?? [7],
            LowestTotalPrice = lowestTotalPrice ?? 500m,
            LowestPricesPerStayLength = lowestPricesPerStayLength ?? new Dictionary<int, decimal> { { 7, 500m } },
            MinCheckIn = minCheckIn ?? DateOnly.FromDateTime(new DateTime(2025, 1, 1)),
            MaxCheckIn = maxCheckIn ?? DateOnly.FromDateTime(new DateTime(2025, 6, 1))
        };
    }
}
