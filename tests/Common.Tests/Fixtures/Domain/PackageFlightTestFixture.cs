using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Policies;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Common.Tests.Fixtures.Domain;

public class PackageFlightTestFixture
{
    private readonly ICurrencyConversionPolicy _currencyConversionPolicy;

    public PackageFlightTestFixture()
    {
        _currencyConversionPolicy = Substitute.For<ICurrencyConversionPolicy>();
    }

    public PackageFlightTestFixture WithAnyCurrencyConversion()
    {
        _currencyConversionPolicy
            .Convert(Arg.Any<decimal>(), Arg.Any<Currency>())
            .Returns(callInfo => callInfo.Arg<decimal>());

        return this;
    }

    public PackageFlightTestFixture WithCurrencyConversion(string fromCurrency, decimal currencyRate)
    {
        _currencyConversionPolicy
            .Convert(Arg.Any<decimal>(), Arg.Is<Currency>(currency => currency == fromCurrency))
            .Returns(callInfo => callInfo.Arg<decimal>() * currencyRate);

        return this;
    }

    public Flight CreateFlight(
        string? flightId = null,
        Dictionary<PackageOccupancy, decimal>? prices = null,
        DateTime? updatedAt = null)
    {
        return new Flight
        {
            Id = flightId ?? "flightId1",
            Prices = prices ?? new Dictionary<PackageOccupancy, decimal> { { DefaultTestValues.PackageOccupancy, 100M } },
            UpdatedAt = updatedAt ?? new DateTime(2025, 1, 1)
        };
    }

    public FlightOffer CreateFlightOffer(
        string? id = null,
        DateTime? departureDate = null,
        DateTime? returnArrivalDate = null,
        Flight[]? flights = null)
    {
        return new FlightOffer
        {
            Id = id ?? "id1",
            DepartureDate = departureDate ?? new DateTime(2025, 3, 24),
            ReturnDepartureDate = new DateTime(2025, 3, 31),
            ReturnArrivalDate = returnArrivalDate ?? new DateTime(2025, 3, 31),
            Flights = flights ?? [CreateFlight()]
        };
    }

    public FlightQuote CreateFlightQuote(
        string? flightId = null,
        DateTime? updateTime = null,
        Currency? currency = null,
        Dictionary<PackageOccupancy, decimal>? prices = null)
    {
        return new FlightQuote
        {
            FlightId = flightId ?? "flightId1",
            UpdateTime = updateTime ?? new DateTime(2025, 1, 2),
            Currency = currency ?? DefaultTestValues.Currency,
            Prices = prices ?? new Dictionary<PackageOccupancy, decimal> { { DefaultTestValues.PackageOccupancy, 100M } }
        };
    }

    public PackageFlight CreatePackageFlight(
        PackageFlightId? id = null,
        string? definitionId = null,
        string? partnerCode = null,
        Currency? currency = null,
        PackageOccupancy[]? occupancies = null,
        FlightOffer[]? flightOffers = null,
        FlightQuote[]? quotes = null,
        int[]? metaCodes = null)
    {
        return PackageFlight.Create(
            id: id ?? new PackageFlightId(
                checkIn: DateOnly.FromDateTime(new DateTime(2025, 3, 24)),
                stayLength: 7,
                marketId: "uk",
                arrivalAirport: new Airport("MAD"),
                departureAirport: new Airport("LHR")),
            definitionId: definitionId ?? "uk-es",
            partnerCode: partnerCode ?? "THOMASCOOKUKPACKAGES",
            currency: currency ?? DefaultTestValues.Currency,
            occupancies: occupancies ?? [DefaultTestValues.PackageOccupancy],
            flights: flightOffers ?? [CreateFlightOffer()],
            quotes: quotes ?? [CreateFlightQuote()],
            metaCodes: metaCodes ?? [100, 200],
            configure: pf => pf.ApplyPolicies(_currencyConversionPolicy));
    }
}
