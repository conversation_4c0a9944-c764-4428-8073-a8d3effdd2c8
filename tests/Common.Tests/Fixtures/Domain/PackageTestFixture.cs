using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Common.Tests.Fixtures.Domain;

public static class PackageTestFixture
{
    public static Package CreatePackage(
        PackageId? packageId = null,
        Dictionary<PackageOccupancy, Dictionary<MealPlan, decimal>>? hotelOfferPrices = null,
        Dictionary<Airport, Dictionary<Airport, Dictionary<PackageOccupancy, FlightPriceEntry>>>? flightPrices = null,
        DateTime? removeAt = null)
    {
        return new Package
        {
            Id = packageId ?? new PackageId(
                checkIn: DateOnly.FromDateTime(new DateTime(2025, 3, 24)),
                stayLength: 7,
                marketId: "pl",
                metaCode: 123),
            HotelOfferPrices = hotelOfferPrices ?? GetDefaultHotelOfferPrices(),
            FlightPricesByOccupancyByDepartureByArrival = flightPrices ?? GetDefaultFlightPrices(),
            RemoveAt = removeAt
        };
    }

    private static Dictionary<PackageOccupancy, Dictionary<MealPlan, decimal>> GetDefaultHotelOfferPrices()
    {
        return new Dictionary<PackageOccupancy, Dictionary<MealPlan, decimal>>
        {
            {
                DefaultTestValues.PackageOccupancy,
                new Dictionary<MealPlan, decimal>
                {
                    { MealPlan.Breakfast, 500M },
                    { MealPlan.HalfBoard, 700M }
                }
            }
        };
    }

    private static Dictionary<Airport, Dictionary<Airport, Dictionary<PackageOccupancy, FlightPriceEntry>>> GetDefaultFlightPrices()
    {
        return new Dictionary<Airport, Dictionary<Airport, Dictionary<PackageOccupancy, FlightPriceEntry>>>
        {
            {
                DefaultTestValues.ArrivalAirport,
                new Dictionary<Airport, Dictionary<PackageOccupancy, FlightPriceEntry>>
                {
                    {
                        DefaultTestValues.DepartureAirport,
                        new Dictionary<PackageOccupancy, FlightPriceEntry>
                        {
                            {
                                DefaultTestValues.PackageOccupancy,
                                new FlightPriceEntry
                                {
                                    Price = 300M,
                                    DepartureDate = DateOnly.FromDateTime(new DateTime(2025, 3, 24)),
                                    ReturnArrivalDate = DateOnly.FromDateTime(new DateTime(2025, 3, 31))
                                }
                            }
                        }
                    }
                }
            }
        };
    }
}
