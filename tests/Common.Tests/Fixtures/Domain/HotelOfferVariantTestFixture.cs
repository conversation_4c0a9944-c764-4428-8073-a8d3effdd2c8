using Esky.Packages.Domain.Model.LiveVariants;
using Esky.Packages.Domain.Policies;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Common.Tests.Fixtures.Domain;

public class HotelOfferVariantTestFixture
{
    private readonly ICurrencyConversionPolicy _currencyConversionPolicy;

    public HotelOfferVariantTestFixture()
    {
        _currencyConversionPolicy = Substitute.For<ICurrencyConversionPolicy>();
    }

    public HotelOfferVariantTestFixture WithAnyCurrencyConversion()
    {
        _currencyConversionPolicy
            .Convert(Arg.Any<decimal>(), Arg.Any<Currency>())
            .Returns(callInfo => callInfo.Arg<decimal>());

        return this;
    }

    public HotelOfferVariantTestFixture WithCurrencyConversion(Currency fromCurrency, decimal currencyRate)
    {
        _currencyConversionPolicy
            .Convert(Arg.Any<decimal>(), Arg.Is<Currency>(currency => currency.Equals(fromCurrency)))
            .Returns(callInfo => callInfo.Arg<decimal>() * currencyRate);

        return this;
    }

    public HotelOfferLiveVariant CreateHotelOfferVariant(
        string? offerId = null,
        MealPlan? mealPlan = null,
        Refundability? refundability = null,
        DateOnly? freeRefundUntil = null,
        Currency? currency = null,
        Money? price = null,
        Money? priceAtHotel = null,
        int? availability = null)
    {
        return HotelOfferLiveVariant.Create(
            offerId: offerId ?? "offer1",
            mealPlan: mealPlan ?? DefaultTestValues.MealPlan,
            refundability: refundability ?? DefaultTestValues.Refundability,
            freeRefundUntil: freeRefundUntil ?? DateOnly.FromDateTime(new DateTime(2025, 1, 1)),
            currency: currency ?? DefaultTestValues.Currency,
            price: price ?? new Money(500M, DefaultTestValues.Currency),
            priceAtHotel: priceAtHotel ?? new Money(1M, DefaultTestValues.Currency),
            availability: availability ?? 10,
            configure: hov => hov.ApplyPolicies(_currencyConversionPolicy));
    }
}
