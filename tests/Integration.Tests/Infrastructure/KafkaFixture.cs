using Confluent.Kafka.Admin;
using Confluent.Kafka;
using Testcontainers.Kafka;
using System.Text.Json;

namespace Esky.Packages.Integration.Tests.Infrastructure;

public class KafkaFixture : IAsyncDisposable
{
    public KafkaContainer KafkaContainer { get; private set; }

    public KafkaFixture()
    {
        KafkaContainer = new KafkaBuilder()
            .WithImage("confluentinc/cp-kafka:7.0.1")
            .WithPortBinding(9092, true)
            .Build();
    }

    public async Task InitializeAsync()
    {
        await KafkaContainer.StartAsync();

        await InitializeTopics();
    }

    private async Task InitializeTopics()
    {
        var adminConfig = new AdminClientConfig
        {
            BootstrapServers = KafkaContainer.GetBootstrapAddress()
        };

        using var adminClient = new AdminClientBuilder(adminConfig).Build();

        var topics = new[]
        {
            new TopicSpecification { Name = "flightQuotes", NumPartitions = 12, ReplicationFactor = 1 },
            new TopicSpecification { Name = "packageQuotes", NumPartitions = 12, ReplicationFactor = 1 },
            new TopicSpecification { Name = "packageVariants", NumPartitions = 12, ReplicationFactor = 1 },
            new TopicSpecification { Name = "packageFlightVariants", NumPartitions = 12, ReplicationFactor = 1 },
            new TopicSpecification { Name = "bloomFilterNotifications", NumPartitions = 12, ReplicationFactor = 1 },
            new TopicSpecification { Name = "repartitionedHotelOfferQuotes", NumPartitions = 12, ReplicationFactor = 1 },
            new TopicSpecification { Name = "repartitionedFlightQuotes", NumPartitions = 12, ReplicationFactor = 1 },
            new TopicSpecification { Name = "hotelOfferCacheQuotes", NumPartitions = 12, ReplicationFactor = 1 }
        };

        try
        {
            await adminClient.CreateTopicsAsync(topics);

            Console.WriteLine("Topics created successfully.");
        }
        catch (CreateTopicsException e)
        {
            Console.WriteLine($"Error creating topics: {e.Results[0].Error.Reason}");
        }
    }

    public string GetBootstrapServers() => KafkaContainer.GetBootstrapAddress();

    public void PublishEvent(string topicName, List<KafkaEvent> events)
    {
        foreach (var @event in events)
        {
            PublishEvent(topicName, @event);
        }
    }

    public void PublishEvent(string topicName, KafkaEvent @event)
    {
        var config = new ProducerConfig
        {
            BootstrapServers = GetBootstrapServers()
        };

        using var producer = new ProducerBuilder<string, byte[]>(config).Build();
        var messageBytes = JsonSerializer.SerializeToUtf8Bytes(@event.Message, InfrastructureBootstrapper.SerializerOptions);
            
        producer.Produce(topicName, new Message<string, byte[]>
        {
            Key = @event.Key,
            Value = messageBytes
        });
        
        producer.Flush(timeout: TimeSpan.FromSeconds(5));
    }

    public async ValueTask DisposeAsync()
    {
        await KafkaContainer.DisposeAsync();
    }

    public record KafkaEvent(string Key, object Message);
}
