using Esky.Packages.Infrastructure.Serialization.Converters;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Esky.Packages.Integration.Tests.Infrastructure;

public class InfrastructureBootstrapper : IAsyncDisposable
{
    private readonly MongoDbReplicaSetFixture _mongoFixture;
    private readonly RabbitMqFixture _rabbitFixture;
    private readonly KafkaFixture _kafkaFixture;
    private readonly SqlServerFixture _sqlServerFixture;

    public MongoDbReplicaSetFixture MongoDb => _mongoFixture;
    public RabbitMqFixture RabbitMq => _rabbitFixture;
    public KafkaFixture Kafka => _kafkaFixture;
    public SqlServerFixture SqlServer => _sqlServerFixture;

    public static JsonSerializerOptions SerializerOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
        Converters =
            {
                new JsonStringEnumConverter(JsonNamingPolicy.CamelCase),
                new PackageOccupancyJsonConverter(),
                new CurrencyJsonConverter(),
                new MealPlanJsonConverter(),
                new AirportJsonConverter(),
                new TimeOfDayJsonConverter(),
                new ProviderCodeJsonConverter()
            }
    };

    public InfrastructureBootstrapper()
    {
        _mongoFixture = new MongoDbReplicaSetFixture();
        _rabbitFixture = new RabbitMqFixture();
        _kafkaFixture = new KafkaFixture();
        _sqlServerFixture = new SqlServerFixture();
    }

    public async Task InitializeAsync()
    {
        await Task.WhenAll(
            _mongoFixture.InitializeAsync(), 
            _rabbitFixture.InitializeAsync(), 
            _kafkaFixture.InitializeAsync(),
            _sqlServerFixture.InitializeAsync());

        Console.WriteLine("MongoDb connection string: {0}", _mongoFixture.GetConnectionString());
        Console.WriteLine("RabbitMq connection string: {0}", _rabbitFixture.GetConnectionString());
        Console.WriteLine("Kafka connection string: {0}", _kafkaFixture.GetBootstrapServers());
        Console.WriteLine("SQL Server connection string: {0}", _sqlServerFixture.GetConnectionString());
    }

    public ConnectionStrings GetConnectionStrings()
    {
        return new ConnectionStrings(
            MongoDbConnectionString: _mongoFixture.GetConnectionString(),
            RabbitMqConnectionString: _rabbitFixture.GetConnectionString(),
            KafkaBootstrapServers: _kafkaFixture.GetBootstrapServers(),
            SqlServerConnectionString: _sqlServerFixture.GetConnectionString());
    }

    public async ValueTask DisposeAsync()
    {
        await Task.WhenAll(
            _mongoFixture.DisposeAsync().AsTask(), 
            _rabbitFixture.DisposeAsync().AsTask(), 
            _kafkaFixture.DisposeAsync().AsTask(),
            _sqlServerFixture.DisposeAsync().AsTask());
    }
} 