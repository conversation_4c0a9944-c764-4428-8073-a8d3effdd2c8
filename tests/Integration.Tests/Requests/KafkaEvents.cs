using Esky.Packages.Repricer.Events.Flights;
using static Esky.Packages.Integration.Tests.Infrastructure.KafkaFixture;

namespace Esky.Packages.Integration.Tests.Requests;

public static class KafkaEvents
{
    public static class FlightsQuotes
    {
        public static List<KafkaEvent> PriceChanges()
        {
            return
            [
                new KafkaEvent("LGWTFN250501237.5||IB1862|IB1589", FlightQuoteEvent(price: 1m)),
                new KafkaEvent("TFNLGW250504217.283||UX9045|UX1015", FlightQuoteEvent(price: 2m))
            ];
        }

        public static List<KafkaEvent> SoldOuts()
        {
            return
            [
                new KafkaEvent("LGWTFN250501237.5||IB1862|IB1589", FlightQuoteEvent(price: null)),
                new KafkaEvent("TFNLGW250504217.283||UX9045|UX1015", FlightQuoteEvent(price: null)),
                new KafkaEvent("LHRTFN250501178.4||VY6651|VY3214", FlightQuoteEvent(price: null)),
                new KafkaEvent("TFNLHR250504178I.4||VY3213|VY6652", FlightQuoteEvent(price: null)),
                new KafkaEvent("LGWTFS250501250504148.149||BY5458||BY4745", FlightQuoteEvent(price: null)),
                new KafkaEvent("LHRTFS250501250504138||LX317|LX8214||LH4381|LH924", FlightQuoteEvent(price: null)),
                new KafkaEvent("LHRTFN250501237.5||IB3647|IB1589", FlightQuoteEvent(price: null)),
                new KafkaEvent("TFNLHR250504178.4||VY3213|VY6652", FlightQuoteEvent(price: null)),
                new KafkaEvent("LGWTFS250501226.3||U28037", FlightQuoteEvent(price: null)),
                new KafkaEvent("TFSLGW250504226.3||U28054", FlightQuoteEvent(price: null)),
                new KafkaEvent("LHRTFS250501250504138||LX317|LX8214||LH4381|LH900", FlightQuoteEvent(price: null)),
                new KafkaEvent("LHRTFS250501211.46||EW9463|EW9558", FlightQuoteEvent(price: null)),
                new KafkaEvent("TFSLHR250504138||LH4381|LH924", FlightQuoteEvent(price: null)),
            ];
        }

        private static FlightQuoteEvent FlightQuoteEvent(decimal? price)
        {
            return new FlightQuoteEvent
            {
                RefreshDate = DateTime.UtcNow,
                Currency = "PLN",
                PaxConfigurations = new Dictionary<string, PaxPrice?>
                {
                    {
                        "2.0.0.0", !price.HasValue ? null : new PaxPrice { TotalPrice = price.Value }
                    }
                }
            };
        }
    }

    public static class HotelOfferQuote 
    {
        public static KafkaEvent PriceChanges()
        {
            return new KafkaEvent("250501:3:3713392:12|epaeac:A2", HotelOfferQuoteEvent(price: 3m));
        }

        public static KafkaEvent SoldOuts()
        {
            return new KafkaEvent("250501:3:3713392:12|epaeac:A2", HotelOfferQuoteEvent(price: null));
        }

        private static HotelOfferQuoteEvent HotelOfferQuoteEvent(decimal? price)
        {
            return new HotelOfferQuoteEvent
            {
                Id = "250501:3:3713392:12|epaeac:A2",
                CheckIn = new DateOnly(2025, 5, 1),
                MetaCode = 3713392,
                StayLength = 3,
                ProviderConfigurationId = "12|epaeac",
                Occupancy = "A2",
                Prices = !price.HasValue ? [] : new Dictionary<string, Dictionary<string, HotelOfferQuoteEvent.HotelOfferQuoteEventMoney>>
                {
                    {   "None", new Dictionary<string, HotelOfferQuoteEvent.HotelOfferQuoteEventMoney> 
                        {
                            {   "Refundable", new HotelOfferQuoteEvent.HotelOfferQuoteEventMoney { Value = price.Value, Currency = "PLN" } }
                        }
                    },
                    {   "Breakfast", new Dictionary<string, HotelOfferQuoteEvent.HotelOfferQuoteEventMoney>
                        {
                            {   "Refundable", new HotelOfferQuoteEvent.HotelOfferQuoteEventMoney { Value = price.Value, Currency = "PLN" } }
                        }
                    }
                }
            };
        }
    }
}
