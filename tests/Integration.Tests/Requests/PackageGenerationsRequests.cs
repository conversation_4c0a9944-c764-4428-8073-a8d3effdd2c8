using Esky.Packages.Contract.PackagesGeneration;
using Esky.Packages.Integration.Tests.Infrastructure;
using System.Text;
using System.Text.Json;

namespace Esky.Packages.Integration.Tests.Requests;

public static class PackageGenerationsRequests
{
    public static async Task<HttpResponseMessage> EnqueuePackageGenerations(HttpClient httpClient)
    {
        var command = new EnqueueGenerationsCommandDto
        {
            DefinitionsIds = ["pl-es"],
            Tags = [],
            Partitions = [],
            GenerationMode = GenerationModeDto.Full,
            StartedByUserId = "<EMAIL>"
        };

        return await httpClient.PostAsync(
            "/api/packageGenerations/enqueue",
            new StringContent(JsonSerializer.Serialize(command, InfrastructureBootstrapper.SerializerOptions), Encoding.UTF8, "application/json"));
    }
}
