using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Integration.Tests.Infrastructure;
using System.Text;
using System.Text.Json;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Integration.Tests.Requests;

public static class MarketsRequests
{
    public static async Task<HttpResponseMessage> AddMarket(HttpClient httpClient)
    {
        var market = Market.Create("pl", "PLN", "ESKYPLPACKAGES", ["LHR", "LGW"]);

        return await httpClient.PostAsync(
            "/api/markets/",
            new StringContent(JsonSerializer.Serialize(market, InfrastructureBootstrapper.SerializerOptions), 
                Encoding.UTF8, "application/json"));
    }
}
