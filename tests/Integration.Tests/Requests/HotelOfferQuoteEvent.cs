namespace Esky.Packages.Integration.Tests.Requests;

internal class HotelOfferQuoteEvent
{
    public required string Id { get; set; }
    public required DateOnly CheckIn { get; set; }
    public required int StayLength { get; set; }
    public required int MetaCode { get; set; }
    public required string ProviderConfigurationId { get; set; }
    public required string Occupancy { get; set; }
    public required Dictionary<string, Dictionary<string, HotelOfferQuoteEventMoney>> Prices { get; set; }

    public class HotelOfferQuoteEventMoney
    {
        public required decimal Value { get; init; }
        public required string Currency { get; init; }
    }
}