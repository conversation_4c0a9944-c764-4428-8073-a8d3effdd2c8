using Esky.Packages.Domain.Model.PackageDefinitions.FlightCriterias;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors.Locations;
using Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias.DurationStrategies;
using Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias;
using Esky.Packages.Domain.Model.PackageDefinitions;
using System.Text.Json;
using Esky.Packages.Integration.Tests.Infrastructure;
using System.Text;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors;

namespace Esky.Packages.Integration.Tests.Requests;

public static class PackageDefinitionsRequests
{
    public static async Task<HttpResponseMessage> AddPackageDefinition(HttpClient httpClient)
    {
        var definition = new PackageDefinition
        {
            Id = "pl-es",
            Metadata = new PackageDefinitionMetadata
            {
                Description = "Spain test: Mallorca, Tenerife, Barcelona",
                Name = "pl-test",
                Notes = "",
                Tags = ["local-pl-test"]
            },
            Parameters = new PackageDefinitionParameters
            {
                MarketId = "pl",
                PartnerCode = "ESKYPLPACKAGES",
                FlightCriteria = new FlightCriteria
                {
                    DepartureAirportCodes = ["LHR", "LGW"],
                    AirlineCodes = [],
                    MaxStop = 1,
                    MaxLegDuration = TimeSpan.Parse("18:00:00")
                },
                HotelSelector = new HotelMetaCodeSelector(626617, 127416, 4654964, 846838, 3713392),
                MergeIntoPackageHotelSelector = new HotelNullSelector(),
                TimeCriteria = new TimeCriteria
                {
                    DurationStrategy = new RollingCheckInDurationStrategy
                    {
                        MinCheckInDaysFromGeneration = 10,
                        MaxCheckInDaysFromGeneration = 11,
                        StayLengths = [3]
                    }
                },
                Occupancies = [new PackageOccupancy(adults: 2, youths: 0, children: 0, infants: 0)]
            }
        };

        return await httpClient.PostAsync(
            "/api/packageDefinitions/",
            new StringContent(JsonSerializer.Serialize(definition, InfrastructureBootstrapper.SerializerOptions), Encoding.UTF8, "application/json"));
    }
}
