using Esky.Packages.Infrastructure.HttpClients.Constants;
using Esky.Packages.Integration.Tests.Infrastructure;
using Esky.Packages.Integration.Tests.TestData;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.Packages.Integration.Tests.Services;

public class ApiFactory(ConnectionStrings connectionStrings) 
    : BaseServiceFactory<Api.Program>()
{
    protected override Dictionary<string, string> GetConfigurationValues()
    {
        return new Dictionary<string, string>
        {
            ["Mongo:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["MongoSearch:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["RabbitMq:Host"] = connectionStrings.RabbitMqConnectionString
        };
    }

    protected override void ConfigureServices(IServiceCollection services)
    {
        MockFlightCacheGateway(services);
        MockFlightLiveGateway(services);
        MockHotelTransactionGateway(services);
    }

    private static void MockFlightCacheGateway(IServiceCollection services)
    {
        const string Path = "TestData.Gateways.FlightCache.AlternativeFlights.Response.json";
        var response = TestDataReader.ReadResource(Path);

        services.AddHttpClient(ApiHttpClientConsts.FlightCacheClient)
            .ConfigurePrimaryHttpMessageHandler(() => new MockHttpMessageHandler(response));
    }

    private static void MockFlightLiveGateway(IServiceCollection services)
    {
        const string Path = "TestData.Gateways.FlightLive.Response.json";
        var response = TestDataReader.ReadResource(Path);

        services.AddHttpClient(ApiHttpClientConsts.FlightLiveClient)
            .ConfigurePrimaryHttpMessageHandler(() => new MockHttpMessageHandler(response));
    }

    private static void MockHotelTransactionGateway(IServiceCollection services)
    {
        const string Path = "TestData.Gateways.HotelTransaction.Response.json";
        var response = TestDataReader.ReadResource(Path);

        services.AddHttpClient(ApiHttpClientConsts.HotelTransactionClient)
            .ConfigurePrimaryHttpMessageHandler(() => new MockHttpMessageHandler(response));
    }
}
