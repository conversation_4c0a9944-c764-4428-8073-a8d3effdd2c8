using Esky.Packages.Integration.Tests.Infrastructure;

namespace Esky.Packages.Integration.Tests.Services;

public class ServicesBootstrapper(InfrastructureBootstrapper infrastructure) : IAsyncDisposable
{
    private readonly InfrastructureBootstrapper _infrastructure = infrastructure;
    private HttpClient? _apiClient;
    private HttpClient? _generatorClient;
    private HttpClient? _flightVariantorClient;
    private HttpClient? _flightOplogReaderClient;
    private HttpClient? _hotelOfferOplogReaderClient;
    private HttpClient? _mergerClient;
    private HttpClient? _repartitionerClient;
    private HttpClient? _repricerClient;

    public HttpClient ApiClient => _apiClient ?? throw new InvalidOperationException("ApiClient not initialized");
    public HttpClient GeneratorClient => _generatorClient ?? throw new InvalidOperationException("GeneratorClient not initialized");
    public HttpClient FlightVariantorClient => _flightVariantorClient ?? throw new InvalidOperationException("FlightVariantorClient not initialized");
    public HttpClient FlightOplogReaderClient => _flightOplogReaderClient ?? throw new InvalidOperationException("FlightOplogReaderClient not initialized");
    public HttpClient HotelOfferOplogReaderClient => _hotelOfferOplogReaderClient ?? throw new InvalidOperationException("HotelOfferOplogReaderClient not initialized");
    public HttpClient MergerClient => _mergerClient ?? throw new InvalidOperationException("MergerClient not initialized");
    public HttpClient RepartitionerClient => _repartitionerClient ?? throw new InvalidOperationException("RepartitionerClient not initialized");
    public HttpClient RepricerClient => _repricerClient ?? throw new InvalidOperationException("RepricerClient not initialized");

    public Task InitializeAsync()
    {
        var connectionStrings = _infrastructure.GetConnectionStrings();

        var apiFactory = new ApiFactory(connectionStrings);
        var generatorFactory = new GeneratorFactory(connectionStrings);
        var flightVariantorFactory = new FlightVariantorFactory(connectionStrings);
        var flightOplogReaderFactory = new FlightOplogReaderFactory(connectionStrings);
        var hotelOfferOplogReaderFactory = new HotelOfferOplogReaderFactory(connectionStrings);
        var mergerFactory = new MergerFactory(connectionStrings);
        var repartitionerFactory = new RepartitionerFactory(connectionStrings);
        var repricerFactory = new RepricerFactory(connectionStrings);

        _apiClient = apiFactory.CreateClient();
        _generatorClient = generatorFactory.CreateClient();
        _flightVariantorClient = flightVariantorFactory.CreateClient();
        _flightOplogReaderClient = flightOplogReaderFactory.CreateClient();
        _hotelOfferOplogReaderClient = hotelOfferOplogReaderFactory.CreateClient();
        _mergerClient = mergerFactory.CreateClient();
        _repartitionerClient = repartitionerFactory.CreateClient();
        _repricerClient = repricerFactory.CreateClient();

        return Task.CompletedTask;
    }

    public ValueTask DisposeAsync()
    {
        _apiClient?.Dispose();
        _generatorClient?.Dispose();
        _flightVariantorClient?.Dispose();
        _flightOplogReaderClient?.Dispose();
        _hotelOfferOplogReaderClient?.Dispose();
        _mergerClient?.Dispose();
        _repartitionerClient?.Dispose();
        _repricerClient?.Dispose();

        return ValueTask.CompletedTask;
    }
} 