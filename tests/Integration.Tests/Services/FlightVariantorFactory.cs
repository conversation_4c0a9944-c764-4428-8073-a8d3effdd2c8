using Esky.Packages.Integration.Tests.Infrastructure;

namespace Esky.Packages.Integration.Tests.Services;

public class FlightVariantorFactory(ConnectionStrings connectionStrings) 
    : BaseServiceFactory<FlightVariantor.Program>()
{
    protected override Dictionary<string, string> GetConfigurationValues()
    {
        return new Dictionary<string, string>
        {
            ["Mongo:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["MongoSearch:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["FlightVariantConsumer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers
        };
    }
}
