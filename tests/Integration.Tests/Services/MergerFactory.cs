using Esky.Packages.Integration.Tests.Infrastructure;

namespace Esky.Packages.Integration.Tests.Services;

public class MergerFactory(ConnectionStrings connectionStrings) 
    : BaseServiceFactory<Merger.Program>()
{
    protected override Dictionary<string, string> GetConfigurationValues()
    {
        return new Dictionary<string, string>
        {
            ["Mongo:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["MongoSearch:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["PackageQuoteConsumer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers
        };
    }
}