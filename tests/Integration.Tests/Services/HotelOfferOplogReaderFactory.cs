using Esky.Packages.Integration.Tests.Infrastructure;

namespace Esky.Packages.Integration.Tests.Services;

public class HotelOfferOplogReaderFactory(ConnectionStrings connectionStrings) 
    : BaseServiceFactory<HotelOfferOplogReader.Program>()
{
    protected override Dictionary<string, string> GetConfigurationValues()
    {
        return new Dictionary<string, string>
        {
            ["Mongo:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["MongoSearch:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["PackageHotelOfferProducer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers,
            ["HotelGateway:ConnectionString"] = connectionStrings.SqlServerConnectionString
        };
    }
}