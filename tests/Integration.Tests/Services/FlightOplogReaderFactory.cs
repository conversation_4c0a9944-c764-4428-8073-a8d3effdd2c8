using Esky.Packages.Integration.Tests.Infrastructure;

namespace Esky.Packages.Integration.Tests.Services;

public class FlightOplogReaderFactory(ConnectionStrings connectionStrings) 
    : BaseServiceFactory<FlightOplogReader.Program>()
{
    protected override Dictionary<string, string> GetConfigurationValues()
    {
        return new Dictionary<string, string>
        {
            ["Mongo:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["MongoSearch:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["PackageFlightProducer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers
        };
    }
}