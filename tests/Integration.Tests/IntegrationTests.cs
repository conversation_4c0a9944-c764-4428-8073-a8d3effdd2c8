using Esky.Packages.Integration.Tests.Infrastructure;
using Esky.Packages.Integration.Tests.Services;
using Esky.Packages.Integration.Tests.Requests;
using Esky.Packages.Contract.Packages;
using Esky.Packages.Contract.PackageVariants;
using Esky.Packages.Integration.Tests.TestData;
using Esky.Packages.Contract.Common;

namespace Esky.Packages.Integration.Tests;

public class IntegrationTests : IAsyncLifetime
{
    private static readonly TimeSpan DefaultTimeout = TimeSpan.FromSeconds(60);
    private readonly InfrastructureBootstrapper _infrastructure;
    private readonly ServicesBootstrapper _services;

    public IntegrationTests()
    {
        _infrastructure = new InfrastructureBootstrapper();
        _services = new ServicesBootstrapper(_infrastructure);
    }

    public async Task InitializeAsync()
    {
        await _infrastructure.InitializeAsync();
        await _services.InitializeAsync();
    }

    public async Task DisposeAsync()
    {
        await _services.DisposeAsync();
        await _infrastructure.DisposeAsync();
    }

    [Fact]
    public async Task TestEndToEnd()
    {
        _ = await MarketsRequests.AddMarket(_services.ApiClient);
        _ = await PackageDefinitionsRequests.AddPackageDefinition(_services.ApiClient);
        _ = await PackageGenerationsRequests.EnqueuePackageGenerations(_services.ApiClient);

        // Package after generation
        var package = await PackagesRequests.WaitForPackage(
            httpClient: _services.ApiClient,
            predicate: package => package is not null && 
                       package.FlightOffers.Length > 0 && 
                       package.HotelOffers.Length > 0,
            timeout: DefaultTimeout);
        var expectedPackage = TestDataReader.ReadResource<PackageDto>("TestData.Results.Package.json");
        package.ShouldBeEquivalentTo(expectedPackage);

        // Package live variants
        var packageLiveVariants = await PackagesRequests.GetPackageLiveVariants(_services.ApiClient);
        var expectedPackageLiveVariants = TestDataReader.ReadResource<PackageLiveVariantsDto>("TestData.Results.PackageLiveVariants.json");
        packageLiveVariants.ShouldBeEquivalentTo(expectedPackageLiveVariants);

        // Package after hotel and flight price changes
        _infrastructure.Kafka.PublishEvent("flightQuotes", KafkaEvents.FlightsQuotes.PriceChanges());
        _infrastructure.Kafka.PublishEvent("hotelOfferCacheQuotes", KafkaEvents.HotelOfferQuote.PriceChanges());

        package = await PackagesRequests.WaitForPackage(
            httpClient: _services.ApiClient,
            predicate: package => package is not null &&
                       package.FlightOffers.First().Prices.First().Price.Price is 3m &&
                       package.HotelOffers.First().Prices.First().Value is 3m,
            timeout: DefaultTimeout);
        expectedPackage = TestDataReader.ReadResource<PackageDto>("TestData.Results.Package_PriceChanges.json");
        package.ShouldBeEquivalentTo(expectedPackage);

        // Package after hotel and flight unavailability should not exists anymore
        _infrastructure.Kafka.PublishEvent("flightQuotes", KafkaEvents.FlightsQuotes.SoldOuts());
        _infrastructure.Kafka.PublishEvent("hotelOfferCacheQuotes", KafkaEvents.HotelOfferQuote.SoldOuts());

        package = await PackagesRequests.WaitForPackage(
            httpClient: _services.ApiClient,
            predicate: package => package is null,
            timeout: DefaultTimeout);
        package.ShouldBeNull();
    }

    [Fact]
    public async Task TestPackageLiveVariantsWithoutHotelOffersAndFlights()
    {
        _ = await MarketsRequests.AddMarket(_services.ApiClient);

        var packageLiveVariants = await PackagesRequests.GetPackageLiveVariants(
            _services.ApiClient,
            "250501:3:pl:3713392",
            new GetPackageLiveVariantsQueryDto(
                Occupancy: new OccupancyDto(2, []),
                Occupancies: null,
                DepartureAirports: null,
                FlightOptionId: null,
                InboundDepartures: null,
                OutboundDepartures: null,
                PreferredDepartureAirport: null));
        
        var expectedPackageLiveVariants = TestDataReader.ReadResource<PackageLiveVariantsDto>("TestData.Results.PackageLiveVariantsWithoutHotelOffersAndFlights.json");
        packageLiveVariants.ShouldBeEquivalentTo(expectedPackageLiveVariants);
    }
}